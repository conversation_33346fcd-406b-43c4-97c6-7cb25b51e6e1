<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Azure SQL Server Stretch Database, 价格详情, 定价, 计费" name="keywords"/>
  <meta content="了解 Azure SQL Server Stretch Database 的价格详情。不同的性能级别对应不同的价格，用户可根据需求选择适用的级别。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   SQL Server Stretch Database定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/sql-server-stretch-database/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="sql-server-stretch-database" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/sql-server-stretch%20database.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           SQL
           <small>
            Server Stretch Database
           </small>
          </h2>
          <h4>
           将本地 SQL Server 数据库动态拉伸到 Azure
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         借助 SQL Server Stretch Database，您可以将 Windows SQL Server 中温或冷的事务数据动态地拉伸到 Azure。与传统的冷数据管理解决方案不同，您存储在 Stretch Database 中的数据将随时可用。借助 Stretch Database，您无需支付太多费用即可为大量数据提供更长存储期限。您可以根据性能需求选择性能级别，并可按需扩大或缩小。
        </p>
        <p>
         Stretch Database 对计算和存储是分别计费的，因此您只需要为实际使用的资源付费。计算资源的用量是通过数据库拉伸单位（Database Stretch Unit，DSU）衡量的，客户可以随时按需扩大或缩小性能/DSU 级别。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <div class="scroll-table" style="display: block;">
          <h2>
           定价详细信息
          </h2>
          <p>
           SQL Server Stretch Database 已正式发布。
          </p>
          <div class="tags-date">
           <div class="ms-date">
            *以下价格均为含税价格。
           </div>
           <br/>
           <div class="ms-date">
            *每月价格估算基于每个月 744 小时的使用量。
           </div>
          </div>
          <table cellpadding="0" cellspacing="0" width="100%">
           <tbody>
            <tr>
             <th align="left">
              <strong>
               性能级别（DSU）
              </strong>
             </th>
             <th align="left">
              <strong>
               标准价格（人民币）
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              100
             </td>
             <td>
              <span>
               ¥ 22.51/小时（~¥ 16,747.44/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              200
             </td>
             <td>
              <span>
               ¥ 45.02/小时（~¥ 33,494.88/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              300
             </td>
             <td>
              <span>
               ¥ 67.53/小时（~¥ 50,242.32/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              400
             </td>
             <td>
              <span>
               ¥ 90.04/小时（~¥ 66,989.76/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              500
             </td>
             <td>
              <span>
               ¥ 112.55/小时（~¥ 83,737.20/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              600
             </td>
             <td>
              <span>
               ¥ 135.06/小时（~¥ 100,484.64/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              1000
             </td>
             <td>
              <span>
               ¥ 225.1/小时（~¥ 167,474.40/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              1200
             </td>
             <td>
              <span>
               ¥ 270.12/小时（~¥ 220,969.28/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              1500
             </td>
             <td>
              <span>
               ¥ 337.65/小时（~¥ 251,211.60/月）
              </span>
             </td>
            </tr>
            <tr>
             <td>
              2000
             </td>
             <td>
              <span>
               ¥ 450.2/小时（~¥ 334,948.80/月）
              </span>
             </td>
            </tr>
           </tbody>
          </table>
         </div>
         <!--    <div class="tags-date">
                        <div class="ms-date">预览阶段，数据库大小被限制为 60TB。当 Stretch Database 正式上市后，数据库体积显示将有所扩大。</div><br />
                    </div>
                    -->
         <!-- END: Table1-Content-->
         <!-- BEGIN: Table2-Content-->
         <h3>
          出站数据传输
         </h3>
         <p>
          按照正常
          <a href="../data-transfer/index.html" id="/pricing/details/data-transfer/">
           数据传输
          </a>
          费率对出站数据传输收费。
         </p>
         <h3>
          数据存储和快照
         </h3>
         <p>
          数据存储收费基准为￥1.441/GB/小时。数据存储包括 Stretch Database 和备份快照的大小。所有 Stretch Database 都有 7 天的增量备份快照。
         </p>
         <p>
          注意：存储事务不收费。您只需为存储的数据支付费用，无需支付存储事务的费用。
         </p>
         <h3>
          异地备份
         </h3>
         <p>
          您还可选择对 Stretch Database 进行异地冗余备份，以实现灾难恢复。异地冗余副本的存储按￥1.056/GB/月的 Azure 标准磁盘 RA-GRS
          <a href="../storage/index.html" id="/pricing/details/storage/-1">
           费率
          </a>
          收费。
         </p>
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_compatible">
             哪些版本的 SQL Server 可兼容 Stretch Database？
            </a>
            <section>
             <p>
              Stretch Database 是 SQL Server 2016 的新功能。该功能可用于所有版本的 SQL Server 2016。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_unit">
             DSU（Stretch Database 单位）是什么？
            </a>
            <section>
             <p>
              为了继续向客户提供更加可预测的性能体验，SQL Stretch Database 中引入了 Stretch Database 单位（DSU）这一概念。DSU 代表查询和数据迁移的性能，可通过工作负载的这些目标进行量化：将数据行迁移至 Azure，以及写入、读取，和计算时的速度快慢。这一指标可以帮助客户评估自己的工作负载所需要的相对性能程度。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_amount">
             Stretch Database 的用量是如何体现在账单中的？
            </a>
            <section>
             <p>
              您的账单中会包含可预测的存储月费，外加不同额度的计算费用。取决于所使用的计算资源（DSU）数量，每个月的计算费用可能各不相同。为了让您更清晰地看到所消耗的计算资源总量，存储和计算费用会分别体现在账单中。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_bill">
             如果我使用 Stretch Database 的时间不足一小时会如何计费？
            </a>
            <section>
             <p>
              无论数据库是否活跃，或数据库存在时间是否达到一小时，您都需要为 Stretch Database 存在的每小时付费，并且会按照这一小时内所适用的最高费率付费。举例来说，如果您创建了一个 Stretch Database，并在 5 分钟后将其删除，您的这个数据库将按照 1 小时收费。如果您创建了一个包含 100 DSU 的 Stretch Database，随后立刻将其扩展至 400 个 DSU，那么您的第一个小时将按照 400 DSU 收费。如果您删除了数据库，随后又创建了同名数据库，您的账单中将分别体现这两个独立数据库各自 1 小时的费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_dsu">
             如果我在同一个小时里使用不同级别的 DSU，该如何计费？
            </a>
            <section>
             <p>
              DSU 将按“小时”计费，并会使用这个小时内所适用的最高级别性能来计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_performance">
             我能否更改 Stretch Database 的性能级别？
            </a>
            <section>
             <p>
              您可以调整 Stretch Database 的性能级别。性能级别的更改将在几分钟后生效，更改过程中，您的数据库将维持在线可用状态。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_data">
             我可以将多少数据拉伸到 Azure 中？
            </a>
            <section>
             <p>
              在预览阶段，您的 Stretch Database 最多可包含 60TB 数据。在该服务正式上市前后，我们将确定最终的实际尺寸上限。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_withdraw">
             如何撤销数据库的拉伸？
            </a>
            <section>
             <p>
              您可以随时将数据重新移动回内部部署环境。如果打算退订 Stretch Database，为了能继续访问这些数据，您必须通过撤销拉伸（un-stretching）操作将数据移动回内部部署环境，或将数据导出到存储服务中。有关撤销拉伸或导出数据的详细信息，请参阅 SQL Server 2016 StretchDatabase 文档。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_consume">
             我的数据库会消耗多少存储容量？
            </a>
            <section>
             <p>
              数据在 Stretch Database 中消耗的容量，与相同数据在移动到 Stretch Database之前，位于 SQL Server 中的时候消耗大致相同的存储容量。Stretch Database 顾问工具可以帮您估算存储需求。有关工作负载顾问工具使用方法的详细信息，请参阅相关文档。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_azuresql">
             我能否将Stretch Database与 Azure SQL 数据库配合使用？
            </a>
            <section>
             <p>
              Stretch Database 目前不兼容配合 Azure SQL 数据库使用。您不能将 Azure SQL 数据库拉伸至
             </p>
             <p>
              Stretch Database，也不能将 SQL Server 表或数据库拉伸至 Azure SQL 数据库。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_sqlserver">
             如果我有一个来自 SQL Server 2016 RC0 或更早版本的现有 Stretch Database，能否直接升级到 RC1 或更新的版本，并在新的 Stretch Database 服务上继续运行？
            </a>
            <section>
             <p>
              目前不支持直接升级。如果想要保留已经拉伸到 Azure 的数据，你需要首先将 Azure 中的数据回迁 (Un-Migrate) 至自己的 SQL Server 数据库。回迁完成后，即可升级并重新拉伸至新的Stretch Database服务。请注意，将数据从 Azure 回迁会产生数据传出费用。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_sql-stretch_question_sql-server-rco">
             目前我在使用 SQL Server 2016 RC0 ，在对数据库进行拉伸后，Azure 门户将其显示为一个 SQL DB S3。我该如何拉伸至 SQL Stretch Database？
            </a>
            <section>
             <p>
              SQL Stretch Database 服务目前处于有限公开预览阶段，客户需要注册才能访问预览服务。若要访问该服务请在【这里】注册。若不注册，RC0 用户默认将拉伸至 SQL DB S3。从 RC1 开始，用户将只能拉伸至 SQL Stretch Database 服务。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>SQL Server Stretch Database 服务在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table> -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="sql-server-stretch-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证，至少 99.9% 的时间内客户在其 SQL Server 伸展数据库和我们的 Internet 网关之间存在连接。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/legal/sla/index.html" id="pricing_sql-server-stretch_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="sOcBEa3zeaUBIxzULTQZ_IOoW997OD2ro58tCsBGxzbxUSVi47e5mIfGKfTNhuEqAQzBpUn5069CMsUzWljg-TClia2yPFwaLW0JTKglluo1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
