<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta content="Azure, 密钥保管库, 价格" name="keywords" />
    <meta
        content="了解 Azure 密钥保管库（Key Vault）价格详情。通过 Azure 密钥保管库（Key Vault），Azure 用户能够保护和控制云应用程序和服务使用的加密密钥和其他机密。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。"
        name="description" />
    <title>
        密钥保管库 - Azure云计算
    </title>
    <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/key-vault/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="key-vault" wacn.date="11/27/2015">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/key-vault.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="/Images/marketing-resource/media/images/production/<EMAIL>" />
                                    <h2>
                                        密钥保管库
                                        <span>
                                            Key Vault
                                        </span>
                                    </h2>
                                    <h4>
                                        保护并保持控制密钥和机密
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <p>
                                使用 Azure Key Vault，Azure 订户能够保护并控制云应用程序和服务使用的加密密钥及其他机密。Azure Key Vault 提供两种类型的容器：
                            </p>
                            <ul>
                                <li>
                                    用于存储和管理加密密钥、机密、证书和存储帐户密钥的保管库。
                                </li>
                                <li>
                                    用于存储和托管 HSM 支持的加密密钥的托管 HSM 池。
                                </li>
                            </ul>
                        </div>
                        <!-- BEGIN: TAB-CONTROL -->
                        <h2>
                            定价详细信息
                        </h2>
                        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                            <div class="tab-container-container">
                                <div class="tab-container-box">
                                    <div class="tab-container">
                                        <div class="dropdown-container software-kind-container" style="display:none">
                                            <label>
                                                OS/软件:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    Key Vault
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#tabContent1" href="javascript:void(0)"
                                                            id="home_key-vault">
                                                            Key Vault
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select software-box hidden-lg hidden-md"
                                                id="software-box">
                                                <option data-href="#tabContent1" selected="selected" value="Key Vault">
                                                    Key Vault
                                                </option>
                                            </select>
                                        </div>
                                        <div class="dropdown-container region-container">
                                            <label>
                                                地区:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    中国东部 3
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#east-china3" href="javascript:void(0)"
                                                            id="east-china3">
                                                            中国东部 3
                                                        </a>
                                                    </li>
                                                    <li class="active">
                                                        <a data-href="#north-china3" href="javascript:void(0)"
                                                            id="north-china3">
                                                            中国北部 3
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#east-china2" href="javascript:void(0)"
                                                            id="east-china2">
                                                            中国东部 2
                                                        </a>
                                                    <li>
                                                        <a data-href="#north-china2" href="javascript:void(0)"
                                                            id="north-china2">
                                                            中国北部 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#east-china" href="javascript:void(0)"
                                                            id="east-china">
                                                            中国东部
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china" href="javascript:void(0)"
                                                            id="north-china">
                                                            中国北部
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select region-box hidden-lg hidden-md"
                                                id="region-box">
                                                <option data-href="#east-china3" selected="selected"
                                                    value="east-china3">
                                                    中国东部 3
                                                </option>
                                                <option data-href="#north-china3" value="north-china3">
                                                    中国北部 3
                                                </option>
                                                <option data-href="#east-china2" value="east-china2">
                                                    中国东部 2
                                                </option>
                                                <option data-href="#north-china2" value="north-china2">
                                                    中国北部 2
                                                </option>
                                                <option data-href="#east-china" value="east-china">
                                                    中国东部
                                                </option>
                                                <option data-href="#north-china" value="north-china">
                                                    中国北部
                                                </option>
                                            </select>
                                        </div>
                                        <div class="clearfix">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-content">
                                <div class="tab-panel" id="tabContent1">

                            <!-- BEGIN: Table1-Content-->
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                *以下价格均为含税价格。
                                            </div>
                                            <br/>
                                            <div class="ms-date">
                                                *每月价格估算基于每个月 744 小时的使用量。
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-control-container tab-active">
                                        <h3>
                                            保管库
                                        </h3>
                                        <p>保管库提供两个服务级别 - 标准和高级。</p>
                                        
                                        <table cellpadding="0" cellspacing="0" width="100%" id="keyvault-vaults-N2E2N3E3-table">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        标准
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        高级
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    机密和软件保护的密钥
                                                </td>
                                                <td>
                                                    ￥0.305/10,000 次操作
                                                </td>
                                                <td>
                                                    ￥0.305/10,000 次操作
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    证书操作
                                                    <sup>1</sup>
                                                </td>
                                                <td>
                                                    续订 - ￥30.528/续订请求<br />
                                                    所有其他操作 - ￥0.305/10,000 次操作
                                                </td>
                                                <td>
                                                    续订 - ￥30.528/续订请求<br />
                                                    所有其他操作 - ￥0.305/10,000 次操作
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                <sup>
                                                        1
                                                </sup>
                                                    密钥保管库不颁发证书或转售从公共证书颁发机构购买的证书。密钥保管库提供了简化和自动化针对证书（从公共证书颁发机构上购买的）的某些任务的能力，如注册和更新。
                                            </div>
                                        </div>
                                    </div>
                                    <br />
                                    <div class="tab-control-container tab-active">
                                        <h3>
                                            托管的 HSM 池
                                        </h3>
                                        <table cellpadding="0" cellspacing="0" width="100%" id="keyvault-HSM-pool-N2E2N3E3-table">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        每个 HSM 池的每小时使用费用
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    标准 B1
                                                </td>
                                                <td>
                                                    ￥19.2
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <br />
                                    <div class="tab-control-container tab-active">
                                        <h3>
                                            受软件保护的密钥
                                        </h3>
                                        
                                        <table cellpadding="0" cellspacing="0" width="100%" id="keyvault-key-protected-N2E2N3E3-table">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        标准
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        高级
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    RSA 2,048 位密钥
                                                </td>
                                                <td>
                                                    ￥0.3/10,000 个事务
                                                </td>
                                                <td>
                                                    ￥0.3/10,000 个事务
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    高级密钥类型 - <br/>
                                                    RSA 3,072 位、RSA 4,096 位<br/>和椭圆曲线密码体制(ECC)密钥
                                                </td>
                                                <td>
                                                    ￥1.5/10,000 个事务
                                                </td>
                                                <td>
                                                    ￥1.5/10,000 个事务
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <br/>
                                    <div class="tab-control-container tab-active">
                                        <h3>
                                            受 HSM 保护的密钥
                                        </h3>
                                        
                                        <table cellpadding="0" cellspacing="0" width="100%" id="keyvault-HSM-protected-N2E2N3E3-table">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        标准
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        高级
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    RSA 2,048 位密钥
                                                </td>
                                                <td>
                                                    N/A
                                                </td>
                                                <td>
                                                    ￥10.176 每月每个密钥
                                                    <sup>1</sup>
                                                    + ￥0.3/10,000 个事务
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    高级密钥类型 - 
                                                    <sup>1</sup>
                                                    <br/>
                                                    RSA 3,072 位、RSA 4,096 位<br/>和椭圆曲线密码体制(ECC)密钥
                                                </td>
                                                <td>
                                                    N/A
                                                </td>
                                                <td>
                                                    ￥50.88每月每个密钥 + ￥1.5/10,000 个事务
                                                </td>
                                            </tr>
                                        </table>
                                        <div class="tags-date">
                                            <div class="ms-date">
                                                <sup>
                                                        1
                                                </sup>
                                                仅对活跃使用(过去 30 天内使用过)的受 HSM 保护的密钥收费，且该类密钥的每个版本会计作一个单独的密钥。有关详细信息，请参阅下面的常见问题解答。
                                            </div>
                                        </div>
                                    </div>
                                    <br/>
                                    <div class="tab-control-container tab-active">
                                        <h3>
                                            密钥轮换
                                        </h3>
                                        
                                        <table cellpadding="0" cellspacing="0" width="100%" id="keyvault-key-rotation-N2E2N3E3-table">
                                            <tr>
                                                <th align="left">
                                                    <strong>
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        标准
                                                    </strong>
                                                </th>
                                                <th align="left">
                                                    <strong>
                                                        高级
                                                    </strong>
                                                </th>
                                            </tr>
                                            <tr>
                                                <td>
                                                    自动密钥轮换
                                                </td>
                                                <td>
                                                    每个计划的轮换 ￥10.176
                                                </td>
                                                <td>
                                                    每个计划的轮换 ￥10.176
                                                </td>
                                            </tr>
                                        </table>
                                    </div>

                                    </div>
                        </div>
                        <!-- END: TAB-CONTAINER-1 -->

                    <!-- END: TAB-CONTROL -->

                            <div class="pricing-page-section">
                                <div class="more-detail">
                                    <h2>
                                        常见问题
                                    </h2>
                                    <em>
                                        全部展开
                                    </em>
                                    <ul>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-stored">
                                                    我可在密钥保管库中存储什么？
                                                </a>
                                                <section>
                                                    <p>
                                                        可在 Key Vault 存储以下类型的密钥和机密：
                                                    </p>
                                                    <ul>
                                                        <li>
                                                            可以在 HSM 中导入或生成密钥，并始终将其锁定在 HSM 范围内。当要求 Key Vault 服务使用一个密钥解密或签名时，将在 HSM 内部执行该操作。
                                                        </li>
                                                        <li>
                                                            你还可以使用 HSM 中的密钥进行加密。在这种情况下，与在 HSM 内部相反，将在软件中执行加密操作。这些计算都以 Azure 计算角色执行。
                                                        </li>
                                                        <li>
                                                            机密是少于 10_KB 的数据，如应用程序可以明文存储和检索的密码或 .PFX 文件。Key Vault 服务可保存使用 HSM 支持的密钥加密的机密，并提供针对这些机密的访问控制层。
                                                        </li>
                                                    </ul>
                                                    <p>除了密钥和机密，还可以存储和管理从公共证书颁发机构购买的 SSL/TLS 证书并通过 Key Vault 自动注册或续订证书（如果 Key Vault 目前支持该公共证书颁发机构）。</p>
                                                </section>
                                            </div>
                                        </li>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-operations">
                                                    操作是如何定义的？
                                                </a>
                                                <section>
                                                    <p>每个已成功通过身份验证的 REST API 调用都计为一个操作。</p>
                                                    <p>密钥操作示例 - 创建、导入、获取、列出、备份、还原、删除、更新、签名、验证、包装、解包、加密和解密。请注意，操作收取的费用可能因密钥的类型而异(例如，对 2,048 位 RSA 密钥与 4,096 位 RSA 密钥执行的操作会按不同计量以不同价格计费，如上面的定价部分所述)。</p>
                                                    <p>机密操作示例 - 创建/更新、获取、列出。</p>
                                                    <p>证书操作示例 - 创建、更新策略、联系、导入、续订或更新证书。请注意，证书续订操作的成本与证书的所有其他操作的成本是分开的。</p>
                                                </section>
                                            </div>
                                        </li>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-billed">
                                                    针对执行的操作我如何付费？
                                                </a>
                                                <section>
                                                    <p>
                                                        针对所有密钥（软件保护的密钥和 HSM 保护的密钥）、密文密码和证书的操作都按固定费率计费，每 10,000 个操作 ￥0.305,
                                                        证书续订请求除外，其为每次续订￥30.528。示例 - A) 在一个计费周期内，对 HSM 保护的密钥执行 2,000 个操作、对软件保护的密钥执行 1,000 个操作、对机密执行 500 个操作。那么在该计费周期内，你将需要支付 3,500 个操作的费用。B) 在给定计费周期内，对 20 个证书执行 500 个操作，则其中 2 个证书也会由 Key Vault 续订。将会按 500 个操作和 2 个证书续订请求对你进行计费。
                                                    </p>
                                                </section>
                                            </div>
                                        </li>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-billed">
                                                    如何对我的HSM密钥计费？
                                                </a>
                                                <section>
                                                    <p>
                                                        在 Azure Key Vault HSM 中生成或导入的每个密钥都将作为单独的密钥收费。仅当密钥在前 30 天内至少使用过一次(基于密钥的创建周年日期)时，才会向你收取密钥费用。请注意，如果存储了给定密钥的多个(历史)版本，则每个版本都将被视为单独的密钥进行计费。
                                                    </p>
                                                    <p>实例：</p>
                                                    <ul>
                                                        <li>你在密钥保管库中添加三个受 HSM 保护的密钥。在接下来的 30 天内，你将使用第一个密钥 10,000 次，第二个密钥一次，根本不使用第三个密钥。对于此 30 天 期间，你将需要为 2 HSM 密钥单元付费。例如，如果这些密钥是 2,048 位 RSA 密钥，则需 支付 2 x ￥10.176/密钥/月 = ￥20.352，如果这些密钥是 3,072 位 RSA 密钥，则需支付 2 x ￥50.88/密钥/月 = ￥101.76。</li>
                                                        <li>你在密钥保管库中有 1 个受 HSM 保护的密钥。该密钥有 5 个历史版本，因为你已更改密钥的值四次。 在过去 30 天内，你使用了其中 2 个版本，并且没有接触其他三个版本。对于 2,048 位 RSA 密钥，在本示例中需付费 ￥20.352，而 对于高级密钥类型，在本示例中需付费 ￥101.76。</li>
                                                        <li>请注意，对受 HSM 保护的密钥执行的任何操作都将单独收费，并且将在 HSM 密钥费用之外收取。</li>
                                                    </ul>
                                                </section>
                                            </div>
                                        </li>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-fees">
                                                    Azure Key Vault 是否有设置费？
                                                </a>
                                                <section>
                                                    <p>
                                                        否，Azure Key Vault 没有设置费。
                                                    </p>
                                                </section>
                                            </div>
                                        </li>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-applications">
                                                    如果我的 HSM 保护密钥仅使用不到一个月，我将如何付费？
                                                </a>
                                                <section>
                                                    <p>
                                                        HSM 密钥费用不是根据启用时间长度按比例确定的。我们只对过去 30 天内至少使用过一次的 HSM 密钥计费（基于该密钥的创建周年日）。
                                                    </p>
                                                </section>
                                            </div>
                                        </li>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-key">
                                                    我能否将 Key Vault 用于第三方应用程序？
                                                </a>
                                                <section>
                                                    <p>
                                                        是，你可以授权托管在任何位置（如 Microsoft Azure、第三方云或本地）的任何应用程序使用 Key Vault 中存储的密钥。
                                                    </p>
                                                </section>
                                            </div>
                                        </li>
                                        <li>
                                            <i class="icon icon-plus">
                                            </i>
                                            <div>
                                                <a id="key-vault-question-key">
                                                    如果我的应用程序使用其他 Azure 订户创建的密钥，我是否为使用该密钥付费？
                                                </a>
                                                <section>
                                                    <p>
                                                        否。只有密钥所有者才需要付费。
                                                    </p>
                                                </section>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>密钥保管库在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table> -->
                            <div class="pricing-page-section">
                                <h2>
                                    支持和服务级别协议
                                </h2>
                                <p>
                                    如有任何疑问或需要帮助，请访问
                                    <a href="https://support.azure.cn/zh-cn/support/contact" id="keyvault-contact-page">
                                        Azure 支持
                                    </a>
                                    选择自助服务或者其他任何方式联系我们获得支持。
                                </p>
                                <p>
                                    我们保证至少在 99.9% 的情况下，密钥保管库事务能够在 5 秒内得到处理。若要了解有关我们的服务器级别协议的详细信息，请访问
                                    <a href="../../../support/sla/key-vault/index.html" id="pricing_keyvault_sla">
                                        服务级别协议
                                    </a>
                                    页。
                                </p>
                            </div>
                            <!--BEGIN: Support and service code chunk-->
                            <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
                            <!--END: Support and service code chunk-->
                            <!--BEGIN: Support and service code chunk-->
                            <!--END: Support and service code chunk-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- END: Documentation Content -->
        <!-- BEGIN: Footer -->
        <div class="public_footerpage">
        </div>
        <!--END: Common sidebar-->
        <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
        <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
        </script>
        <script type="text/javascript">
            function getAntiForgeryToken() {
                var token = '<input name="__RequestVerificationToken" type="hidden" value="kklHe0NYvnJYbcCPtcRINnlCfDvuMcxYeuZxSdfevp8W1U4N2cFAdBrE3Eif3gO1YunsY6FaR1DPunQNmjF7Iw5ppI1xbBdUw6I73MDUuKE1" />';
                token = $(token).val();
                return token;
            }
            function setLocaleCookie(localeVal) {
                var Days = 365 * 10; // Ten year expiration
                var exp = new Date();

                var hostName = window.location.hostname;
                var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

                exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
                document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
            }

            setLocaleCookie(window.currentLocale);
        </script>
        <script type="text/javascript">
            var MARKETING_STORAGE = '/blob/marketing-resource/Content';
            var TECHNICAL_STORAGE = '/blob/tech-content/';
            var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
            var EnablePricingSync = 'false';
        </script>
        <!-- BEGIN: Minified RequireJs -->
        <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
        </script>
        <script src="../../../Static/Scripts/require.js" type="text/javascript">
        </script>
        <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
        </script>
        <!-- END: Minified RequireJs -->
        <!-- begin JSLL -->
        <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
        </script>
        <script type="text/javascript">
            (function () {
                var config = {
                    cookiesToCollect: ["_mkto_trk"],
                    syncMuid: true,
                    ix: {
                        a: true,
                        g: true
                    },
                    coreData: {
                        appId: 'AzureCN',
                        market: 'zh-cn',
                    }
                };
                awa.init(config);
            })();
        </script>
        <!-- end JSLL -->
        <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
        </script>
        <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
        </script>
        <script src="/common/useCommon.js" type="text/javascript">
        </script>
</body>

</html>