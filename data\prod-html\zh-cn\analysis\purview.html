

<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">
    <meta name="keywords" content="治理、保护和管理数据资产" />
    <meta name="description" content="Microsoft Purview 是一项统一的数据治理服务，有助于最大化混合数据的商业价值。使用 Microsoft Purview 数据映射，可以大规模自动扫描并分类数据。使用 Microsof Purview 数据目录，可以进行自助式数据发现，从而加快 BI、分析、AI 和 ML。" />

    <title>治理、保护和管理数据资产</title>

    <link rel="apple-touch-icon" sizes="180x180" href="/Static/Favicon/apple-touch-icon.png">
    <link rel="shortcut icon" href="/Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="icon" href="/Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="manifest" href="/Static/Favicon/manifest.json">
    <link rel="mask-icon" href="/Static/Favicon/safari-pinned-tab.svg" color="#0078D4">
    <meta name="msapplication-config" content="/Static/Favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
        <link rel="canonical" href="https://azure.microsoft.com/pricing/details/purview/"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet">
    <link href="/Static/CSS/common.min.css" rel="stylesheet">
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    
    <!-- END: Page Style -->

    <!-- BEGIN: Minified Page Style -->
    <link href='/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css' rel='stylesheet' />
    <!-- END: Minified Page Style -->
            
    <link rel="stylesheet" href="/StaticService/css/service.min.css" />
</head>
<body class="zh-cn">    
    <script>
        window.requireUrlArgs = "1/6/2020 11:41:53 AM";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "5/9/2019 9:29:29 AM";
        window.footerTimestamp = "5/9/2019 9:29:29 AM";
        window.locFileTimestamp = "5/9/2019 9:29:21 AM";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>

    <style>
       @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
    </style>
<div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage"></div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li><span></span></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select><option selected="selected">加载中...</option></select>
                        <span class="icon icon-arrow-top"></span>
                    </div>
                    
            <tags ms.service="purview" ms.date="09/30/2015" wacn.date="11/27/2015"></tags>
            <style type="text/css">
                .pricing-detail-tab .tab-nav {
                    padding-left: 0 !important;
                    margin-top: 5px;
                    margin-bottom: 0;
                    overflow: hidden;
                }

                .pricing-detail-tab .tab-nav li {
                    list-style: none;
                    float: left;
                }

                .pricing-detail-tab .tab-nav li.active a {
                    border-bottom: 4px solid #00a3d9;
                }

                .pricing-detail-tab .tab-nav li.active a:hover {
                    border-bottom: 4px solid #00a3d9;
                }

                .pricing-detail-tab .tab-content .tab-panel {
                    display: none;
                }

                .pricing-detail-tab .tab-content .tab-panel.show-md {
                    display: block;
                }

                .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                    padding-left: 5px;
                    padding-right: 5px;
                    color: #00a3d9;
                    background-color: #FFF;
                }

                .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                    color: #FFF;
                    background-color: #00a3d9;
                }

                .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                    color: #FFF;
                    background-color: #00a3d9;
                }

                .pure-content .technical-azure-selector p a,
                .pure-content .technical-azure-selector table a {
                    background: 0 0;
                    padding: 0;
                    margin: 0 6px;
                    height: 21px;
                    line-height: 22px;
                    font-size: 14px;
                    color: #00a3d9;
                    float: none;
                    display: inline;
                }

                .svg {
                    width: 50px;
                    float: left;
                    margin-right: 10px;
                }
            </style>
            <div class="hide-info" style="display:none;">
                <div class="bg-box">
                    <div class="cover-bg">&nbsp;</div>
                </div>
                <div class="msg-box">
                    <div class="pricing-unavailable-message">Not available in the selected region</div>
                </div>
            </div>
            <!-- BEGIN: Product-Detail-TopBanner -->
            <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product_banner_policy.png','imageHeight':'auto'}">
                <div class="common-banner-image">
                    <div class="common-banner-title">
                       <img src="/Images/marketing-resource/media/images/purview/purviewIcon.svg" />
                        <h2>治理、保护和管理数据资产</h2>
                        <h4>Microsoft Purview 是一项统一的数据治理服务，有助于最大化混合数据的商业价值。使用 Microsoft Purview 数据映射，可以大规模自动扫描并分类数据。使用 Microsof Purview 数据目录，可以进行自助式数据发现，从而加快 BI、分析、AI 和 ML。</h4>
                        
                    </div>
                </div>
            </div>
            <!-- END: Product-Detail-TopBanner -->
            <div class="pricing-page-section">
                <div class="pricing-page-section">
                    <h2>价格详情</h2>
                </div>
                <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                    <div class="tab-container-container">
                        <div class="tab-container-box">
                            <div class="tab-container">
                                <div class="dropdown-container software-kind-container" style="display:none;">
                                    <label>OS/Software:</label>
                                    <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                        <span class="selected-item">purview</span>
                                        <i class="icon"></i>
                                        <ol class="tab-items">
                                            <li class="active"><a href="javascript:void(0)" data-href="#tabContent1"
                                                    id="home_purview">purview</a></li>
                                        </ol>
                                    </div>
                                    <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                                        <option selected="selected" data-href="#tabContent1" value="purview">purview
                                        </option>
                                    </select>
                                </div>
                                <div class="dropdown-container region-container">
                                    <label>区域:</label>
                                    <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                        <span class="selected-item">中国北部 3</span>
                                        <i class="icon"></i>
                                        <ol class="tab-items">
                                            <li><a href="javascript:void(0)" data-href="#north-china3" id="north-china3">中国北部
                                                    3</a></li>
                                            <!-- <li class="active"><a href="javascript:void(0)" data-href="#east-china2"
                                                    id="east-china2">中国东部
                                                    2</a>
                                            </li> -->
                                            <!-- <li><a href="javascript:void(0)" data-href="#north-china2" id="north-china2">China North
                                                    2</a></li>

                                            <li><a href="javascript:void(0)" data-href="#east-china" id="east-china">China East</a></li>
                                            <li><a href="javascript:void(0)" data-href="#north-china" id="north-china">China North</a>
                                            </li> -->
                                        </ol>
                                    </div>
                                    <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                                        <option selected="selected" data-href="#north-china3" value="north-china3">north-china3</option>
                                        <!-- <option data-href="#east-china2" value="east-china2">east-china2</option> -->
                                        <!-- <option data-href="#north-china2" value="north-china2">north-china2
                                        </option>
                                        <option data-href="#east-china" value="east-china">east-china
                                        </option>
                                        <option data-href="#north-china" value="north-china">north-china
                                        </option> -->
                                    </select>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                    <!-- BEGIN: TAB-CONTAINER-1 -->
					<div class="tab-content">
						<!-- BEGIN: TAB-CONTENT-1 -->
						<div class="tab-panel" id="tabContent1">
                            <div class="tab-content">
                                <div class="scroll-table" style="display: block;">
                                    <h3>Microsoft Purview 数据映射</h3>
                                    <div>
                                       <p>Microsoft Purview 数据映射将与数据资产关联的元数据、注释和关系存储在可搜索的知识图中。</p>
                                       <p>最终用户通过专门生成的应用程序(例如数据目录、Data Estate Insights 等)使用数据映射中的技术元数据、世系、分类和其他信息。</p>
                                       <p>数据映射以云规模填充，并通过自动扫描、分类和更新(来自配置为使用 Microsoft Purview 帐户进行发现的云和本地中的数据系统)来保持最新状态。</p>
                                        <p>可以处理数据映射中包含的信息以扩充数据映射，从而简化搜索、生成见解、优化数据资产的存储等。</p>
                                       <p>开发人员还可以使用开放式 API (包括 Apache Atlas、扫描 API 等)生成由 Microsoft Purview 数据映射提供支持的自己的应用。</p>
                                       
                                       <p>数据映射按三种类型的活动计费:</p>
                                        <p>
                                            <b>•	数据映射填充 </b>– 示例包括基于元数据和内容检查的元数据和世系提取或分类。
                                        </p>
                                        <p><b>•	   数据映射扩充 </b>– 示例包括使用资源集优化数据湖资产的存储，或聚合分类以生成见解。
                                        </p>
                                        <p>
                                            <b>•	数据映射消耗</b>- 示例包括提供搜索结果或呈现世系图。这还包括使用 Apache Atlas API 在数据映射上生成应用。
                                            
                                        </p>
                                    </div>
                                    <h3>示例方案:</h3>
                                    <div>
                                        <p>除上述内容外，此处还提供了更多有关正式发布中定价工作原理的信息，从而帮助估算成本。</p>
                                        <p>数据映射可根据请求负载弹性缩放容量。按每秒数据映射操作数测量请求负载。作为一种成本控制措施，数据映射默认配置为在<a href="https://learn.microsoft.com/zh-cn/azure/purview/how-to-manage-quotas" target="_blank">弹性窗口</a>内弹性缩放。</p>
                                        <p><b>数据映射(Always on)</b>: 1 个容量单位 × 每小时每个容量单位 ￥4.182336 × 744 小时，最多 10 GB 元数据存储和每秒 25 次操作</p>
                                        <p><b>扫描（即用即付）</b>: 一个月内所有扫描的总持续分钟数 [M] /每小时 60 分钟 × 每次扫描 32 个 vCore × 每小时每个 vCore ￥6.41088</p>
                                           <p><b>资源集</b>: 一个月内处理高级资源集数据资产的总持续小时数 [H] * 每小时每个 vCore ￥2.13696 </p>
                                    </div>
                                </div>
                                <div>
                                    <div class="category-container-container">
                                        <div class="category-container-box">
                                            <div class="category-container">
                                                <span class="category-title hidden-lg hidden-md">类别：</span>
                                                <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
                                                    <li class="active"><a href="javascript:void(0)" data-href="#tabContent1-1" id="home_storage_gpvhy">数据映射填充</a></li>
                                                    <li><a href="javascript:void(0)" data-href="#tabContent1-2" id="home_storage_gpv2">数据映射扩充</a></li>
                                                    <li><a href="javascript:void(0)" data-href="#tabContent1-3" id="home_storage_blobs">数据映射消耗</a></li>
                                                </ul>
                                                <select class="dropdown-select category-tabs hidden-lg hidden-md">
                                                    <option data-href="#tabContent1-1" id="home_storage_gpv2" value="General Purpose v2">数据映射填充</option>
                                                    <option data-href="#tabContent1-2" id="home_storage_blobs" value="Blob storage">数据映射扩充</option>
                                                    <option data-href="#tabContent1-3" id="home_storage_gpv2" value="General Purpose V2 Hierarchical Namspace">数据映射消耗</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="tab-panel" id="tabContent1-1">
                                        <h3>数据映射填充</h3>
                                        <h4>自动扫描、引入和分类</h4>
                                        <div>
                                            <p>数据映射填充为无服务器计算，且根据扫描持续时间(包括元数据提取和分类)和引入作业计费。使用本机连接器的自动扫描会触发扫描和引入作业。来自 Microsoft Purview 客户端的基于推送的更新(例如，来自 Azure 数据工厂的世系推送)仅触发引入作业。</p>
                                            <p>这些作业的持续时间可能因扫描的系统数或推送更新的系统数、扫描的系统中包含的数据资产、选定的分类数、数据形状和扫描的系统性能而异。</p>
                                        </div>
                                        <table cellpadding="0" cellspacing="0" width="100%" >
                                            <tr>
                                                <th align="left"><strong></strong></th>
                                                <th align="left"><strong>价格</strong></th>
                                                
                                            </tr>
                                            <tr>
                                                <td>对于 Power BI Online</td>
                                                <td>限时免费</td>
                                            </tr>
                                            <tr>
                                                <td>对于本地 SQL Server</td>
                                                <td>限时免费</td>
                                            </tr>
                                            <tr>
                                                <td>对于其他数据源</td>
                                                <td>￥6.41088 每 vCore 每小时</td>
                                            </tr>
                                            </table>
                                            <div>
                                                <p>
                                                    注意: 扫描和引入作业不包括使用 Apache Atlas 添加或修改实体，而是根据请求负载驱动的容量单位(按操作/秒)计为数据映射消耗。
                                                </p>
                                                <p>
                                                    注意: Microsoft Purview 在预配 Microsoft Purview 帐户的订阅内将存储帐户和 Azure 事件中心帐户预配为托管资源。此为扫描期间支持增强型安全功能的必需项。这可能会产生单独费用，在大多数情况下，不会超过扫描费用的 2%。请参阅 Azure 门户中 Azure Purview Resource JSON 内的“托管资源”节。
                                                </p>
                                                <p>注意: 使用 Microsoft Purview 治理其他云(例如 AWS、GCP)中的数据的客户可能会因数据传输和 API 调用(与将元数据发布到 Microsoft Purview 数据映射关联)而产生额外费用。此费用因地区而异。有关扫描 AWS 中数据的信息，请参阅 AWS 管理控制台内的“计费和管理”控制台，从而查看这些费用。</p>
                                            </div>
                                    </div>
                                    <div class="tab-panel" id="tabContent1-2">
                                        <h3>数据映射扩充</h3>
                                        <h4>高级资源集</h4>
                                        <div>
                                        <p>高级资源集是数据映射的内置功能，用于优化与数据湖中分区文件关联的数据资产的存储和搜索。处理资源集数据资产的计费为无服务器计算，且以处理的持续时间为基础，该时间可能因已配置的分区文件和资源集配置文件中的更改而异。</p>
                                     </div>
                                        <table cellpadding="0" cellspacing="0" width="100%" >
                                            <tr>
                                                <th align="left"><strong></strong></th>
                                                <th align="left"><strong>价格</strong></th>
                                                
                                            </tr>
                                            <tr>
                                                <td>高级资源集</td>
                                                <td>￥2.13696 每 vCore 每小时</td>
                                            </tr>
                                            </table>
                                            <div class="tags-date">
                                                <div class="ms-date">
                                                    注意: 默认情况下，对于配置为在启用资源集切换的情况下进行扫描的所有系统，高级资源集处理每 12 小时运行一次。   
                                                </div>
                                            </div>

                                            <h3>Insights 生成 </h3>
                                            <div><p>Insights 生成将原始数据映射中的元数据和分类聚合为可在数据资产见解应用程序中可视化的可执行扩充报告，以及可导出的业务友好格式的精细资产级别信息。报告可视化和导出会因数据资产见解应用程序中的见解报告使用而产生费用。</p></div>
                                            <table cellpadding="0" cellspacing="0" width="100%" >
                                                <tr>
                                                    <th align="left"><strong></strong></th>
                                                    <th align="left"><strong>价格</strong></th>
                                                    
                                                </tr>
                                                <tr>
                                                    <td>报告生成</td>
                                                    <td>￥8.34432 每 vCore 每小时</td>
                                                </tr>
                                                </table>
                                                <div class="tags-date">
                                                    <div class="ms-date">
                                                        注意: 默认情况下，Insights 生成在预配时启用，且可以在 Microsoft Purview 治理门户的管理中心内禁用。禁用 Insights 生成将停止刷新 Data Estate Insights 应用程序中的报告。默认情况下，Insights 生成会根据数据映射更新自动运行。   
                                                    </div>
                                                </div>
                                    </div>
                                    <div class="tab-panel" id="tabContent1-3">
                                        <h3>数据映射消耗</h3>
                                        <h4>弹性数据映射</h4>
                                        <div>
                                        <p>默认情况下，Microsoft Purview 帐户预配至少 1 个容量单位的数据映射。1 个容量单位支持每秒最多 25 项数据映射操作的请求，且包含最多 10 GB 数据资产元数据存储。第一个 1MB 的数据映射元数据存储对所有客户都是免费的。
                                            数据映射可以基于请求负载弹性缩放容量。请求负载按每秒数据映射操作数进行度量。作为成本控制度量值，数据映射默认配置为在<a href="https://learn.microsoft.com/zh-cn/azure/purview/how-to-manage-quotas" target="_blank">弹性窗口</a>内弹性扩展。
                                            </p>
                                        <p>数据映射操作指的是创建、读取、更新或删除数据映射中的实体。实体示例包括数据资产或两个数据资产之间的世系关系。搜索请求可能需要执行多项操作，具体取决于返回的资产和请求的复杂性。实体的存储大小可能因实体类型和与实体关联的注释而异。</p>
                                        <p>对于所需的每 10 GB 元数据存储，数据映射需要一个额外的容量单位。例如，具有 10 GB 元数据存储的数据映射按每小时 1 个“容量单位”计费。如果新的数据资产将大小增加到 10.1 GB，则数据映射按每小时 2 个“容量单位”计费。</p>
 
                                        </div>
                                        <table cellpadding="0" cellspacing="0" width="100%" >
                                            <tr>
                                                <th align="left"><strong></strong></th>
                                                <th align="left"><strong>价格</strong></th>
                                                <th align="left"><strong>包含的数量</strong></th>
                                            </tr>
                                            <tr>
                                                <td>容量单位</td>
                                                <td>￥4.182336/容量单位/小时</td>
                                                <td>1MB 数据映射元数据存储</td>
                                            </tr>
                                           
                                            </table>
                                    </div>
                                </div>
                                <div>
                                    <h3>Microsoft Purview 应用程序</h3>
                                    <div><p>Microsoft Purview 应用程序是基于数据映射(包括数据目录、Data Estate Insights 等)生成的一组可独立采用且高度集成的用户体验。这些应用程序由数据使用者、生成者、数据专员和主管使用，使企业能够确保数据易于发现、理解、高质量，且所有用途都符合企业和法规要求。</p></div>
                                    <div class="technical-azure-selector pricing-detail-tab">
                                    <ul class="tab-nav">
                                        <li class="active">
                                            <a href="javascript:void(0)" data-href="#tabContent1-4"
                                                id="home_purview-dataca" data-toggle="tab">数据目录</a>
                                        </li>
                                        <li>
                                            <a href="javascript:void(0)" data-href="#tabContent1-5"
                                                id="home_purview-estate" data-toggle="tab">Data Estate Insights</a>
                                        </li>
                                    </ul>
                                    <div class="tab-content">
                                        <div class="tab-panel" id="tabContent1-4">
                                            <h3>数据目录</h3>
                                            <p>数据目录是一款基于数据映射生成的应用程序，供业务用户、数据工程师和专员用于快速、轻松地发现数据、识别世系关系和分配业务上下文。</p>
                                            <p>标记为“包含在数据映射中”的功能计为数据映射容量单位消耗。</p>
                                            <table cellpadding="0" cellspacing="0" width="100%" >
                                                <tr>
                                                    <th align="left"><strong>功能</strong></th>
                                                    <th align="left"><strong>价格</strong></th>
                                                    
                                                </tr>
                                                <tr>
                                                    <td>搜索和浏览数据资产</td>
                                                    <td>包含在数据映射中</td>
                                                </tr>
                                                <tr>
                                                    <td>业务术语表</td>
                                                    <td>包含在数据映射中</td>
                                                </tr>
                                                <tr>
                                                    <td>世系可视化效果</td>
                                                    <td>包含在数据映射中</td>
                                                </tr>
                                                <tr>
                                                    <td>自助式数据访问</td>
                                                    <td>预览版中免费</td>
                                                </tr>
                                                </table>
                                        </div>
                                        <div class="tab-panel" id="tabContent1-5">
                                            <h3>Data Estate Insights</h3>
                                            <p>数据资产见解是一款基于数据映射生成的应用程序，供数据主管和专员用于了解其各种数据资产的数据资产运行状况和治理状况，并推动采取纠正措施以缩小差距。数据资产见解提供通过见解生成而生成的扩充数据，这些数据按上述数据映射扩充中所示(见解生成部分)单独收费。</p>
                                            <p>见解消耗按 API 调用计费。一个 API 调用最多返回 10,000 行表格结果。</p>
                                            
                                            
                                            <table cellpadding="0" cellspacing="0" width="100%" >
                                                <tr>
                                                    <th align="left"><strong></strong></th>
                                                    <th align="left"><strong>价格</strong></th>
                                                    
                                                </tr>
                                                <tr>
                                                    <td>Insights 消耗</td>
                                                    <td>每个 API 调用 ￥2.13696</td>
                                                </tr>
                                               
                                                </table>
                                        </div>
                                    </div>
                                </div>
                                   
                                </div>
                            </div>
                        </div>
                        <!-- END: TAB-CONTENT-1 -->
                    </div>
                    <!-- END: TAB-CONTAINER-1 -->
                </div>
            </div>
            <!-- <div class="pricing-page-section">
                <h2>Support &amp; SLA</h2>
                <p>If you have any questions or need help, please visit <a href="https://support.azure.cn/en-us/support/contact" id="policy-contact-page"> Azure Support</a> and select self-help service or any other method to contact us for support.</p>
                <p>Azure Policy is a free service, therefore, it does not have a financially backed SLA.To learn more about the details of
                our Service Level Agreement, please visit the <a href="/en-us/support/legal/sla/index.html" id="pricing_policy_sla">Service Level Agreements</a> page.</p>
            </div>   -->
            <!-- BEGIN: TAB-CONTROL -->
            <!-- END: TAB-CONTROL -->
            <!--BEGIN: Support and service code chunk-->
            
             
                                                                              
            <!--END: Support and service code chunk-->                                                                                
    
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->

<!-- BEGIN: Footer -->
<div class="public_footerpage"></div>
<!--END: Common sidebar-->


        
<link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet">
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="dEG1TJI2W5mMj8WKXt85yLgsQNITtN7SiMu3MAc9CSHbrO-Ne_9JZzpgNXK1BENnmpPLozM99p3Ch6p2fgwctzrKMzm9hwC7lKRxU7y710s1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>

<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>

<!-- BEGIN: Minified RequireJs -->
<script src='../../../Static/Scripts/global.config.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/require.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js' type='text/javascript'></script>
<script src=' /Static/Scripts/pricing-page-detail.js' type='text/javascript'></script>
<!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
<!-- END: Minified RequireJs -->

<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js"></script>

<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->

<script type="text/javascript" src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../Static/Scripts/wacndatatracker.js"></script>


<script type="text/javascript" src="/common/useCommon.js"></script>

</body>

</html>