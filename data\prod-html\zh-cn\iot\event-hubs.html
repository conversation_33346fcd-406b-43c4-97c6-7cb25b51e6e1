<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, 物联网, 事件中心, Azure Event Hubs, 数据处理, 托管服务, 价格" name="keywords"/>
  <meta content="了解 Azure 事件中心（Azure Event Hubs）价格详情。Azure 事件中心是一项用于处理来自网站、应用和设备的大数据流的服务。提供基本和标准级别。标准级别的事件中心为每个订阅提供 1,000 个 Service Bus 中转连接。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   事件中心服务定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/event-hubs/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }
      
        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="event-hubs" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav {
                    padding-left: 0 !important;
                    margin-top: 5px;
                    margin-bottom: 0;
                    overflow: hidden;
                }

                .pricing-detail-tab .tab-nav li {
                    list-style: none;
                    float: left;
                }

                .pricing-detail-tab .tab-nav li.active a {
                    border-bottom: 4px solid #00a3d9;
                }

                .pricing-detail-tab .tab-nav li.active a:hover {
                    border-bottom: 4px solid #00a3d9;
                }

                .pricing-detail-tab .tab-content .tab-panel {
                    display: none;
                }

                .pricing-detail-tab .tab-content .tab-panel.show-md {
                    display: block;
                }

                .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                    padding-left: 5px;
                    padding-right: 5px;
                    color: #00a3d9;
                    background-color: #FFF;
                }

                .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                    color: #FFF;
                    background-color: #00a3d9;
                }

                .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                    color: #FFF;
                    background-color: #00a3d9;
                }

                .pure-content .technical-azure-selector p a,
                .pure-content .technical-azure-selector table a {
                    background: 0 0;
                    padding: 0;
                    margin: 0 6px;
                    height: 21px;
                    line-height: 22px;
                    font-size: 16px;
                    color: #00a3d9;
                    float: none;
                    display: inline;
                }

                .svg {
                    width: 50px;
                    float: left;
                    margin-right: 10px;
                }
       </style>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/event-hubs.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           事件中心
           <span>
            Event Hubs
           </span>
          </h2>
          <h4>
           从数百万个设备中接收遥测
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              spring-cloud
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_spring-cloud">
                spring-cloud
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="spring-cloud">
              spring-cloud
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
                <li>
                    <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                     中国东部 3
                    </a>
                   </li>  
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 3
               </a>
              </li>
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                <option data-href="#east-china3"  value="east-china3">
                    中国东部 3
                   </option>
                   <option data-href="#north-china2"  value="north-china2">
                    中国北部 3
                   </option>
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-control-container tab-active" id="tabContent1">
         <!-- BEGIN: Table1-Content-->
         <h2>
          定价详细信息
         </h2>
         <p>
          Azure 事件中心是一种完全托管的实时数据引入服务，它简单、安全且可缩放。通过事件中心，每秒可流式传输来自任何源的数百万个事件，从而可构建动态数据管道并立即应对业务挑战。利用异地灾难恢复和异地复制选项保证数据引入安全无忧。
         </p>
         <p>
          借助适用于 Apache Kafka 的 Azure 事件中心，无需做任何代码更改即可使现有的 Kafka 客户端和应用程序与事件中心通信，让你在享受托管 Kafka 体验的同时无需管理自己的群集。
         </p>
         <div class="tags-date">
          <div class="ms-date">
           *以下价格均为含税价格。
          </div>
          <br/>
          <div class="ms-date">
           *每月价格估算基于每个月 744 小时的使用量。
          </div>
         </div>
         <table cellpadding="0" cellspacing="0" width="100%">
          <tr>
           <th align="left">
            <strong>
            </strong>
           </th>
           <th align="left">
            <strong>
             基本
            </strong>
           </th>
           <th align="left">
            <strong>
             标准
            </strong>
           </th>
           <th align="left">
            <strong>
             高级
            </strong>
           </th>
           <th align="left">
            <strong>
             专用
             <sup>
              *
             </sup>
            </strong>
           </th>
          </tr>
          <tr>
           <td>
            容量
           </td>
           <td>
            每个吞吐量单位
            <sup>
             ***
            </sup>
            ￥0.097/小时
           </td>
           <td>
            每个吞吐量单位
            <sup>
             ***
            </sup>
            ￥0.19/小时
           </td>
           <td>
            每个处理单位(PU)￥10.45/小时
           </td>
           <td>
            每个
            <a aria-label="lianjie" href="https://docs.azure.cn/zh-cn/event-hubs/event-hubs-dedicated-overview#capacity-unitscu" id="lianjie" target="_blank">
             容量单位(CU)
            </a>
            ￥45.9/小时
           </td>
          </tr>
          <tr>
           <td>
            入口事件
           </td>
           <td>
            每百万个事件 ¥ 0.18
           </td>
           <td>
            每百万个事件 ¥ 0.18
           </td>
           <td>
            包含
           </td>
           <td>
            包含
           </td>
          </tr>
          <tr>
           <td>
            捕获
           </td>
           <td>
           </td>
           <td>
            ¥ 0.636/小时 (~¥473.184/月）
            <sup>
             ***
            </sup>
           </td>
           <td>
            包含
           </td>
           <td>
            包含
           </td>
          </tr>
          <tr>
           <td>
            Apache Kafka
           </td>
           <td>
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            架构注册表
           </td>
           <td>
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
          </tr>
          <tr>
           <td>
            最大保留期
           </td>
           <td>
            1 天
           </td>
           <td>
            7 天
           </td>
           <td>
            90 天
           </td>
           <td>
            90 天
           </td>
          </tr>
          <tr>
           <td>
            存储保留期
           </td>
           <td>
            84 GB
           </td>
           <td>
            84 GB
           </td>
           <td>
            1 TB/PU
           </td>
           <td>
            10 TB/PU
           </td>
          </tr>
          <td>
           延长保持
           <sup>
            **
           </sup>
          </td>
          <td>
          </td>
          <td>
          </td>
          <td>
           ¥ 1.02/GB/月 (每 PU 包含 1 TB)
          </td>
          <td>
           ¥ 1.02/GB/月 (每 CU 包含 10 TB)
          </td>
         </table>
         <!-- <p>基本级别的事件中心为每个 Service Bus 命名空间提供 100 个 Service Bus 中转连接，这些中转连接在整个 Service Bus 命名空间中共享。标准级别的事件中心为每个订阅提供 1,000 个 Service Bus 中转连接。</p> -->
         <!-- END: Table4-Content-->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="tags-date">
        <div class="ms-date">
         <sup>
          *
         </sup>
         要了解 CU 可以实现的程度，请参阅基准结果。使用量将按一小时增量收费，最少对四小时的使用量进行收费。有关此产品/服务的详细信息，请阅读事件中心专用层概述。有关其他问题，请联系配额增加支持，或请联系事件中心团队。
        </div>
        <br/>
        <!-- <div class="ms-date"><sup>2</sup>若要请求将标准事件中心吞吐量单位增加到 20 个以上，请</span><a style="font-size: 12px; margin-left: 1px;margin-right: 0px;" href="https://support.azure.cn/zh-cn/support/contact">联系我们</a>。</div><br /> -->
        <div class="ms-date">
         <sup>
          **
         </sup>
         超过包含的存储配额的消息保留将导致超额费用。
        </div>
        <br/>
        <div class="ms-date">
         <sup>
          ***
         </sup>
         吞吐量单位提供 1 MB/s 的流入量和 2 MB/s 流出量。
        </div>
       </div>
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question1">
             什么是入口事件以及如何对它们计费？
            </a>
            <section>
             <p>
              入口事件是等于或小于 64 KB 的数据单位。每个入口事件都是一个可计费事件。较大的消息按 64 KB 的倍数计费。例如，8 KB 的消息按一个事件计费，而 96 KB 的消息按两个事件计费。
             </p>
             <p>
              从事件中心使用的事件，以及管理操作和“控制调用”（例如检查点）不会统计为可计费入口事件，而会累积到吞吐量单位限额中。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question2">
             什么是吞吐量单位以及如何对它们计费？
            </a>
            <section>
             <p>
              吞吐量单位由客户通过 Azure门户或事件中心管理 API 明确选择。吞吐量单位将应用到某个 Service Bus 命名空间中的所有事件中心，每个吞吐量单位将赋予该命名空间以下功能：
             </p>
             <ul style="list-style:none;">
              <li>
               1.	每秒最多 1 MB 入口事件（等于发送到事件中心的事件数），但每秒的入口事件、管理操作或控制 API 调用数不超过 1000 个。
              </li>
              <li>
               2.	每秒最多 2 MB 出口事件（等于从事件中心使用的事件数）。
              </li>
              <li>
               3.	最多 84 GB 事件存储（对于默认的 24 小时保留期已足够）。
              </li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question3">
             事件中心吞吐量单位是如何实施的？
            </a>
            <section>
             <p>
              如果某个命名空间中所有事件中心内的总入口吞吐量或总入口事件率超过了聚合吞吐量单位限额，则发送方将会受到限制，并有错误消息指出已超过入口配额。
             </p>
             <p>
              如果某个命名空间中所有事件中心内的总出口吞吐量或总出口事件率超过了聚合吞吐量单位限额，则接收方将会受到限制，并有错误消息指出已超过出口配额。入口配额和出口配额是分开实施的，因此，任何发送方都不会导致减慢事件的使用，并且任何接收方都无法阻止将事件发送到事件中心。
             </p>
             <p>
              请注意，吞吐量单位的选择与事件中心分区（在类似系统中有时称作分片）的数目无关。尽管每个分区提供的最大吞吐量为每秒 1 MB/1000 个入口事件和每秒 2 MB 出口事件，但分区本身不产生固定的费用。费用是针对某个 Service Bus 命名空间中所有事件中心上的聚合吞吐量单位收取的。这样，客户便可以创建足够多的分区来支持其系统的最大预期负载，在系统上的事件负载真正需要更大的吞吐量之前无需支付任何吞吐量单位费用，并且无需更改其系统的结构和体系结构来满足系统负载升高的需求。
             </p>
             <p>
              假设你在命名空间中选择了 8 个吞吐量单位，并创建了包含 32 个分区的单个事件中心。如果此事件中心内的所有分区负载均衡，则每个分区将获得大约 0.25 MB/秒的入口吞吐量，总聚合吞吐量为 8 MB/秒。如果某一个分区出现 1 MB/秒的使用量高峰，而其他 8 个分区的负载只是其峰值负载的一半（0.125 MB/秒），则不会发生分区受限的情况。但是，如果某一个分区的负载高峰超过了 1 MB/秒，则该分区将会受到限制（因为每个分区有限额），尽管所有分区的聚合吞吐量仍旧低于 8 MB/秒。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question4">
             可以选择的吞吐量单位数是否有限制？
            </a>
            <section>
             <p>
              是。基本层和标准层命名空间最多可通过自助体验具有 40 个吞吐量单位 (TU)。如果你需要的 TU 超过 40 个，建议考虑使用专用事件中心。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question5">
             最长的保留期是多久？
            </a>
            <section>
             <p>
              在公开上市后，我们提供最长 7 天的保留期。请注意，不应将事件中心用作永久性的数据存储。提供 24 小时以上的保留期主要是为了方便用户将事件流重播到相同的系统，以实现培训或者基于现有数据验证新计算机学习模型这样的目的。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question6">
             将事件中心事件保留 24 小时以上需要付费吗？
            </a>
            <section>
             <p>
             </p>
             <p>
              在许多情况下是要付费的。如果存储事件的总量大小超过了选定吞吐量单位数的存储限额（每个吞吐量单位为 84 GB），则会根据一般的
              <a href="../../../home/<USER>/storage/index.html#price" id="pricing_event_hubs_blob" style="color:#00a8d9;">
               Azure Blob  存储费率
              </a>
              收取超额部分的费用。每个吞吐量单位的存储限额涵盖了保留期 24 小时（默认期限）的所有存储费用，即使该吞吐量单位的使用量达到了最大入口限额。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question7">
             事件中心存储大小是如何计算和计费的？
            </a>
            <section>
             <p>
              所有存储事件的总大小（包括事件标头的所有内部开销，或者某个命名空间中所有事件中心内的磁盘存储结构）是按整天计量的。在一天结束时，将计算峰值存储大小。每日存储限额是根据一天中选择的最少吞吐量单位数计算的（每个吞吐量单位提供的限额为 84 GB）。如果总大小超过每日存储限额，则使用 Azure Blob存储费率（按本地冗余存储费率）对超出的存储计费。
             </p>
            </section>
           </div>
          </li>
          <!--
						<li>
                            <i class="icon icon-plus"></i>
                            <div>
                                <a id="pricing_enent_hubs_question8">我是否可以使用单个 AMQP 连接来与事件中心、Service Bus 队列和主题相互收发数据？</a>
                                <section>
                                    <p>可以，但前提是所有事件中心、队列和主题都在同一个 Service Bus 命名空间中。这样，客户便能够以次秒级的延迟、经济高效且高度可伸缩的方式，与大量的设备建立双向中转连接。</p>
                                </section>
                            </div>
                        </li>
						-->
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question9">
             你们是否向事件中心收取中转连接费用？
            </a>
            <section>
             <p>
              不管有多少个发送系统/设备，使用 HTTP 发送事件都不必支付连接费用。我们会计量 AMQP 连接数，但是，对于每个基本事件中心命名空间，前 100 个并发连接是免费的；对于标准事件中心，每个订阅的前 1,000 个并发连接是免费的。这些限额涵盖了大多数接收方案，以及许多的服务到服务方案。通常，仅当你打算在大量客户端上使用 AMQP 时，即，想要实现更高效的事件流或者启用双向通信（物联网指挥与控制方案）时，中转连接费用才会变得可观。有关中转连接的构成及其计量方式的详细信息，
                                            请参阅
              <a href="../../../home/<USER>/service-bus/index.html#price" id="pricing_event_hubs_con" style="color: #00a8d9;">
               Service Bus 连接定价
              </a>
              信息。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question10">
             事件中心捕获（Event Hub Capture）如何计费？
            </a>
            <section>
             <p>
              当命名空间中的事件中心启用了捕获功能时，事件中心捕获功能即可启用。根据购买的吞吐量单位按小时计费。吞吐量单位数增加或减少时，该变化将按整小时增量反映在计费中。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question11">
             事件中心捕获（Event Hub Capture）对事件中心的出站有何影响？
            </a>
            <section>
             <p>
              事件中心捕获（Event Hub Capture）不会影响事件中心吞吐量单位的出站率。您仍然可以以每秒 2,000 个事件/每个吞吐量单位 2MBps 的满吞吐量单位读取。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_enent_hubs_question11">
             事件中心捕获（Event Hub Capture）的存储帐户如何应用存储收费？
            </a>
            <section>
             <p>
              事件中心捕获（Event Hub Capture）按照您提供的计划使用存储帐户。因为这是您的存储帐户，所以这个存储帐户的使用费用将被计入您的 Azure 订阅。捕获窗口越短，存储事务发生得越频繁。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>Azure 事件中心在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table>
                -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="hubs-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         对于事件中心“基本”和“标准”级别，我们保证至少在 99.9% 的时间里，正确配置的应用程序能够通过在事件中心发送或接收消息或是执行其他操作。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="/support/legal/sla/event-hubs/v1_2/ " id="pricing_hubs_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="nn8e5gl0U4KEN8UnCpyu9DL-Cq541YWE73M1l8bQagxV0ymvGZOhF5e3FwUQsf-Uo8bLsivQArCu6Lrz2Yt726pagChdxIFTPDi2ACQ1WDw1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
