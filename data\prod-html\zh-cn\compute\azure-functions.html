<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure Functions" name="keywords"/>
  <meta content="在应用服务虚拟机上托管 Azure Functions。" name="description"/>
  <title>
   Azure Functions 定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/zh-cn/services/functions/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="azure-functions" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;              
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;              
            }
            .pure-content .technical-azure-selector .tags-date a,.pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product_banner_functions.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <svg height="48px" id="Layer_1" style="enable-background:new 0 0 45 41.5;float: left;margin-right: 10px;margin-top: 5px;" version="1.1" viewbox="0 0 45 41.5" width="48px" x="0px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" y="0px">
           <style type="text/css">
            .azure-functions0{fill:#3999C6;}
                                .azure-functions1{fill:#FAD11B;}
                                .azure-functions2{opacity:0.3;fill:#F78C21;enable-background:new    ;}
           </style>
           <title>
            Azure Functions
           </title>
           <g>
            <path class="azure-functions0" d="M44.7,21c0.4-0.5,0.4-1.1,0-1.6l-2.2-2.2l-9.7-9.4c-0.4-0.4-1.1-0.4-1.5,0l0,0c-0.4,0.4-0.5,1.1-0.1,1.5
                                    c0,0,0.1,0.1,0.1,0.1l10.2,10c0.4,0.5,0.4,1.2,0,1.6L31.1,31.3c-0.4,0.5-0.4,1.2,0,1.6l0,0c0.4,0.4,1.1,0.4,1.5,0l9.6-9.6l0.1-0.1
                                    L44.7,21z">
            </path>
            <path class="azure-functions0" d="M0.3,21c-0.4-0.5-0.4-1.1,0-1.6l2.2-2.2l9.7-9.4c0.4-0.4,1.1-0.4,1.5,0l0,0c0.4,0.4,0.5,1.1,0.1,1.5
                                    c0,0-0.1,0.1-0.1,0.1l-10,10c-0.4,0.5-0.4,1.2,0,1.6l10.2,10.3c0.4,0.5,0.4,1.2,0,1.6l0,0c-0.4,0.4-1.1,0.4-1.5,0l-9.8-9.4
                                    l-0.1-0.1L0.3,21z">
            </path>
            <polygon class="azure-functions1" points="33.5,0 19.8,0 12.4,20.8 21.4,20.9 14.3,41.5 33.7,14 24.3,14   ">
            </polygon>
            <polygon class="azure-functions2" points="24.3,14 33.5,0 26.3,0 18.7,17.3 27.7,17.4 14.3,41.5 33.7,14   ">
            </polygon>
           </g>
          </svg>
          <h2>
           Azure Functions
           <span>
            Azure Functions
           </span>
          </h2>
          <h4>
           使用无服务器代码处理事件
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure Functions 提供无需服务器的开发体验，支持一系列可靠的事件触发器和数据绑定。
        </p>
        <h2>
         Azure Functions 定价
        </h2>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure Functions
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_azure-functions">
                Azure Functions
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="Azure Functions">
              Azure Functions
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 3
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
                <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                    中国东部 3
                </a>
               </li>
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li>
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china3" selected="selected" value="east-china3">
                中国东部 3
              </option>
             <option data-href="#north-china3" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTAINER-3 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 2 -->
          <!-- BEGIN: Tab level 2 content 3 -->
          <div class="tab-content">
           <div class="scroll-table" style="display: block;">
            <p>
             Azure Functions 使用计划根据每秒资源使用和执行次数计费。消费计划定价包括每月免费授予的 1 百万个请求和每个订阅每月 400,000 GB-s
                                    的资源消耗，包含在订阅中所有函数应用之间的定价中。客户还可在其应用服务计划内运行 Functions，以常规应用服务计划
             <a href="../app-service/index.html">
              费率
             </a>
             计费。
            </p>
            <div class="tags-date">
             <div class="ms-date">
              *以下价格均为含税价格。
             </div>
             <br/>
             <div class="ms-date">
              *每月价格估算基于每个月 744 小时的使用量。
             </div>
            </div>
            <table cellpadding="0" cellspacing="0" id="azure-functions-table-rate-per-second" width="100%">
             <tbody>
              <tr>
               <th align="left">
                <strong>
                 计量
                </strong>
               </th>
               <th align="left">
                <strong>
                 价格
                </strong>
               </th>
               <th align="left">
                <strong>
                 免费授予（每月）
                </strong>
               </th>
              </tr>
              <tr>
               <td align="left">
                执行时间
                <sup>
                 *
                </sup>
               </td>
               <td align="left">
                ￥ 0.000113/GB-s
               </td>
               <td align="left">
                400,000 GB-s
               </td>
              </tr>
              <tr>
               <td align="left">
                总执行数
                <sup>
                 *
                </sup>
               </td>
               <td align="left">
                ￥ 1.30 /百万次执行
               </td>
               <td align="left">
                1 百万次执行
               </td>
              </tr>
             </tbody>
            </table>
            <div class="tags-date">
             <div class="ms-date">
              <sup>
               *
              </sup>
              免费授予仅适用于付费消耗性订阅。
             </div>
            </div>
            <p>
             <strong>
              注意
             </strong>
             - 每个 Functions 应用都默认创建一个存储帐户。免费授予中不包含存储帐户。如果适用，将按标准
             <a href="../storage/files/index.html">
              存储费率
             </a>
             和
             <a href="../bandwidth/index.html">
              网络费率
             </a>
             收费。
            </p>
            <p>
             查看有关
             <a href="https://azure.microsoft.com/zh-cn/global-infrastructure/services/?products=functions&amp;regions=china-non-regional,china-north,china-north-2,china-east,china-east-2">
              区域可用性
             </a>
             的详细信息
            </p>
           </div>
           <h2>
            定价详细信息
           </h2>
           <h3>
            执行
           </h3>
           <p>
            基于所有函数的每月请求执行总次数，对 Functions 进行计费。每当为响应事件而触发执行绑定的函数时，计算一次执行。每月前一百万次执行免费。
           </p>
           <h3>
            资源消耗
           </h3>
           <p>
            基于观察到的资源消耗量（十亿字节 (GB) 为单位）对 Functions 进行计费。将平均内存大小 (GB) 乘以执行函数所花费的时间（毫秒），计算观察到的资源消耗。函数所用内存按四舍五入到最近的 128 MB 计算，最大内存大小高达 1,536 MB，执行时间按四舍五入到最近的 1 ms 计算。单个函数执行的最小执行时间和内存分别为 100 ms 和 128 mb。Functions 定价包括每月免费授予的 400,000 GB。
           </p>
           <div class="scroll-table" style="display: block;">
            <h3>
             高级计划
            </h3>
            <p>
             Azure Functions 高级计划提供与消费计划相同的功能和扩展机制（基于事件数），无需冷启动且具有增强的性能和 VNET 访问。Azure Functions 高级计划根据函数使用的 vCPU 和内存进行计费。
            </p>
            <table cellpadding="0" cellspacing="0" id="azure-functions-table-total-plan-functions" width="100%">
             <tbody>
              <tr>
               <th align="left">
                <strong>
                 计量
                </strong>
               </th>
               <th align="left">
                <strong>
                 价格
                </strong>
               </th>
              </tr>
              <tr>
               <td align="left">
                vCPU 持续时间
               </td>
               <td align="left">
                vCPU：￥1.626 vCPU/小时
               </td>
              </tr>
              <tr>
               <td align="left">
                内存持续时间
               </td>
               <td align="left">
                内存： ￥0.1158 GB/小时
               </td>
              </tr>
             </tbody>
            </table>
           </div>
           <div class="scroll-table" style="display: block;">
            <h3>
             高级计划
            </h3>
            <p>
             Azure Functions 高级计划提供与消费计划相同的功能和扩展机制（基于事件数），无需冷启动且具有增强的性能和 VNET 访问。Azure Functions 高级计划根据函数使用的 vCPU 和内存进行计费。
            </p>
            <table cellpadding="0" cellspacing="0" id="azure-functions-table-total-plan-functions-north3" width="100%">
             <tbody>
              <tr>
               <th align="left">
                <strong>
                 计量
                </strong>
               </th>
               <th align="left">
                <strong>
                 价格
                </strong>
               </th>
              </tr>
              <tr>
               <td align="left">
                vCPU 持续时间
               </td>
               <td align="left">
                vCPU：￥1.724 vCPU/小时
               </td>
              </tr>
              <tr>
               <td align="left">
                内存持续时间
               </td>
               <td align="left">
                内存： ￥0.123 GB/小时
               </td>
              </tr>
             </tbody>
            </table>
           </div>
           <h3>
            Functions 代理
           </h3>
           <p>
            此定价也应用于 Functions 代理。代理是由 HTTP 请求触发的函数。代理使用的内存小于 128 MB。代理执行时间是往返时间（请求响应），因为代理需要处于工作状态才能保持 HTTP 连接的活动状态。如果代理触发函数，将分别对函数执行和内存使用情况进行计数。
           </p>
           <h3>
            定价示例
           </h3>
           <p>
            观察到内存消耗为 512 MB 的函数在本月执行 3,000,000 次，执行持续时间为 1 秒。每月费用按以下方式计算：
           </p>
           <div class="scroll-table" style="display: block;">
            <h3>
             资源消耗计费计算
            </h3>
            <table cellpadding="0" cellspacing="0" id="azure-functions-table-resource-consumption" width="100%">
             <tbody>
              <tr>
               <th align="left">
                <strong>
                 资源消耗（秒）
                </strong>
                <br/>
                执行
                <br/>
                执行持续时间（秒）
               </th>
               <th align="left">
                <br/>
                3 百万次执行
                <br/>
                x 1 秒
               </th>
              </tr>
              <tr>
               <th align="left">
                资源总消耗
               </th>
               <th align="left">
                3 百万秒
               </th>
              </tr>
              <tr>
               <th align="left">
                <strong>
                 资源消耗 (GB)
                </strong>
                <br/>
                资源消耗转换为 GB
                <br/>
                执行时间（秒）
               </th>
               <th align="left">
                <br/>
                512 MB / 1,024 MB
                <br/>
                x 3 百万秒
               </th>
              </tr>
              <tr>
               <th align="left">
                GB 总计
               </th>
               <th align="left">
                1.5 百万 GB
               </th>
              </tr>
              <tr>
               <th align="left">
                <strong>
                 可计费资源消耗
                </strong>
                <br/>
                资源消耗
                <br/>
                每月免费授予量
               </th>
               <th align="left">
                <br/>
                1.5 百万 GB
                <br/>
                - 400,000 GB-s
               </th>
              </tr>
              <tr>
               <th align="left">
                可计费总消耗
               </th>
               <th align="left">
                1.1 百万 GB
               </th>
              </tr>
              <tr>
               <th align="left">
                <strong>
                 每月资源消耗费用
                </strong>
                <br/>
                可计费资源消耗
                <br/>
                资源消耗价格
               </th>
               <th align="left">
                <br/>
                1.1 百万 GB-s
                <br/>
                x ￥ 0.000113/GB-s
               </th>
              </tr>
              <tr>
               <th align="left">
                总成本
               </th>
               <th align="left">
                ￥ 124.3
               </th>
              </tr>
             </tbody>
            </table>
           </div>
           <div class="scroll-table" style="display: block;">
            <h3>
             执行次数计费计算
            </h3>
            <table cellpadding="0" cellspacing="0" id="azure-functions-table-execute-times" width="100%">
             <tbody>
              <tr>
               <th align="left">
                <strong>
                 可计费执行次数
                </strong>
                <br/>
                每月总执行次数
                <br/>
                每月免费执行次数
               </th>
               <th align="left">
                <br/>
                3 百万次执行
                <br/>
                - 1 百万次执行
               </th>
              </tr>
              <tr>
               <th align="left">
                每月可计费执行次数
               </th>
               <th align="left">
                2 百万次执行
               </th>
              </tr>
              <tr>
               <th align="left">
                <strong>
                 每月执行费用
                </strong>
                <br/>
                每月可计费执行次数
                <br/>
                每百万次执行的价格
               </th>
               <th align="left">
                <br/>
                2 百万次执行
                <br/>
                x ￥ 1.3
               </th>
              </tr>
              <tr>
               <th align="left">
                每月执行费用
               </th>
               <th align="left">
                ￥ 2.6
               </th>
              </tr>
             </tbody>
            </table>
           </div>
           <div class="scroll-table" style="display: block;">
            <h3>
             总消耗计费计算
            </h3>
            <table cellpadding="0" cellspacing="0" id="azure-functions-table-total-resource-consumption" width="100%">
             <tbody>
              <tr>
               <th align="left">
                <strong>
                 每月总成本
                </strong>
                <br/>
                每月资源消耗费用
                <br/>
                每月执行费用
               </th>
               <th align="left">
                <br/>
                ￥ 124.3
                <br/>
                + ￥ 2.6
               </th>
              </tr>
              <tr>
               <th align="left">
                每月总成本
               </th>
               <th align="left">
                ￥ 126.9
               </th>
              </tr>
             </tbody>
            </table>
           </div>
          </div>
         </div>
         <!-- END: TAB-CONTAINER-3 -->
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <h3>
         详细信息
        </h3>
        <ul>
         <li>
          也可在
          <a href="../app-service/index.html">
           应用服务虚拟机
          </a>
          上托管 Functions
         </li>
         <li>
          查看
          <a href="https://docs.azure.cn/azure-functions/">
           Azure Functions
          </a>
          文档，了解最新信息
         </li>
         <li>
          Azure Functions
          <a href="https://docs.azure.cn/azure-functions/functions-reference">
           开发人员参考
          </a>
         </li>
        </ul>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证客户订阅中运行的应用将在 99.95% 的时间可用，若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="/support/sla/functions">
          服务级别协议页
         </a>
         。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <!-- END: TAB-CONTROL -->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="EnPL9-3KUf_3ADiG1TeqHOnrOvQuHi49lh0OgdqSrRNjYeuNRQzBUORvNFlI3mV3x6a-2sudL5xT4Zq2xiJSrRxBjboZMDlv-R7timoeF6M1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
