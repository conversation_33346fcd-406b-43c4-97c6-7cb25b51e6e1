<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8" />
  <meta content="IE=edge" http-equiv="X-UA-Compatible" />
  <meta content="width=device-width, initial-scale=1" name="viewport" />
  <meta content="获取 Azure Fluid Relay 服务定价信息。使用 Azure 免费帐户免费试用热门服务，并即付即用，无预付成本。" name="description" />
  <title>
    Azure Fluid Relay - 定价| Microsoft Azure
  </title>
  <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
  <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
  <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
  <link href="../../../../Static/Favicon/manifest.json" rel="manifest" />
  <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
  <meta content="#ffffff" name="theme-color" />
  <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical" />
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet" />
  <link href="../../../../Static/CSS/common.min.css" rel="stylesheet" />
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
  <!-- END: Minified Page Style -->
  <link href="../../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
  <script>
    window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
      IsEnableQrCode: true,
      CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
    @media (min-width: 0) {
      .acn-header-placeholder {
        height: 48px;
        width: 100%;
      }
    }

    @media (min-width: 980px) {
      .acn-header-placeholder {
        height: 89px;
        width: 100%;
      }
    }

    .acn-header-placeholder {
      background-color: #1A1A1A;
    }

    .acn-header-service {
      position: absolute;
      top: 0;
      width: 100%;
    }
  </style>
  <div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <div class="bread-crumb hidden-sm hidden-xs">
            <ul>
              <li>
                <span>
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="single-page">
        <div class="row">
          <div class="col-md-2">
            <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
              <div class="loader">
                <i class="circle">
                </i>
                <i class="circle">
                </i>
                <i class="circle">
                </i>
                <i class="circle">
                </i>
                <i class="circle">
                </i>
              </div>
            </div>
          </div>
          <div class="col-md-10 pure-content">
            <div class="select left-navigation-select hidden-md hidden-lg">
              <select>
                <option selected="selected">
                  加载中...
                </option>
              </select>
              <span class="icon icon-arrow-top">
              </span>
            </div>
            <tags ms.date="09/30/2015" ms.service="fluid-relay" wacn.date="11/27/2015">
            </tags>
            <style type="text/css">
              .pricing-detail-tab .tab-nav {
                padding-left: 0 !important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
              }

              .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
              }

              .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
              }

              .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
              }

              .pricing-detail-tab .tab-content .tab-panel {
                display: none;
              }

              .pricing-detail-tab .tab-content .tab-panel.show-md {
                display: block;
              }

              .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
              }

              .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                color: #FFF;
                background-color: #00a3d9;
              }

              .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                color: #FFF;
                background-color: #00a3d9;
              }

              .pure-content .technical-azure-selector p a,
              .pure-content .technical-azure-selector table a {
                background: 0 0;
                padding: 0;
                margin: 0 6px;
                height: 21px;
                line-height: 22px;
                font-size: 14px;
                color: #00a3d9;
                float: none;
                display: inline;
              }

              .svg {
                width: 50px;
                float: left;
                margin-right: 10px;
              }
            </style>
            <div class="hide-info" style="display:none;">
              <div class="bg-box">
                <div class="cover-bg">
                </div>
              </div>
              <div class="msg-box">
                <div class="pricing-unavailable-message">
                  所选区域不可用
                </div>
              </div>
            </div>
            <div class="common-banner col-top-banner"
              data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
              <div class="common-banner-image">
                <div class="common-banner-title">
                  <div class="svg">
                    <svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                      <defs>
                        <style>
                          .cls-1 {
                            fill: #0078d4;
                          }

                          .cls-2 {
                            fill: #50e6ff;
                          }
                        </style>
                      </defs>
                      <path class="cls-1"
                        d="M1.52,4.25A2.25,2.25,0,0,1,3.77,2h3.5A2.25,2.25,0,0,1,9.52,4.25v3.5A2.25,2.25,0,0,1,7.27,10H3.77A2.25,2.25,0,0,1,1.52,7.75Z"
                        transform="translate(-1.52 -2)" />
                      <path class="cls-1"
                        d="M11.52,14.25A2.25,2.25,0,0,1,13.77,12h5.5a2.25,2.25,0,0,1,2.25,2.25v5.5A2.25,2.25,0,0,1,19.27,22h-5.5a2.25,2.25,0,0,1-2.25-2.25Z"
                        transform="translate(-1.52 -2)" />
                      <path class="cls-2"
                        d="M16.31,5H10.52V8.19A2.81,2.81,0,0,1,7.7,11H4.52v5.79A2.19,2.19,0,0,0,6.72,19h3.82V13.71A2.69,2.69,0,0,1,13.23,11h5.28V7.2A2.19,2.19,0,0,0,16.31,5Z"
                        transform="translate(-1.52 -2)" />
                    </svg>
                  </div>
                  <h2>
                    Azure Fluid Relay定价
                  </h2>
                </div>
              </div>
            </div>
            <div class="pricing-page-section">
              <h4>
                使用 Fluid Framework 轻松将实时协作体验添加到应用
              </h4>
              <p>
                Azure Fluid Relay 服务可帮助开发人员通过无缝共同创作和数据同步构建实时协作体验。现在可以在客户端之间实时复制。
              </p>
              <p>
                使用即用即付定价构建安全、合规的应用，并进行自动缩放和优化。
              </p>
                <h2>
                  定价详细信息
                </h2>
            </div>
              <!-- BEGIN: TAB-CONTROL -->
              <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                <div class="tab-container-container">
                  <div class="tab-container-box">
                    <div class="tab-container">
                      <div class="dropdown-container software-kind-container" style="display:none;">
                        <label>
                          OS/软件:
                        </label>
                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                          <span class="selected-item">
                            Fluid Relay
                          </span>
                          <i class="icon">
                          </i>
                          <ol class="tab-items">
                            <li class="active">
                              <a data-href="#tabContent1" href="javascript:void(0)" id="home_managed-disks">
                                Fluid Relay
                              </a>
                            </li>
                          </ol>
                        </div>
                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                          <option data-href="#tabContent1" selected="selected" value="Fluid Relay">
                            Fluid Relay
                          </option>
                        </select>
                      </div>
                      <div class="dropdown-container region-container">
                        <label>
                          地区:
                        </label>
                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                          <span class="selected-item">
                            中国北部 3
                          </span>
                          <i class="icon">
                          </i>
                          <ol class="tab-items">
                            <li class="active">
                              <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                                中国北部3
                              </a>
                            </li>
                          </ol>
                        </div>
                        <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                          <option data-href="#north-china3" selected="selected" value="north-china3">
                            中国北部 3
                          </option>
                        </select>
                      </div>
                      <div class="clearfix">
                      </div>
                    </div>
                  </div>
                </div>
                <!-- BEGIN: TAB-CONTAINER-1 -->
                <div class="tab-content">
                  <!-- BEGIN: TAB-CONTENT-1 -->
                  <div class="tab-panel" id="tabContent1">
                    <!-- BEGIN: Tab level 2 navigator 2 -->
                    <!-- BEGIN: Tab level 2 content 3 -->
                    <ul class="tab-nav" style="display:none">
                      <li class="active">
                        <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)" id="gpv1">
                          常规用途 v1
                        </a>
                      </li>
                    </ul>
                    <div class="tab-content">
                      <!-- BEGIN: Table1-Content-->
                      <div class="tab-panel" id="tabContent2">
                        <h4>
                          标准
                        </h4>
                        <p>“标准”服务计划提供跨区域复原能力(意味着发生区域性中断时可用性更高)和会话路由(意味着改进了最终用户体验和 SLA 保证)。</p>
                        <table cellpadding="0" cellspacing="0" width="100%">
                          <tr>
                            <th align="left" style="width: 652px;">

                            </th>
                            <th align="left">
                              <strong>
                                价格
                              </strong>
                            </th>
                          </tr>
                          <tr>
                            <td>
                              操作输入
                            </td>
                            <td>
                              每 100 万次操作 ￥15.264
                            </td>
                          </tr>
                          <tr>
                            <td>操作输出</td>
                            <td>每 100 万次操作 ￥5.088</td>
                          </tr>
                          <tr>
                            <td>客户端连接分钟数</td>
                            <td>每 100 万个客户端连接分钟数 ￥15.264</td>
                          </tr>
                          <tr>
                            <td>存储</td>
                            <td>￥2.0352 每月每 GB</td>
                          </tr>
                        </table>
                      </div>
                      <!-- END: Table1-Content-->
                    </div>
                  </div>
                  <!-- END: TAB-CONTENT-3 -->
                </div>
                <!-- END: TAB-CONTAINER-1 -->
              </div>

              <!-- END: TAB-CONTROL -->
              <!-- 修改常见问题 -->
              <div class="pricing-page-section" style="display: none;">
                <div class="more-detail">
                  <h2>
                    常见问题
                  </h2>
                  <em>
                    全部展开
                  </em>
                  <ul>
                    <li>
                      <i class="icon icon-plus">
                      </i>
                      <div>
                        <a id="Storage_question14">
                          不足 1 小时是否按 1 小时计费？
                        </a>
                        <section>
                          <p>
                            是，不足 1 小时按 1 小时计费。
                          </p>
                        </section>
                      </div>
                    </li>
                    <li>
                      <i class="icon icon-plus">
                      </i>
                      <div>
                        <a id="Storage_question15">
                          Azure 防火墙管理器如何定价？
                        </a>
                        <section>
                          <p>
                            客户至少需支付一项基准策略的费用。根据区域数量，每个区域按各区域 ￥636 每区域每个策略 收费。如果子策略位于单个中心，则不收费，但基准策略仍按 ￥636 每区域每个策略 收费。
                          </p>
                        </section>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <!--END: Support and service code chunk-->
              <div class="pricing-page-section" style="display: none;">
                <h2>
                  支持和服务级别协议
                </h2>
                <p>
                  如有任何疑问或需要帮助，请访问
                  <a href="https://support.azure.cn/zh-cn/support/contact" id="storage-contact-page">
                    Azure 支持
                  </a>
                  选择自助服务或者其他任何方式联系我们获得支持。
                </p>
                <p>
                  Azure 防火墙管理器可用性保证已纳入所选部署选项（Azure 防火墙、VPN 网关和虚拟 vWAN）的 SLA 中。有关更多详细信息，请参阅 Azure 防火墙 SLA、VPN 网关和 Azure
                  虚拟 WAN。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
      function getAntiForgeryToken() {
        var token =
          '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
        token = $(token).val();
        return token;
      }

      function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
          .toGMTString();
      }

      setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
      var MARKETING_STORAGE = '/blob/marketing-resource/Content';
      var TECHNICAL_STORAGE = '/blob/tech-content/';
      var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
      var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
      (function () {
        var config = {
          cookiesToCollect: ["_mkto_trk"],
          syncMuid: true,
          ix: {
            a: true,
            g: true
          },
          coreData: {
            appId: 'AzureCN',
            market: 'zh-cn',
          }
        };
        awa.init(config);
      })();
    </script>
    <!-- end JSLL -->
    <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
  </div>
</body>

</html>