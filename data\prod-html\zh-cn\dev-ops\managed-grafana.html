<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <meta content="查看 Azure 托管 Grafana 的定价。" name="description"/>
  <title>
    定价详细信息 - 托管 Grafana | Microsoft Azure
  </title>
  <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
		window.currentLocale = "zh-CN";
		window.headerTimestamp = "2019/1/23 8:25:24";
		window.footerTimestamp = "2019/1/8 8:07:06";
		window.locFileTimestamp = "2018/11/29 7:49:17";

		window.AcnHeaderFooter = {
			IsEnableQrCode: true,
			CurrentLang: window.currentLocale.toLowerCase(),
		};
  </script>
  <style>
   @media (min-width: 0) {
			.acn-header-placeholder {
				height: 48px;
				width: 100%;
			}
		}

		@media (min-width: 980px) {
			.acn-header-placeholder {
				height: 89px;
				width: 100%;
			}
		}

		.acn-header-placeholder {
			background-color: #1A1A1A;
		}

		.acn-header-service {
			position: absolute;
			top: 0;
			width: 100%;
		}
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="managed-grafana" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav {
								padding-left: 0 !important;
								margin-top: 5px;
								margin-bottom: 0;
								overflow: hidden;
							}

							.pricing-detail-tab .tab-nav li {
								list-style: none;
								float: left;
							}

							.pricing-detail-tab .tab-nav li.active a {
								border-bottom: 4px solid #00a3d9;
							}

							.pricing-detail-tab .tab-nav li.active a:hover {
								border-bottom: 4px solid #00a3d9;
							}

							.pricing-detail-tab .tab-content .tab-panel {
								display: none;
							}

							.pricing-detail-tab .tab-content .tab-panel.show-md {
								display: block;
							}

							.pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
								padding-left: 5px;
								padding-right: 5px;
								color: #00a3d9;
								background-color: #FFF;
							}

							.pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
								color: #FFF;
								background-color: #00a3d9;
							}

							.pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
								color: #FFF;
								background-color: #00a3d9;
							}

							.pure-content .technical-azure-selector p a,
							.pure-content .technical-azure-selector table a {
								background: 0 0;
								padding: 0;
								margin: 0 6px;
								height: 21px;
								line-height: 22px;
								font-size: 14px;
								color: #00a3d9;
								float: none;
								display: inline;
							}
							.svg{
              					 width: 50px;
               					 float: left;
              					 margin-right: 10px;
           						}
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <div class="svg">
            <?xml version="1.0" encoding="utf-8"?>
            <!-- Generator: Adobe Illustrator 24.2.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
            <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
               viewBox="0 0 85.1 92.5" style="enable-background:new 0 0 85.1 92.5;" xml:space="preserve">
            <style type="text/css">
              .st0{fill:url(#SVGID_1_);}
            </style>
            <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="57.5" y1="17.382" x2="57.5" y2="99.76">
                <stop offset="0.18" stop-color="#5ea0ef"/>
                <stop offset="1" stop-color="#0078d4"/>
            </linearGradient>
            <path class="st0" d="M85,40.8c-0.1-1.6-0.4-3.3-0.9-5.3c-0.5-2-1.3-4.1-2.4-6.4c-1.1-2.2-2.6-4.6-4.5-6.8c-0.7-0.9-1.5-1.8-2.4-2.6
              c1.3-5.2-1.6-9.7-1.6-9.7c-5-0.3-8.1,1.5-9.3,2.4c-0.2-0.1-0.4-0.2-0.6-0.2c-0.8-0.3-1.7-0.7-2.6-0.9c-0.9-0.3-1.8-0.5-2.7-0.8
              c-0.9-0.2-1.9-0.4-2.9-0.6c-0.2,0-0.3,0-0.5-0.1C52.4,2.9,46.2,0,46.2,0c-6.9,4.4-8.3,10.6-8.3,10.6s0,0.1-0.1,0.4
              c-0.4,0.1-0.8,0.2-1.2,0.3c-0.5,0.2-1.1,0.4-1.6,0.5c-0.5,0.2-1.1,0.4-1.6,0.6c-1,0.5-2.1,1-3.1,1.5c-1,0.6-2,1.2-2.9,1.8
              c-0.1-0.1-0.2-0.1-0.2-0.1C17.6,12,9.1,16.5,9.1,16.5c-0.8,10.2,3.8,16.7,4.8,17.9c-0.2,0.6-0.4,1.3-0.6,1.9
              c-0.7,2.3-1.2,4.7-1.6,7.2c0,0.4-0.1,0.7-0.1,1.1C2.6,48.8,0,57.8,0,57.8c7.4,8.5,16.1,9.1,16.1,9.1c0,0,0,0,0,0
              c1.1,2,2.4,3.8,3.8,5.6c0.6,0.7,1.2,1.4,1.9,2.1c-2.7,7.7,0.4,14.2,0.4,14.2c8.3,0.3,13.7-3.6,14.8-4.5c0.8,0.3,1.7,0.5,2.5,0.7
              c2.5,0.7,5.1,1,7.7,1.2c0.6,0,1.3,0,1.9,0l0.3,0l0.2,0l0.4,0l0.4,0l0,0c3.9,5.6,10.7,6.3,10.7,6.3c4.9-5.1,5.1-10.2,5.1-11.3l0,0
              c0,0,0,0,0-0.1c0-0.1,0-0.2,0-0.2s0,0,0,0c0-0.1,0-0.2,0-0.2c1-0.7,2-1.5,2.9-2.3c1.9-1.8,3.7-3.8,5.1-5.9c0.1-0.2,0.3-0.4,0.4-0.6
              c5.5,0.3,9.4-3.4,9.4-3.4c-0.9-5.7-4.2-8.5-4.9-9.1l0,0c0,0,0,0-0.1-0.1c0,0-0.1,0-0.1,0l0,0c0,0-0.1,0-0.1-0.1c0-0.3,0.1-0.7,0.1-1
              c0-0.6,0.1-1.2,0.1-1.9l0-0.5l0-0.2l0-0.1c0-0.2,0-0.1,0-0.2l0-0.4l0-0.5c0-0.2,0-0.3,0-0.5c0-0.2,0-0.3,0-0.5L79,52.9l-0.1-0.5
              c-0.1-0.6-0.2-1.3-0.4-1.9c-0.6-2.5-1.5-4.8-2.8-6.9c-1.3-2.1-2.9-4-4.7-5.6c-1.8-1.6-3.8-2.8-5.9-3.8c-2.1-0.9-4.3-1.6-6.5-1.8
              c-1.1-0.1-2.2-0.2-3.3-0.2l-0.4,0l-0.1,0c0,0-0.2,0-0.1,0l-0.2,0l-0.4,0c-0.2,0-0.3,0-0.4,0c-0.6,0.1-1.1,0.1-1.7,0.2
              c-2.2,0.4-4.2,1.2-6.1,2.3c-1.8,1.1-3.4,2.4-4.7,4c-1.3,1.5-2.3,3.2-3,5c-0.7,1.8-1.1,3.6-1.2,5.4c0,0.4,0,0.9,0,1.3
              c0,0.1,0,0.2,0,0.3l0,0.4c0,0.2,0,0.4,0,0.6c0.1,0.9,0.3,1.8,0.5,2.6c0.5,1.7,1.2,3.2,2.2,4.4c0.9,1.3,2.1,2.3,3.3,3.2
              c1.2,0.8,2.5,1.4,3.8,1.8c1.3,0.4,2.5,0.5,3.7,0.5c0.1,0,0.3,0,0.4,0c0.1,0,0.2,0,0.2,0c0.1,0,0.2,0,0.2,0c0.1,0,0.3,0,0.4,0
              c0,0,0.1,0,0.1,0l0.1,0c0.1,0,0.2,0,0.2,0c0.2,0,0.3,0,0.4-0.1c0.1,0,0.3-0.1,0.4-0.1c0.3-0.1,0.5-0.1,0.8-0.2
              c0.5-0.2,1-0.4,1.5-0.6c0.5-0.2,0.9-0.5,1.3-0.8c0.1-0.1,0.2-0.2,0.3-0.2c0.4-0.3,0.5-0.9,0.1-1.3c-0.3-0.4-0.8-0.5-1.2-0.2
              c-0.1,0.1-0.2,0.1-0.3,0.2c-0.3,0.2-0.7,0.3-1.1,0.4c-0.4,0.1-0.8,0.2-1.2,0.3c-0.2,0-0.4,0.1-0.6,0.1c-0.1,0-0.2,0-0.3,0
              c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.3,0-0.4,0c0,0-0.1,0,0,0l0,0l-0.1,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2,0-0.3,0
              c-0.9-0.1-1.9-0.4-2.8-0.8c-0.9-0.4-1.8-1-2.6-1.7c-0.8-0.7-1.5-1.6-2-2.5c-0.5-1-0.9-2-1.1-3.2c-0.1-0.6-0.1-1.1-0.1-1.7
              c0-0.2,0-0.3,0-0.5c0,0,0,0,0,0l0-0.1l0-0.1c0-0.1,0-0.2,0-0.2c0-0.3,0.1-0.6,0.1-0.9c0.4-2.4,1.7-4.8,3.6-6.6
              c0.5-0.5,1-0.9,1.5-1.2c0.5-0.4,1.1-0.7,1.7-1c0.6-0.3,1.2-0.5,1.9-0.7c0.6-0.2,1.3-0.3,2-0.3c0.3,0,0.7,0,1,0c0.1,0,0.2,0,0.2,0
              l0.3,0l0.2,0c0.1,0,0,0,0,0l0.1,0l0.3,0c0.7,0.1,1.5,0.2,2.2,0.3c1.4,0.3,2.8,0.8,4.1,1.6c2.6,1.4,4.8,3.7,6.2,6.4
              c0.7,1.4,1.2,2.8,1.4,4.3c0.1,0.4,0.1,0.8,0.1,1.1l0,0.3l0,0.3c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3l0,0.2l0,0.3c0,0.2,0,0.5,0,0.7
              c0,0.4-0.1,0.8-0.1,1.2c0,0.4-0.1,0.8-0.2,1.2c-0.1,0.4-0.2,0.8-0.3,1.2c-0.2,0.8-0.5,1.6-0.8,2.4c-0.6,1.5-1.4,3-2.4,4.4
              c-2,2.7-4.6,4.9-7.7,6.3c-1.5,0.7-3.1,1.2-4.8,1.5c-0.8,0.1-1.7,0.2-2.5,0.3l-0.2,0l-0.1,0l-0.3,0l-0.4,0l-0.2,0c0.1,0,0,0,0,0
              l-0.1,0c-0.4,0-0.9,0-1.3-0.1c-1.8-0.1-3.6-0.5-5.3-1c-1.7-0.5-3.4-1.2-4.9-2c-3.1-1.7-6-4-8.2-6.8c-1.1-1.4-2.1-2.9-2.9-4.4
              c-0.8-1.6-1.4-3.2-1.9-4.9c-0.5-1.7-0.7-3.4-0.9-5.1l0-0.3l0-0.1l0-0.1l0-0.1l0-0.3l0-0.1l0-0.1l0-0.2l0-0.4l0-0.1c0,0,0,0,0,0
              l0-0.2c0-0.2,0-0.4,0-0.6c0-0.9,0.1-1.7,0.2-2.6c0.1-0.9,0.3-1.8,0.4-2.6c0.2-0.9,0.4-1.7,0.6-2.6c0.5-1.7,1.1-3.4,1.8-4.9
              c1.4-3.1,3.3-5.9,5.6-8.1c0.6-0.6,1.2-1.1,1.8-1.6c0.6-0.5,1.2-0.9,1.9-1.4c0.6-0.4,1.3-0.8,2-1.2c0.3-0.2,0.7-0.3,1-0.5
              c0.2-0.1,0.4-0.2,0.5-0.2c0.2-0.1,0.4-0.2,0.5-0.2c0.7-0.3,1.5-0.6,2.2-0.8c0.2-0.1,0.4-0.1,0.6-0.2c0.2-0.1,0.4-0.1,0.6-0.2
              c0.4-0.1,0.8-0.2,1.1-0.3c0.2-0.1,0.4-0.1,0.6-0.1c0.2,0,0.4-0.1,0.6-0.1c0.2,0,0.4-0.1,0.6-0.1l0.3-0.1l0.3,0
              c0.2,0,0.4-0.1,0.6-0.1c0.2,0,0.4-0.1,0.7-0.1c0.2,0,0.5-0.1,0.7-0.1c0.1,0,0.3,0,0.4,0l0.3,0l0.1,0l0.2,0c0.2,0,0.4,0,0.7,0l0.3,0
              c0,0,0.1,0,0,0l0.1,0l0.1,0c0.2,0,0.4,0,0.6,0c0.8,0,1.5,0,2.2,0c1.5,0.1,2.9,0.2,4.3,0.5c2.8,0.5,5.5,1.4,7.9,2.6
              c2.4,1.2,4.6,2.6,6.4,4.2c0.1,0.1,0.2,0.2,0.3,0.3c0.1,0.1,0.2,0.2,0.3,0.3c0.2,0.2,0.4,0.4,0.7,0.6c0.2,0.2,0.4,0.4,0.6,0.6
              c0.2,0.2,0.4,0.4,0.6,0.6c0.8,0.8,1.5,1.7,2.2,2.6c1.3,1.7,2.4,3.4,3.2,5.1c0.1,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.1,0.2,0.1,0.3
              c0.1,0.2,0.2,0.4,0.3,0.6c0.1,0.2,0.2,0.4,0.3,0.6c0.1,0.2,0.2,0.4,0.3,0.6c0.3,0.8,0.6,1.5,0.8,2.2c0.4,1.1,0.7,2.1,0.9,3
              c0.1,0.3,0.4,0.6,0.8,0.5c0.4,0,0.7-0.3,0.7-0.7C85.1,43.1,85.1,42,85,40.8z"/>
            </svg>
          </div>
          <h2>
            Azure 托管 Grafana定价
          </h2>
          <p style="color: #000 !important;">将 Grafana 仪表板部署为完全托管的 Azure 服务，作为统一可观测性解决方案的一部分</p>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <h4>
          将 Grafana 仪表板部署为完全托管的 Azure 服务，作为统一可观测性解决方案的一部分
        </h4>
        <p>
          Azure 托管 Grafana 是一款基于 Grafana Pro 软件生成的丰富且可扩展的可视化应用程序。作为完全托管的 Azure 服务，可以轻松将 Grafana 部署为可观测性解决方案的一部分并创建统一仪表板，从而监视 Azure Monitor、Azure 数据资源管理器和其他数据存储中的数据。
        </p>
      
        <div class="pricing-page-section">
         <h2>
          定价详细信息
         </h2>
        </div>
        <!-- BEGIN: TAB-CONTROL -->
        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
         <div class="tab-container-container">
          <div class="tab-container-box">
           <div class="tab-container">
            <div class="dropdown-container region-container">
             <label>
              地区:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               中国北部 3
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
                <li class="active">
                  <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                    中国东部 3
                  </a>
                 </li>
                <li class="active">
                  <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                  中国北部3
                  </a>
                </li>
              </ol>
             </div>
             <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
              <option data-href="#east-china3" selected="selected" value="east-china3">
                中国东部 3
              </option>
              <option data-href="#north-china3" selected="selected" value="north-china3">
                中国北部 3
               </option>
             </select>
            </div>
            <div class="clearfix">
            </div>
           </div>
          </div>
         </div>
        
          <!-- BEGIN: Table1-Content-->
          <div>
           <h4>
            标准
           </h4>
           <p>“标准”服务计划提供跨区域复原能力(意味着发生区域性中断时可用性更高)和会话路由(意味着改进了最终用户体验和 SLA 保证)。</p>
           <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
             <th align="left">
              <strong>类型</strong>
             </th>
             <th align="left">
              <strong>
                Essential (预览版)
              </strong>
             </th>
             <th align="left">
              <strong>
                标准
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              实例*
             </td>
             <td>
              n/a
             </td>
             <td>
              ￥0.7 每小时 (默认为每个实例 2 个节点)
             </td>
            </tr>
            <tr>
              <td>活跃用户**</td>
              <td>每月 ￥38.16</td>
              <td>每月 ￥38.16</td>
            </tr>
            <tr>
              <td>区域冗余</td>
              <td>n/a</td>
              <td>￥0.418236每小时</td>
            </tr>
            <tr>
              <td>Grafana Enterprise 许可证</td>
              <td>n/a</td>
              <td><a href="https://aka.ms/managed-grafana/Enterprise/licensing">许可成本详细信息</a></td>
            </tr>
           </table>

           <p>*新 Grafana 实例在前 30 天内免费(每个 Azure 订阅仅限一个实例)。</p>
           <p>**活跃用户定义为在日历月中已访问 Grafana 实例的唯一用户、Grafana 服务帐户或 API 密钥。</p>
          </div>
         
       
         <!-- END: TAB-CONTAINER-1 -->
        </div>
        <!-- END: TAB-CONTROL -->
        <!-- 修改常见问题 -->
        <div class="pricing-page-section" style="display: none;">
         <div class="more-detail">
          <h2>
           常见问题
          </h2>
          <em>
           全部展开
          </em>
          <ul>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question14">
              不足 1 小时是否按 1 小时计费？
             </a>
             <section>
              <p>
               是，不足 1 小时按 1 小时计费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question15">
              Azure 防火墙管理器如何定价？
             </a>
             <section>
              <p>
               客户至少需支付一项基准策略的费用。根据区域数量，每个区域按各区域 ￥636 每区域每个策略 收费。如果子策略位于单个中心，则不收费，但基准策略仍按 ￥636 每区域每个策略 收费。
              </p>
             </section>
            </div>
           </li>
          </ul>
         </div>
        </div>
        <!--END: Support and service code chunk-->
        <div class="pricing-page-section" style="display: none;">
         <h2>
          支持和服务级别协议
         </h2>
         <p>
          如有任何疑问或需要帮助，请访问
          <a href="https://support.azure.cn/zh-cn/support/contact" id="storage-contact-page">
           Azure 支持
          </a>
          选择自助服务或者其他任何方式联系我们获得支持。
         </p>
         <p>
          Azure 防火墙管理器可用性保证已纳入所选部署选项（Azure 防火墙、VPN 网关和虚拟 vWAN）的 SLA 中。有关更多详细信息，请参阅 Azure 防火墙 SLA、VPN 网关和 Azure 虚拟 WAN。
         </p>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
   <!-- END: Documentation Content -->
   <!-- BEGIN: Footer -->
   <div class="public_footerpage">
   </div>
   <!--END: Common sidebar-->
   <link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
   <script type="text/javascript">
    function getAntiForgeryToken() {
				var token =
					'<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
				token = $(token).val();
				return token;
			}

			function setLocaleCookie(localeVal) {
				var Days = 365 * 10; // Ten year expiration
				var exp = new Date();

				var hostName = window.location.hostname;
				var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

				exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
				document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
					.toGMTString();
			}

			setLocaleCookie(window.currentLocale);
   </script>
   <script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
			var TECHNICAL_STORAGE = '/blob/tech-content/';
			var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
			var EnablePricingSync = 'false';
   </script>
   <!-- BEGIN: Minified RequireJs -->
   <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
   </script>
   <script src="../../../../Static/Scripts/require.js" type="text/javascript">
   </script>
   <script src="../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
   </script>
   <!-- END: Minified RequireJs -->
   <!-- begin JSLL -->
   <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
   </script>
   <script type="text/javascript">
    (function () {
				var config = {
					cookiesToCollect: ["_mkto_trk"],
					syncMuid: true,
					ix: {
						a: true,
						g: true
					},
					coreData: {
						appId: 'AzureCN',
						market: 'zh-cn',
					}
				};
				awa.init(config);
			})();
   </script>
   <!-- end JSLL -->
   <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
   </script>
   <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
   </script>
   <script src="/common/useCommon.js" type="text/javascript">
   </script>
  </div>
 </body>
</html>
