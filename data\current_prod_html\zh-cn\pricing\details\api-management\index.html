<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="" name="keywords"/>
  <meta content="在几分钟内发布、管理、保证安全并分析你的 API " name="description"/>
  <title>
   API 管理定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/api-management/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
   @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
  </style>
  <div class="hide-info" style="display:none;">
   <div class="bg-box">
    <div class="cover-bg">
    </div>
   </div>
   <div class="msg-box">
    <div class="pricing-unavailable-message">
     所选区域不可用
    </div>
   </div>
  </div>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="api-management" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/product-banner-api-01.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/images/<EMAIL>"/>
          <h2>
           API 管理
           <span>
            API Management
           </span>
          </h2>
          <h4>
           安全、大规模地向开发人员、合作伙伴和员工发布 API
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         借助于 Azure API 管理，组织可以更安全、可靠以及大规模地发布 API。使用 API 管理可促使内部团队、合作伙伴和开发人员使用
                            API，同时可以从管理门户提供的业务和日志分析中受益。此服务有助于提供组织针对 API 端到端管理所需的工具 ——
                            涵盖从预配用户角色、创建使用计划和配额、应用用于转换有效负载的策略到限制、分析、监视和警报的方方面面。
        </p>
       </div>
       <h2>
        定价详细信息
       </h2>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              API Management
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_storage-blobs">
                API Management
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="API Management">
              API Management
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部3
               </a>
              </li>
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#north-china3" value="north-china3">
              中国北部3
             </option>
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <div class="tab-content">
         <div class="tab-panel" id="tabContent1">
          <div class="pricing-page-section" style="margin-top: 0px;">
           <!-- BEGIN: Table1-Content-->
           <h2>
            API 管理
           </h2>
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
            <br/>
            <!-- <div class="ms-date">*以下价格表中的新价格及基本定价层将于 2018 年 3 月 1 日正式生效。 </div> -->
           </div>
           <table cellpadding="0" cellspacing="0" id="API-Management-preview" width="100%">
            <tr>
             <th align="left">
              <strong>
              </strong>
             </th>
             <th align="left">
              <strong>
               开发人员
              </strong>
             </th>
             <th align="left">
              <strong>
               基本
              </strong>
             </th>
             <th align="left">
              <strong>
               标准
              </strong>
             </th>
             <th align="left">
              <strong>
               高级
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              用途
             </td>
             <td>
              非生产用例和评估
             </td>
             <td>
              入门级的生产用例
             </td>
             <td>
              中等数量生产用例
             </td>
             <td>
              大批量或企业生产用例
             </td>
            </tr>
            <tr>
             <td>
              价格 (每单位)
             </td>
             <td>
              ￥ 0.5022/ 小时 （约￥ 373.6368 /月）
             </td>
             <td>
              ￥ 1.5386/ 小时 （约￥ 1,144.7184 /月）
             </td>
             <td>
              ￥ 7.1794/ 小时 （约￥ 5,341.4736 /月）
             </td>
             <td>
              ￥ 29.2229/ 小时 （约￥ 21,741.8376 /月）
              <br/>
              增量单位（大于 1 个）的单位成本
              <br/>
              是所购买的第一个单位的价格的 50%
             </td>
            </tr>
            <tr>
             <td>
              缓存（每单位）
             </td>
             <td>
              10 MB
             </td>
             <td>
              50 MB
             </td>
             <td>
              1 GB
             </td>
             <td>
              5 GB
             </td>
            </tr>
            <tr>
             <td>
              横向扩展（单位）
             </td>
             <td>
              1
             </td>
             <td>
              2
             </td>
             <td>
              4
             </td>
             <td>
              每个区域 10 个（调用支持添加更多）
             </td>
            </tr>
            <tr>
             <td>
              SLA
             </td>
             <td>
              否
             </td>
             <td>
              99.9%
             </td>
             <td>
              99.9%
             </td>
             <td>
              99.95%
              <sup>
               1
              </sup>
             </td>
            </tr>
            <tr>
             <td>
              使用限制
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
            </tr>
            <tr>
             <td>
              缓存，外部
              <sup>
               6
              </sup>
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              开发人员门户
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              多个自定义域名
              <sup>
               5
              </sup>
             </td>
             <td>
              是
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              Azure Active Directory 集成
             </td>
             <td>
              是
             </td>
             <td>
              否
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              虚拟网络支持
             </td>
             <td>
              是
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              多区域部署
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              自托管网关
             </td>
             <td>
              是
              <sup>
               3
              </sup>
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              是
              <sup>
               4
              </sup>
             </td>
            </tr>

            <tr>
                <td>
                 工作区
                </td>
                <td>
                 否
                </td>
                <td>
                 否
                </td>
                <td>
                 否
                </td>
                <td>
                 是
                </td>
               </tr>

            <tr>
             <td>
              预计最大吞吐量
              <sup>
               2
              </sup>
              （每单位）
             </td>
             <td>
              500 个请求/秒
             </td>
             <td>
              1,000 个请求/秒
             </td>
             <td>
              2,500 个请求/秒
             </td>
             <td>
              4,000 个请求/秒
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="API-Management-preview2" width="100%">
            <tr>
             <th align="left">
              <strong>
              </strong>
             </th>
             <th align="left">
              <strong>
               开发人员
              </strong>
             </th>
             <th align="left">
              <strong>
               基本
              </strong>
             </th>
             <th align="left">
              <strong>
               标准
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              用途
             </td>
             <td>
              非生产用例和评估
             </td>
             <td>
              入门级的生产用例
             </td>
             <td>
              中等数量生产用例
             </td>
            </tr>
            <tr>
             <td>
              价格 (每单位)
             </td>
             <td>
              ￥ 0.5022/ 小时 （约￥ 373.6368 /月）
             </td>
             <td>
              ￥ 1.5386/ 小时 （约￥ 1,144.7184 /月）
             </td>
             <td>
              ￥ 7.1794/ 小时 （约￥ 5,341.4736 /月）
             </td>
            </tr>
            <tr>
             <td>
              缓存（每单位）
             </td>
             <td>
              10 MB
             </td>
             <td>
              50 MB
             </td>
             <td>
              1 GB
             </td>
            </tr>
            <tr>
             <td>
              横向扩展（单位）
             </td>
             <td>
              1
             </td>
             <td>
              2
             </td>
             <td>
              4
             </td>
            </tr>
            <tr>
             <td>
              SLA
             </td>
             <td>
              否
             </td>
             <td>
              99.9%
             </td>
             <td>
              99.9%
             </td>
            </tr>
            <tr>
             <td>
              使用限制
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
            </tr>
            <tr>
             <td>
              缓存，外部
              <sup>
               6
              </sup>
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              开发人员门户
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              多个自定义域名
              <sup>
               5
              </sup>
             </td>
             <td>
              是
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
            </tr>
            <tr>
             <td>
              Azure Active Directory 集成
             </td>
             <td>
              是
             </td>
             <td>
              否
             </td>
             <td>
              是
             </td>
            </tr>
            <tr>
             <td>
              虚拟网络支持
             </td>
             <td>
              是
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
            </tr>
            <tr>
             <td>
              多区域部署
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
            </tr>
            <tr>
             <td>
              自托管网关
             </td>
             <td>
              是
              <sup>
               3
              </sup>
             </td>
             <td>
              否
             </td>
             <td>
              否
             </td>
            </tr>
            <tr>
             <td>
              预计最大吞吐量
              <sup>
               2
              </sup>
              （每单位）
             </td>
             <td>
              500 个请求/秒
             </td>
             <td>
              1,000 个请求/秒
             </td>
             <td>
              2,500 个请求/秒
             </td>
            </tr>
           </table>
           <div class="tags-date">
            <p>
            <div class="ms-date">
            <sup>
             1
            </sup>
            要求在两个或更多区域至少部署一个单位。
            </div>    
            </p>
            <p>
            <div class="ms-date">
            <sup>
             2
            </sup>
            吞吐量数据仅供参考，不得作为容量和预算规划的依据。必须执行反映预期生产条件的负载测试，以准确确定预期吞吐量。
            </div>    
            </p>
            <p>
            <div class="ms-date">
            <sup>
             3
            </sup>
            在开发者层中，无需额外付费即可使用自承载网关功能。网关部署的数量不受限制，但每个部署只能使用一个网关副本(实例)。 
            </div>    
            </p>
            <p>
            <div class="ms-date">
            <sup>
             4
            </sup>
            在高级层中，可以通过额外付费(见下文)使用自承载网关功能。每个部署中的网关副本数(实例)不受限制。 
            </div>    
            </p>
            <p>
            <div class="ms-date">
           <sup>
             5
            </sup>
            仅适用于网关。每个实例限制为 20 个域名。请 
            <a href="https://support.azure.cn/zh-cn/support/contact/" style="float: none; font-size: 12px ; display: inline; background-color: white; margin:0; padding:0;" aria-label="aLabel" target="_blank">
             致电支持人员
            </a>
            以添加更多内容。
            </div>
            </p>
            <p>
            <div class="ms-date">
            <sup>
             6
            </sup>
            请使用自己的 Redis 兼容缓存，例如 Azure Redis 缓存。
            </div>
            </p>
            </div>
            <h2>Gateway</h2>
            <p>The gateway resource ensures runtime isolation and supports hybrid and multi-cloud API deployments. 
                <a href="https://docs.azure.cn/zh-cn/api-management/api-management-gateways-overview" style="float: none; font-size: 15px ; display: inline; background-color: white; margin:0; padding:0;" aria-label="aLabel" target="_blank">Learn more about gateways.</a>
            </p>
            <br/>
            <table cellpadding="0" cellspacing="0" id="API-Management-gateway" width="100%">
                <tr>
                 <th align="left">
                  <strong>
                  </strong>
                 </th>
                 <th align="left">
                  <strong>
                    Workspace gateway premium
                  </strong>
                 </th>
                 <th align="left">
                  <strong>
                    自托管网关
                  </strong>
                 </th>
                </tr>
                <tr>
                 <td>
                  用途
                 </td>
                 <td>
                    Use as a managed data plane for workspaces in the Premium tier.
                 </td>
                 <td>
                    Use as a self-hosted data plane for APIs deployed on premises or in other private and public clouds.
                 </td>
                </tr>
                <tr>
                 <td>
                  价格
                 </td>
                 <td>
                  ￥ 12,235.08 per month per gateway unit
                 </td>
                 <td>
                  ￥ 2,060.88/月/网关部署
                  <sup>1</sup>
                 </td>
                </tr>
                <tr>
                 <td>
                    Workspaces
                 </td>
                 <td>
                    ￥647.28 for 5 workspaces per month<sup>2</sup>
                 </td>
                 <td>
                    不适用
                 </td>
                </tr>
            </table>
            <div class="tags-date">
                <p>
                <div class="ms-date">
                <sup>
                 1
                </sup>
                Developer tier deployments are free of charge.
                </div>    
                </p>
                <p>
                    <div class="ms-date">
                        <sup>
                            2
                           </sup>
                           The first five workspaces are included at no additional cost.
                    </div>
                </p>
        </div>
          </div>
          <div class="pricing-page-section">
           <!-- BEGIN: Table1-Content-->
<!--            <h2>
            自托管网关
           </h2> -->
<!--            <p>
            自托管网关功能扩展了对混合和多云环境的API管理支持。它允许客户通过一个API管理解决方案来管理所有API，而不会损害安全性、法规遵从性或性能。使用自托管网关功能，组织可以将容器化版本的API管理网关组件部署到托管其API的相同环境中，同时继续从Azure中关联的API管理服务管理它们。
           </p> -->
<!--            <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
            <br/>
             <div class="ms-date">*以下价格表中的新价格及基本定价层将于 2018 年 3 月 1 日正式生效。 </div> 
           </div> -->
<!--            <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
             <th align="left">
              价格
             </th>
             <th align="left">
              ￥8.71/小时/网关部署
             </th>
            </tr>
           </table> -->
<!--            <p>
            以上价格将于2020年7月1日开始生效。
           </p> -->
          </div>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="api-management_faq-01">
             开发人员层的用途是什么？
            </a>
            <section>
             <p>
              开发人员层适用于 API 管理试用、开发和功能测试。客户不应将此层用于生产。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="api-management_faq-02">
             我是否可以在自己的数据中心中在本地部署 API 管理代理？
            </a>
            <section>
             <p>
              不。目前没有本地部署选项，但如果你喜欢此功能，可在
              <a href="https://feedback.azure.com/forums/248703-api-management?filter=top&amp;page=1" id="api-management_faq-feedback-api-management">
               UserVoice
              </a>
              上投票。但是，你当然可以将基于 Azure 的 API 管理用于本地系统和数据。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="api-management_faq-03">
             什么是“单位”？我可以如何扩展我的服务？
            </a>
            <section>
             <p>
              客户可通过添加和删除单位来扩展 API 管理。每个单位都具有依赖其级别的容量。例如，标准层包含每月 200 百万 API 调用、每月 1 TB
                                                带宽、每秒约 1,000 请求的吞吐量。当你添加附加单位时，容量会成比例扩展。例如，2 个标准单位提供每月 400 百万 API 调用、2 TB 带宽、每秒约
                                                2,000 请求的吞吐量。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="api-management_faq-03">
             什么是“网关部署”？
            </a>
            <section>
             <p>
              网关部署中的所有节点共享位置属性和配置，例如自定义域名和分配的API。每个网关部署都对应一个网关资源，可以在Azure门户中通过API管理服务内的刀片式网关创建，也可以通过管理API以编程方式创建。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                        <h2>上市地区</h2>
                        <p>API 管理在以下区域中提供：</p>
                        <table cellpadding="0" cellspacing="0" class="table-col6">
                            <tr>
                                <th align="left"><strong>地域</strong></th>
                                <th align="left"><strong>区域</strong></th>
                            </tr>
                            <tr>
                                <td>中国大陆</td>
                                <td>中国东部数据中心 , 中国北部数据中心</td>
                            </tr>
                        </table>
                    </div> -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="cognitive-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证至少在 99.9% 的时间内，标准层中运行的 API 管理服务实例将响应请求以执行操作。我们保证至少在 99.95% 的时间内，高级层中运行的跨两个或更多区域部署的
                            API 管理服务实例将响应请求以执行操作。未向开发人员层 API 管理服务提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/api-management/index.html" id="pricing_api-management_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

                <h2>支持和服务级别协议</h2>
                <p>Azure 支持功能：</p>
                <p>我们免费向用户提供以下支持服务：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left">&nbsp;</th>
                        <th align="left"><strong>是否支持</strong></th>
                    </tr>
                    <tr>
                        <td>计费和订阅管理</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>服务仪表板</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>Web事件提交</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>中断/修复不受限制</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>电话支持</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>ICP备案支持</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                </table>
                <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                <h2>服务热线：</h2>
                <ul>
                    <li>400-089-0365</li>
                    <li>010-84563652</li>
                </ul>
                <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

          -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="ep1Y716elbXbpx_787OKE6dR2Eh0BD4QZZ19ffv0rvQz7W0pmZi4jG9-IShD-QAjxLgVnR2lRlBr73X5c1Hzz3EFoHYwpxfbYA5sKYdKR6U1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!--<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
