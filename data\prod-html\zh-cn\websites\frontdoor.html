<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta
        content="Azure Front Door Premium 以 Azure Front Door Standard 定价的功能为基础，并添加了额外的安全功能，例如 Web 应用程序防火墙(WAF)、专用链接、机器人防护、与 Microsoft 威胁智能集成和安全分析。WAF 和专用链接定价包含在 Azure Front Door Premium 中。"
        name="keywords" />
    <meta content="了解 Azure Front Door 价格详情。Azure Front Door 可在任何位置为用户提供优化体验的新式云" name="description" />
    <title>
        Azure Front Door_价格预算 - Azure 云计算
    </title>
    <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/zh-cn/pricing/details/frontdoor/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }

        
         .updatetime {
            color: black;
            text-align: right;
            font-size: 12px;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="frontdoor" wacn.date="11/27/2015">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/cdn.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="/Static/Images/SVG/10073-icon-FrontDoor-and-CDN-Profiles-Networking.svg" />
                                    <h2>
                                        Azure Front Door
                                    </h2>
                                    <h4>
                                        可在任何位置为用户提供优化体验的新式云
                                    </h4>
                                    <div class="updatetime">更新时间：2025年08月29日</div>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <p>
                                Azure Front Door Premium 以 Azure Front Door Standard 定价的功能为基础，并添加了额外的安全功能，例如 Web
                                应用程序防火墙(WAF)、
                                专用链接、机器人防护、与 Microsoft 威胁智能集成和安全分析。WAF 和专用链接定价包含在 Azure Front Door Premium 中。
                            </p>
                        </div>
                        <!-- BEGIN: TAB-CONTROL -->
                        <div class="technical-azure-selector tab-control-selector">
                            <h2>
                                定价详细信息
                            </h2>
                            <!-- BEGIN: TAB-CONTAINER-1 -->
                            <div class="tab-control-container tab-active" id="tabContent1">
                                <!-- BEGIN: Table1-Content-->
                                 <br/>
                                 <p><strong>Azure Front Door的计费将于10/1/2025开始。</strong></p>
                                 <br/>
                                <p>
                                    Azure Front Door 是一种安全的云 CDN 服务，用于加速内容交付，同时保护应用、API 和网站免受网络威胁。
                                    Azure Front Door 结合了传统 CDN、全局负载平衡、动态站点加速和安全性的功能，包括 Azure Web 
                                    应用程序防火墙(WAF)和 DDoS。 ​Azure Front Door 定价分为两层:
                                </p>
                                <br />
                                <p>
                                    <strong>Azure Front Door Standard</strong> 针对内容交付进行了优化，提供静态和动态内容加速、
                                    全局负载平衡、SSL 卸载、域和证书管理、增强的流量分析和基本安全功能。​
                                </p>
                                <br />
                                <p>
                                    <strong>Azure Front Door Premium</strong> 以 Azure Front Door Standard 的功能为基础，
                                    并添加了广泛的安全功能，例如 WAF、机器人防护、Azure 专用链接支持、与 Microsoft 威胁智能集成以及安全分析。
                                    WAF 和专用链接定价包含在 Azure Front Door Premium 中。
                                </p>
                                <br />
                                <p>Azure Front Door Standard/Premium 基于以下定价维度计费:​
                                <ol>
                                    <li>
                                        基本费用（即按小时计算的固定费用）​
                                    </li>
                                    <li>
                                        从 Edge 到客户端的出站数据传输
                                    </li>
                                    <li>
                                        从 Edge 到源的出站数据传输
                                    </li>
                                    <li>
                                        从客户端发送到 Front Door 的边缘位置的请求
                                    </li>
                                    <li>
                                        从 Azure 数据中心原点到 Front Door 边缘位置的免费数据传输
                                    </li>
                                </ol>
                                </p>
                                <p>详细了解
                                    <a href="https://docs.azure.cn/zh-cn/frontdoor/billing">Azure Front Door 计费计量
                                    </a>
                                    .
                                </p>
                                <br />
                                <div>
                                    <h3>基本费用（按小时计费且仅适用于使用的小时数）​</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%" id="frontdoor-basefee">
                                        <tr>
                                            <th style="min-width: 200;"></th>
                                            <th align="left" width="50%"><strong>每月价格</strong></th>
                                        </tr>
                                        <tr>
                                            <td>
                                                标准
                                            </td>
                                            <td>
                                                ￥222.6
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                高级
                                            </td>
                                            <td>
                                                ￥2098.8
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <br />
                                <div>
                                    <h3>从 Edge 到客户端的出站数据传输(标准版和高级版价格相同)</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%"
                                        id="frontdoor-OutboundDataTransfer-EdgetoClient">
                                        <tr>
                                            <th><strong>数据发送到客户端</strong></th>
                                            <th align="left"><strong>价格</strong></th>
                                        </tr>
                                        <tr>
                                            <td>
                                                所有数据传输
                                            </td>
                                            <td width="50%">
                                                ￥0.67/GB
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <br />
                                <div>
                                    <h3>从 Edge 到源的出站数据传输(标准版和高级版价格相同)</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%"
                                        id="frontdoor-OutboundDataTransfer-EdgetoOrigin">
                                        <tr>
                                            <th><strong>到原点的数据传输</strong></th>
                                            <th align="left"><strong>价格</strong></th>
                                        </tr>
                                        <tr>
                                            <td>
                                                所有数据传输
                                            </td>
                                            <td width="50%">
                                                ￥ 0.3/GB
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <br />

                                <h3>请求定价</h3>

                                <div>
                                    <h3>标准</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%"
                                        id="frontdoor-RequestPrice-Standard">
                                        <tr>
                                            <th><strong>请求定价（每 10,000 个请求）</strong></th>
                                            <th align="left"><strong>价格</strong></th>
                                        </tr>
                                        <tr>
                                            <td width="50%">
                                                前 2.5 亿个请求​​
                                            </td>
                                            <td>
                                                ￥ 0.135
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                超过 2.5 亿个请求​
                                            </td>
                                            <td>
                                                联系销售
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <br />

                                <div>
                                    <h3>高级版(包括 WAF 和专用链接，不需增加加强)</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%"
                                        id="frontdoor-RequestPrice-Premium-noAddCost">
                                        <tr>
                                            <th><strong>请求定价（每 10,000 个请求）</strong></th>
                                            <th align="left"><strong>价格</strong></th>
                                        </tr>
                                        <tr>
                                            <td>
                                                前 2.5 亿个请求​​
                                            </td>
                                            <td>
                                                ￥ 0.225
                                            </td>
                                        </tr>
                                        <tr>
                                            <td width="50%">
                                                超过 2.5 亿个请求
                                            </td>
                                            <td>
                                                联系销售
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <br />
<!-- 
                                <div>
                                    <h3>高级版(WAF 加载项)</h3>
                                    <table cellpadding="0" cellspacing="0" width="100%"
                                        id="frontdoor-RequestPrice-Premium-AddCost">
                                        <tr>
                                            <th>加载项</th>
                                            <th align="left">价格</th>
                                        </tr>
                                        <tr>
                                            <td>
                                                HIP 质询
                                            </td>
                                            <td>
                                                ￥ 0.225
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="tags-date">
                                    <div class="ms-date">
                                        每个 HIP 质询会话都记录为单个实例，无论在该会话中遇到和解决的质询数如何。
                                    </div>
                                </div> -->
                                <!-- END: Table1-Content-->
                            </div>
                            <!-- END: TAB-CONTAINER-1 -->
                        </div>
                        <!-- END: TAB-CONTROL -->
                        <div class="pricing-page-section">
                            <!-- FAQ -->
                        </div>
                        <div class="pricing-page-section">
                        <!-- SLA -->
                        </div>
                        <!--BEGIN: Support and service code chunk-->
                        <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
                        <!--END: Support and service code chunk-->
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="3oPhXExIDIwZ3R9jIzFi2mU5h8bBXGP038Br2Rb-26HvlU4FhWEuWxhsnVwdZ_UabCez4xa9dKfpt2hPA8rJIIfAkL-ppZAbXgMJ0RND1y01" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>