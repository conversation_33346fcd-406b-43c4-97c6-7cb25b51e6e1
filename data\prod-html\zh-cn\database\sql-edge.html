<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1" name="viewport" />
    <meta content="无需 VM 上的公共 IP，Azure Bastion 服务就提供了到 Azure 虚拟机中的 Azure VM 的安全、无缝 RDP 和 SSH 连接。" name="description" />
    <title>
        Azure SQL Edge定价
    </title>
    <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="azure-sql-edge" wacn.date="11/27/2015">
                        </tags>
                        <style type="text/css">
                            .pricing-detail-tab .tab-nav {
                                padding-left: 0 !important;
                                margin-top: 5px;
                                margin-bottom: 0;
                                overflow: hidden;
                            }

                            .pricing-detail-tab .tab-nav li {
                                list-style: none;
                                float: left;
                            }

                            .pricing-detail-tab .tab-nav li.active a {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-nav li.active a:hover {
                                border-bottom: 4px solid #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel {
                                display: none;
                            }

                            .pricing-detail-tab .tab-content .tab-panel.show-md {
                                display: block;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                                padding-left: 5px;
                                padding-right: 5px;
                                color: #00a3d9;
                                background-color: #FFF;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                                color: #FFF;
                                background-color: #00a3d9;
                            }

                            .pure-content .technical-azure-selector p a,
                            .pure-content .technical-azure-selector table a {
                                background: 0 0;
                                padding: 0;
                                margin: 0 6px;
                                height: 21px;
                                line-height: 22px;
                                font-size: 14px;
                                color: #00a3d9;
                                float: none;
                                display: inline;
                            }

                            .svg {
                                width: 50px;
                                float: left;
                                margin-right: 10px;
                            }
                        </style>
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <div class="svg">
                                        <svg aria-hidden="true" data-slug-id="sql-edge" role="presentation"
                                            viewbox="0 0 49 49" xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M6.89062 7.13281V41.3286C6.89062 44.8529 14.7966 47.8058 24.6076 47.8058V7.13281H6.89062Z"
                                                fill="#0072C5">
                                            </path>
                                            <path
                                                d="M24.418 47.8058H24.7037C34.5148 47.8058 42.4207 44.9482 42.4207 41.3286V7.13281H24.418V47.8058Z"
                                                fill="#0072C5">
                                            </path>
                                            <path
                                                d="M24.2266 47.8053H24.5123C34.4186 47.8053 42.5151 44.9477 42.5151 41.3281V7.03711H24.3218L24.2266 47.8053Z"
                                                fill="#3A93CE">
                                            </path>
                                            <path
                                                d="M42.4184 7.13344C42.4184 10.6578 34.5124 13.6106 24.7014 13.6106C14.8904 13.6106 6.98438 10.753 6.98438 7.13344C6.98438 3.51383 14.8904 0.65625 24.7014 0.65625C34.5124 0.65625 42.4184 3.60909 42.4184 7.13344Z"
                                                fill="#fff">
                                            </path>
                                            <path
                                                d="M38.8003 6.75122C38.8003 9.13254 32.5136 11.0376 24.7029 11.0376C16.8922 11.0376 10.6055 9.13254 10.6055 6.75122C10.6055 4.3699 16.8922 2.46484 24.7029 2.46484C32.5136 2.46484 38.8003 4.3699 38.8003 6.75122Z"
                                                fill="#7FB900">
                                            </path>
                                            <path
                                                d="M35.8475 9.32305C37.6573 8.56102 38.8003 7.70375 38.8003 6.75122C38.8003 4.3699 32.5136 2.46484 24.7029 2.46484C16.8922 2.46484 10.6055 4.3699 10.6055 6.75122C10.6055 7.70375 11.7485 8.65628 13.5583 9.32305C16.1301 8.27526 20.226 7.70375 24.7029 7.70375C29.1798 7.70375 33.2756 8.37052 35.8475 9.32305Z"
                                                fill="#B7D332">
                                            </path>
                                            <path
                                                d="M18.9868 30.8499C18.9868 31.8977 18.6058 32.755 17.8438 33.3265C17.0818 33.898 16.034 34.1838 14.6052 34.1838C13.4621 34.1838 12.5096 33.9933 11.7476 33.517V31.0404C12.6049 31.8025 13.6527 32.1835 14.7004 32.1835C15.1767 32.1835 15.653 32.0882 15.9387 31.8977C16.2245 31.7072 16.3197 31.4215 16.3197 31.0404C16.3197 30.6594 16.2245 30.3737 15.9387 30.1832C15.653 29.8974 15.0814 29.6117 14.2242 29.2306C12.5096 28.4686 11.6523 27.3256 11.6523 25.9921C11.6523 24.9443 12.0334 24.1822 12.7954 23.6107C13.5574 23.0392 14.5099 22.6582 15.7482 22.6582C16.796 22.6582 17.7485 22.8487 18.5105 23.1345V25.5158C17.7485 25.0395 16.8912 24.7538 15.9387 24.7538C15.4625 24.7538 15.0814 24.849 14.7957 25.0395C14.5099 25.23 14.4147 25.5158 14.4147 25.8968C14.4147 26.2778 14.5099 26.5636 14.7957 26.7541C14.9862 26.9446 15.4625 27.2303 16.2245 27.6113C17.2723 28.0876 18.0343 28.5639 18.5105 29.1354C18.7963 29.5164 18.9868 30.0879 18.9868 30.8499Z"
                                                fill="#fff">
                                            </path>
                                            <path
                                                d="M28.4183 28.4686C28.4183 27.3256 28.1325 26.4683 27.6563 25.8015C27.18 25.1348 26.5132 24.849 25.656 24.849C24.7987 24.849 24.0367 25.1348 23.5604 25.8015C22.9889 26.4683 22.7984 27.3256 22.7984 28.4686C22.7984 29.5164 23.0841 30.4689 23.5604 31.1357C24.0367 31.8025 24.7987 32.0882 25.656 32.0882C26.5132 32.0882 27.18 31.8025 27.7515 31.1357C28.1325 30.4689 28.4183 29.6117 28.4183 28.4686ZM31.0854 28.3734C31.0854 29.7069 30.7996 30.8499 30.2281 31.8025C29.6566 32.755 28.7993 33.4218 27.6563 33.8028L30.8949 36.8509H27.6563L25.3702 34.279C24.4177 34.279 23.4652 33.9933 22.7031 33.517C21.9411 33.0408 21.2743 32.374 20.8933 31.5167C20.4171 30.6594 20.2266 29.7069 20.2266 28.6591C20.2266 27.5161 20.4171 26.4683 20.8933 25.5158C21.3696 24.5633 22.0364 23.8965 22.8936 23.4202C23.7509 22.944 24.7034 22.6582 25.8465 22.6582C26.8943 22.6582 27.8468 22.8487 28.6088 23.325C29.4661 23.8012 30.0376 24.468 30.5139 25.3253C30.8949 26.2778 31.0854 27.2303 31.0854 28.3734Z"
                                                fill="#fff">
                                            </path>
                                            <path d="M39.6575 33.9922H32.8945V22.8477H35.4664V31.9919H39.6575V33.9922Z"
                                                fill="#fff">
                                            </path>
                                        </svg>
                                    </div>
                                    <h2>
                                        Azure SQL Edge定价
                                    </h2>
                                    <h4>
                                        具有内置流式处理、存储和 AI 的边缘优化 IoT 数据库
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <div class="pricing-page-section">
                            <p>
                                Azure SQL Edge 将 Microsoft SQL 引擎的业界领先性能和安全性扩展到智能边缘。这一内存占用少的容器针对 IoT
                                网关和设备进行了优化，通过内置的流、存储和对 AI 的支持提供实时见解。 一次性开发应用程序，并在边缘、数据中心和 Azure 上的任何位置进行部署。
                            </p>
                            <br/>
                            <h2>
                                定价详细信息
                            </h2>
                        </div>

                        <!-- BEGIN: TAB-CONTROL -->
                        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                            <div class="tab-container-container">
                                <div class="tab-container-box">
                                    <div class="tab-container">
                                        <div class="dropdown-container software-kind-container" style="display:none;">
                                            <label>
                                                OS/Software:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    Azure SQL Edge
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li class="active">
                                                        <a data-href="#tabContent1" href="javascript:void(0)"
                                                            id="home_VPN Gateway">
                                                            Azure SQL Edge
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select software-box hidden-lg hidden-md"
                                                id="software-box">
                                                <option data-href="#tabContent1" selected="selected"
                                                    value="VPN Gateway">
                                                    Azure SQL Edge
                                                </option>
                                            </select>
                                        </div>
                                        <div class="dropdown-container region-container"  style="display:none;">
                                            <label>
                                                地区:
                                            </label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">
                                                    中国北部 3
                                                </span>
                                                <i class="icon">
                                                </i>
                                                <ol class="tab-items">
                                                    <li>
                                                        <a data-href="#north-china3" href="javascript:void(0)"
                                                            id="north-china3">
                                                            中国北部 3
                                                        </a>
                                                    </li>
                                                    <li class="active">
                                                        <a data-href="#east-china2" href="javascript:void(0)"
                                                            id="east-china2">
                                                            中国东部 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china2" href="javascript:void(0)"
                                                            id="north-china2">
                                                            中国北部 2
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#east-china" href="javascript:void(0)"
                                                            id="east-china">
                                                            中国东部
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a data-href="#north-china" href="javascript:void(0)"
                                                            id="north-china">
                                                            中国北部
                                                        </a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select region-box hidden-lg hidden-md"
                                                id="region-box">
                                                <option data-href="#north-china3" selected="selected"
                                                    value="north-china3">
                                                    中国北部 3
                                                </option>
                                                <option data-href="#east-china2" selected="selected"
                                                    value="east-china2">
                                                    中国东部 2
                                                </option>
                                                <option data-href="#north-china2" value="north-china2">
                                                    中国北部 2
                                                </option>
                                                <option data-href="#east-china" value="east-china">
                                                    中国东部
                                                </option>
                                                <option data-href="#north-china" value="north-china">
                                                    中国北部
                                                </option>
                                            </select>
                                        </div>
                                        <div class="clearfix">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- BEGIN: TAB-CONTAINER-1 -->
                            <div class="tab-content">
                                <div class="tab-panel" id="tabContent1">
                                    <p>
                                        Azure SQL Edge 具有简化的定价，它已针对 IoT 部署设定适当大小。Azure SQL Edge 采用每设备定价模型。购买一个单位的 Azure
                                        SQL
                                        Edge 后，就有权在一台设备上运行 Azure SQL Edge。
                                    </p>
                                    <table cellpadding="0" cellspacing="0" width="100%">
                                        <tr>
                                            <th align="left">
                                                <strong>
                                                    产品
                                                </strong>
                                            </th>
                                            <th align="left">
                                                <strong>
                                                    价格
                                                </strong>
                                            </th>
                                        </tr>
                                        <tr>
                                            <td>
                                                Azure SQL Edge
                                            </td>
                                            <td>
                                                ￥0.0822/设备/小时
                                            </td>
                                        </tr>
                                    </table>
                                    <div class="tags-date">
                                        <div class="ms-date">
                                            Azure SQL Edge 部署到设备时便开始计费，无论 SQL 进程是正在运行、失败还是已停止，都是如此。
                                        </div>
                                    </div>
                                </div>
                                <!-- END: Table1-Content-->
                            </div>
                            <!-- END: TAB-CONTAINER-1 -->
                        </div>
                        
                        <div class="pricing-page-section">
                            <h2>
                                支持和服务级别协议
                            </h2>
                            <p>
                                如有任何疑问或需要帮助，请访问
                                <a href="https://support.azure.cn/zh-cn/support/contact"
                                    id="sql-server-stretch-contact-page">
                                    Azure 支持
                                </a>
                                选择自助服务或者其他任何方式联系我们获得支持。
                            </p>
                        </div>
                        <!--BEGIN: Support and service code chunk-->
                        <!--END: Support and service code chunk-->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token =
                '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
                .toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
    </div>
</body>

</html>