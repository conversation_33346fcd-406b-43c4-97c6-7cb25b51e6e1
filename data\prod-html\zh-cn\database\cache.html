<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure 微软云, 用于 Redis 的 Azure 缓存, 价格详情定价计费" name="keywords"/>
  <meta content="了解用于 Redis 的 Azure 缓存（Cache）的价格详情。Azure Redis缓存分为基本、标准和高级三个级别，不同级别价格有所差异，用户可根据需求选择适用的级别。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   Azure Redis 缓存服务定价 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/cache/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <div class="hide-info" style="display:none;">
   <div class="bg-box">
    <div class="cover-bg">
    </div>
   </div>
   <div class="msg-box">
    <div class="pricing-unavailable-message">
     所选区域不可用
    </div>
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="cache" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/redis-cache.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           用于 Redis 的 Azure 缓存
           <span>
            Azure Cache for Redis
           </span>
          </h2>
          <h4>
           具有高吞吐量、低延迟的数据访问的强大应用程序
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         借助用于 Redis 的 Azure 缓存，你将能够使用安全的开源 Redis 缓存。用于 R edis 的 Azure 缓存是由 Azure
                                托管的专用服务，可通过向你提供对数据的超高速访问来生成高度可扩展且高度可响应的应用程序。你还可以利用 Redis 提供的丰富功能集和生态系统，同时可以从 Azure
                                获得可靠的承载和监视。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Azure Cache for Redis
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_Azure Cache for Redis">
                Azure Cache for Redis
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="Azure Cache for Redis">
              Azure Cache for Redis
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#north-china3" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTAINER-1 -->
         <div class="tab-control-container tab-active" id="tabContent1">
          <!-- BEGIN: Table1-Content-->
          <table cellpadding="0" cellspacing="0" id="Anomaly_Detector" width="100%">
           <tr>
            <th align="left">
            </th>
            <th align="left">
             <strong>
              基本
             </strong>
            </th>
            <th align="left" style="padding-left: 80px;">
             <strong>
              标准
             </strong>
            </th>
            <th align="left" style="padding-left: 80px;">
             <strong>
              高级
             </strong>
            </th>
           </tr>
           <tr>
            <td style="min-width:100px;">
            </td>
            <td style="min-width:100px;">
             基本缓存是单个缓存节点，适用于开发/测试和非关键型工作负荷。
            </td>
            <td style="padding-left: 80px;">
             基于主/从复制的生产级别缓存服务。
            </td>
            <td style="padding-left: 80px;">
             适合企业的级别，可用作缓存和暂留数据。专为最大规模和企业集成而设计。
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             缓存
            </td>
            <td style="min-width:100px;">
             是
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             复制和故障转移
            </td>
            <td style="min-width:100px;">
             -
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             SLA
            </td>
            <td style="min-width:100px;">
             基本级别没有服务级别协议
            </td>
            <td style="padding-left: 80px;">
             99.9%
            </td>
            <td style="padding-left: 80px;">
             99.9%
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             配置 Redis（Keyspace 通知等）
            </td>
            <td style="min-width:100px;">
             -
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             Redis 数据暂留
            </td>
            <td style="min-width:100px;">
             -
            </td>
            <td style="padding-left: 80px;">
             -
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             Redis 群集
            </td>
            <td style="min-width:100px;">
             -
            </td>
            <td style="padding-left: 80px;">
             -
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             扩大到多个缓存单位
            </td>
            <td style="min-width:100px;">
             -
            </td>
            <td style="padding-left: 80px;">
             -
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             Azure 虚拟网络
            </td>
            <td style="min-width:100px;">
             -
            </td>
            <td style="padding-left: 80px;">
             -
            </td>
            <td style="padding-left: 80px;">
             是
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             内存大小
            </td>
            <td style="min-width:100px;">
             250 MB - 53 GB
            </td>
            <td style="padding-left: 80px;">
             250 MB - 53 GB
            </td>
            <td style="padding-left: 80px;">
             6 GB - 530 GB
             <sup>
              *
             </sup>
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             网络性能
            </td>
            <td style="min-width:100px;">
             低 - 高
            </td>
            <td style="padding-left: 80px;">
             低 - 高
            </td>
            <td style="padding-left: 80px;">
             中等 - 最高
            </td>
           </tr>
           <tr>
            <td style="min-width:100px;">
             客户端连接的最大数量
            </td>
            <td style="min-width:100px;">
             20000
            </td>
            <td style="padding-left: 80px;">
             20000
            </td>
            <td style="padding-left: 80px;">
             40000
            </td>
           </tr>
           <tr>
            <td colspan="4" style="min-width:100px;">
             <sup>
              *
             </sup>
             根据请求可缩放更多实例。
            </td>
           </tr>
          </table>
          <!-- END: Table1-Content-->
          <!-- BEGIN: Table2-Content-->
          <h2>
           定价详细信息
          </h2>
          <div class="scroll-table" style="display: block;">
           <h3>
            基本
           </h3>
           <p>
            基本缓存是单个缓存节点，适用于开发/测试和非关键型工作负荷。基本级别没有服务级别协议。缓存节点的更新升级阶段，服务不可用，数据可能会丢失。
           </p>
           <!-- <p>从2016年4月1日起，基本级别价格会下调 25.5%，以下是下调后的新价格：</p> -->
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="cache1" width="100%">
            <tbody>
             <tr>
              <th align="left">
               <strong>
                缓存名称
               </strong>
              </th>
              <th align="left">
               <strong>
                缓存大小
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                价格
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                网络性能
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                客户端连接数量
               </strong>
              </th>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C0
              </td>
              <td style="min-width:100px;">
               250 MB
              </td>
              <td style="padding-left: 80px;">
               ￥0.14 片/节点/小时（约￥104.16 /月）
              </td>
              <td style="padding-left: 80px;">
               低
              </td>
              <td style="padding-left: 80px;">
               256
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C1
              </td>
              <td style="min-width:100px;">
               1 GB
              </td>
              <td style="padding-left: 80px;">
               ￥0.35 片/节点/小时（约￥260.40 /月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               1000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C2
              </td>
              <td style="min-width:100px;">
               2.5 GB
              </td>
              <td style="padding-left: 80px;">
               ￥0.57 片/节点/小时（约￥424.08 /月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               2000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C3
              </td>
              <td style="min-width:100px;">
               6 GB
              </td>
              <td style="padding-left: 80px;">
               ￥1.14 片/节点/小时（约 ￥848.16 /月）
              </td>
              <td style="padding-left: 80px;">
               高
              </td>
              <td style="padding-left: 80px;">
               5000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C4
              </td>
              <td style="min-width:100px;">
               13 GB
              </td>
              <td style="padding-left: 80px;">
               ￥1.33 片/节点/小时（约 ￥989.52 /月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               10000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C5
              </td>
              <td style="min-width:100px;">
               26 GB
              </td>
              <td style="padding-left: 80px;">
               ￥2.66 片/节点/小时（约 ￥1979.04 /月）
              </td>
              <td style="padding-left: 80px;">
               高
              </td>
              <td style="padding-left: 80px;">
               15000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C6
              </td>
              <td style="min-width:100px;">
               53 GB
              </td>
              <td style="padding-left: 80px;">
               ￥5.31 片/节点/小时（约￥3950.64 /月）
              </td>
              <td style="padding-left: 80px;">
               最高
              </td>
              <td style="padding-left: 80px;">
               20000
              </td>
             </tr>
            </tbody>
           </table>
          </div>
          <!-- END: Table2-Content-->
          <!-- BEGIN: Table3-Content-->
          <div class="scroll-table" style="display: block;">
           <h3>
            标准
           </h3>
           <p>
            在双节点主要/辅助配置中提供一个复制的缓存。我们将会管理两个节点之间的自动复制，并提供一个高可用性的服务级别协议。
           </p>
           <!-- <p>从2016年4月1日起，标准级别价格会下调 25.5%，以下是下调后的新价格：</p> -->
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="cache2" width="100%">
            <tbody>
             <tr>
              <th align="left">
               <strong>
                缓存名称
               </strong>
              </th>
              <th align="left">
               <strong>
                缓存大小
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                价格（两节点）
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                网络性能
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                客户端连接数量
               </strong>
              </th>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C0
              </td>
              <td style="min-width:100px;">
               250 MB
              </td>
              <td style="padding-left: 80px;">
               ￥0.35 /小时（约￥258.8/月）
              </td>
              <td style="padding-left: 80px;">
               低
              </td>
              <td style="padding-left: 80px;">
               256
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C1
              </td>
              <td style="min-width:100px;">
               1 GB
              </td>
              <td style="padding-left: 80px;">
               ￥0.82 /小时（约￥610.08/月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               1000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C2
              </td>
              <td style="min-width:100px;">
               2.5 GB
              </td>
              <td style="padding-left: 80px;">
               ￥1.42 /小时（约￥1,059.14/月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               2000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C3
              </td>
              <td style="min-width:100px;">
               6 GB
              </td>
              <td style="padding-left: 80px;">
               ￥2.85/小时（约 ￥2,116.7/月）
              </td>
              <td style="padding-left: 80px;">
               高
              </td>
              <td style="padding-left: 80px;">
               5000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C4
              </td>
              <td style="min-width:100px;">
               13 GB
              </td>
              <td style="padding-left: 80px;">
               ￥3.32/小时（约 ￥2,470.8/月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               10000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C5
              </td>
              <td style="min-width:100px;">
               26 GB
              </td>
              <td style="padding-left: 80px;">
               ￥6.64/小时（约 ￥4,941.62/月）
              </td>
              <td style="padding-left: 80px;">
               高
              </td>
              <td style="padding-left: 80px;">
               15000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               C6
              </td>
              <td style="min-width:100px;">
               53 GB
              </td>
              <td style="padding-left: 80px;">
               ￥13.28/小时（约￥9,881.66/月）
              </td>
              <td style="padding-left: 80px;">
               最高
              </td>
              <td style="padding-left: 80px;">
               20000
              </td>
             </tr>
            </tbody>
           </table>
          </div>
          <!-- END: Table3-Content-->
          <!-- BEGIN: Table4-Content-->
          <div class="scroll-table" style="display: block;">
           <h3>
            高级
           </h3>
           <p>
            除了所有标准级别功能之外，新的高级级别还包括更多其他功能，如优于基本或标准级别缓存的性能、更大的工作负荷、灾难恢复和增强的安全性。其他功能包括：
           </p>
           <ul class="ul-price-style">
            <li>
             Redis 暂留可让你保留存储在 Redis 缓存中的数据。你还可获取快照并备份数据，以便在出现故障时进行加载。
            </li>
            <li>
             Redis 群集自动对多个 Redis 节点中的数据进行分片，因此你可以创建具有更大的内存大小的工作负荷（大于 53 GB）并获取更好的性能。
            </li>
            <li>
             <a href="https://azure.microsoft.com/zh-cn/services/virtual-network/" id="home_redia_network" target="_blank">
              Azure
                                                    虚拟网络 (VNet)
             </a>
             部署为用于 Redis 的 Azure
                                                缓存提供增强的安全性，并提供子网、访问控制策略和进一步限制访问的其他功能。
            </li>
           </ul>
           <!-- <p>从2016年4月1日起，高级级别价格会下调 25.5%，以下是下调后的新价格：</p> -->
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="cache6" width="100%">
            <tbody>
             <tr>
              <th align=" left">
               <strong>
                缓存名称
               </strong>
              </th>
              <th align="left">
               <strong>
                缓存大小
               </strong>
              </th>
              <th align="left" style="min-width:150px;">
               <strong>
                预付款（每个分片，两个节点）
               </strong>
              </th>
              <th align="left" style="min-width:100px;">
               <strong>
                网络性能
               </strong>
              </th>
              <th align="left" style="min-width:150px;">
               <strong>
                客户端连接数量
               </strong>
              </th>
              <th align="left" style="min-width:100px;">
               <strong>
                CPP保留1年
               </strong>
              </th>
              <th align="left" style="min-width:100px;">
               <strong>
                CPP保留3年
               </strong>
              </th>
             </tr>
             <tr>
              <td style="min-width:100px;">
               P1
              </td>
              <td style="min-width:100px;">
               6 GB
              </td>
              <td style="min-width:150px;">
               ￥3.72 片/节点/小时（约￥2770.51/月）
              </td>
              <td style="min-width:100px;">
               中等
              </td>
              <td style="min-width:100px;">
               7,500
              </td>
              <td style="min-width:100px;">
               ￥2.53/小时 （￥1879.51月大约节省 32%）
              </td>
              <td style="min-width:100px;">
               ￥1.78/小时（￥ 1321.53/月大约节省 52%）
              </td>
             </tr>
             <tr>
              <td>
               P2
              </td>
              <td>
               13 GB
              </td>
              <td>
               ￥7.44 片/节点/小时（约￥5534.69/月）
              </td>
              <td>
               中等
              </td>
              <td>
               15,000
              </td>
              <td>
               ￥5.05/小时 （￥3754.73/月大约节省 32%）
              </td>
              <td>
               ￥3.55/小时（￥ 2640.05/月大约节省 52%）
              </td>
             </tr>
             <tr>
              <td>
               P3
              </td>
              <td>
               26 GB
              </td>
              <td>
               ￥14.88 片/节点/小时（约￥11069.38/月）
              </td>
              <td>
               高
              </td>
              <td>
               30,000
              </td>
              <td>
               ￥10.09/小时 （￥7509.46/月大约节省 32%）
              </td>
              <td>
               ￥7.1/小时（￥ 5280.09/月大约节省 52%）
              </td>
             </tr>
             <tr>
              <td>
               P4
              </td>
              <td>
               53 GB
              </td>
              <td>
               ￥29.77 片/节点/小时（约￥22150.52/月）
              </td>
              <td>
               最高
              </td>
              <td>
               40,000
              </td>
              <td>
               ￥20.2/小时 （￥15026.91/月大约节省 32%）
              </td>
              <td>
               ￥14.2/小时（￥ 10565.79/月大约节省 52%）
              </td>
             </tr>
             <tr>
              <td>
               P5
              </td>
              <td>
               120 GB
              </td>
              <td>
               ￥63.92 片/节点/小时（约￥47556.48/月）
              </td>
              <td>
               最高
              </td>
              <td>
               40,000
              </td>
              <td>
               ￥43.36/小时 （￥32262.32/月大约节省 32%）
              </td>
              <td>
               ￥30.49/小时（￥ 22684.45/月大约节省 52%）
              </td>
             </tr>
            </tbody>
           </table>
          </div>
          <div class="scroll-table" style="display: block;">
           <h3>
            高级
           </h3>
           <p>
            除了所有标准级别功能之外，新的高级级别还包括更多其他功能，如优于基本或标准级别缓存的性能、更大的工作负荷、灾难恢复和增强的安全性。其他功能包括：
           </p>
           <ul class="ul-price-style">
            <li>
             Redis 暂留可让你保留存储在 Redis 缓存中的数据。你还可获取快照并备份数据，以便在出现故障时进行加载。
            </li>
            <li>
             Redis 群集自动对多个 Redis 节点中的数据进行分片，因此你可以创建具有更大的内存大小的工作负荷（大于 53 GB）并获取更好的性能。
            </li>
            <li>
             <a href="https://azure.microsoft.com/zh-cn/services/virtual-network/" id="home_redia_network" target="_blank">
              Azure
                                                    虚拟网络 (VNet)
             </a>
             部署为用于 Redis 的 Azure
                                                缓存提供增强的安全性，并提供子网、访问控制策略和进一步限制访问的其他功能。
            </li>
           </ul>
           <!-- <p>从2016年4月1日起，高级级别价格会下调 25.5%，以下是下调后的新价格：</p> -->
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="cache3" width="100%">
            <tbody>
             <tr>
              <th align=" left">
               <strong>
                缓存名称
               </strong>
              </th>
              <th align="left">
               <strong>
                缓存大小
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                预付款（每个分片，两个节点）
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                网络性能
               </strong>
              </th>
              <th align="left" style="padding-left: 80px;">
               <strong>
                客户端连接数量
               </strong>
              </th>
             </tr>
             <tr>
              <td style="min-width:100px;">
               P1
              </td>
              <td style="min-width:100px;">
               6 GB
              </td>
              <td style="padding-left: 80px;">
               ￥3.72 片/节点/小时（约￥2770.51/月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               7,500
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               P2
              </td>
              <td style="min-width:100px;">
               13 GB
              </td>
              <td style="padding-left: 80px;">
               ￥7.44 片/节点/小时（约￥5534.69/月）
              </td>
              <td style="padding-left: 80px;">
               中等
              </td>
              <td style="padding-left: 80px;">
               15,000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               P3
              </td>
              <td style="min-width:100px;">
               26 GB
              </td>
              <td style="padding-left: 80px;">
               ￥14.88 片/节点/小时（约￥11069.38/月）
              </td>
              <td style="padding-left: 80px;">
               高
              </td>
              <td style="padding-left: 80px;">
               30,000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               P4
              </td>
              <td style="min-width:100px;">
               53 GB
              </td>
              <td style="padding-left: 80px;">
               ￥29.77 片/节点/小时（约￥22150.52/月）
              </td>
              <td style="padding-left: 80px;">
               最高
              </td>
              <td style="padding-left: 80px;">
               40,000
              </td>
             </tr>
             <tr>
              <td style="min-width:100px;">
               P5
              </td>
              <td style="min-width:100px;">
               120 GB
              </td>
              <td style="padding-left: 80px;">
               ￥63.92 片/节点/小时（约￥47556.48/月）
              </td>
              <td style="padding-left: 80px;">
               最高
              </td>
              <td style="padding-left: 80px;">
               40,000
              </td>
             </tr>
            </tbody>
           </table>
          </div>
          <!-- <div class="scroll-table" style="display: block;">
                                        <h3>企业</h3>
                                        <table cellpadding="0" cellspacing="0" width="100%" id="cache4">
                                            <tbody>
                                                <tr>
                                                    <th align=" left"><strong>缓存名称</strong></th>
                                                    <th align="left"><strong>缓存大小</strong></th>
                                                    <th align="left" style="padding-left: 80px;"><strong>预付款（每个分片，两个节点）</strong></th>
                                                    <th align="left" style="padding-left: 80px;"><strong>网络性能</strong></th>
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        E1
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        6 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥2.54 片/节点/小时（约￥1,889.76/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        高
                                                    </td>
                                    
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        E10
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        12 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥4.89 片/节点/小时（约￥3,638.16/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        高
                                                    </td>
                                    
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        E20
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        25 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥9.76 片/节点/小时（约￥7,261.44/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        高
                                                    </td>
                                    
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        E50
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        50 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥19.18 片/节点/小时（约￥14,269.92/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        最高
                                                    </td>
                                    
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        E100
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        100 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥38.35 片/节点/小时（约￥28,532.4/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        最高
                                                    </td>
                                    
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="scroll-table" style="display: block;">
                                        <h3>Enterprise Flash</h3>
                                        <table cellpadding="0" cellspacing="0" width="100%" id="cache5">
                                            <tbody>
                                                <tr>
                                                    <th align=" left"><strong>缓存名称</strong></th>
                                                    <th align="left"><strong>缓存大小</strong></th>
                                                    <th align="left" style="padding-left: 80px;"><strong>预付款（每个分片，两个节点）</strong></th>
                                                    <th align="left" style="padding-left: 80px;"><strong>网络性能</strong></th>
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        F300
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        384 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥
                                                        20.41
                                                        片/节点/小时（约￥15,185.04/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        高
                                                    </td>
                                    
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        F700
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        715 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥40.82 片/节点/小时（约￥30,370.08/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        最高
                                                    </td>
                                    
                                                </tr>
                                                <tr>
                                                    <td style="min-width:100px;">
                                                        F1500
                                                    </td>
                                                    <td style="min-width:100px;">
                                                        1,455 GB
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        ￥81.64 片/节点/小时（约￥60,740.16/月）
                                                    </td>
                                                    <td style="padding-left: 80px;">
                                                        最高
                                                    </td>
                                    
                                                </tr>
                                    
                                            </tbody>
                                        </table>
                                    </div> -->
          <!-- END: Table4-Content-->
         </div>
         <!-- END: TAB-CONTAINER-1 -->
        </div>
        <!-- END: TAB-CONTAINER-1 -->
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_redis_how-to-charge">
             对于传到缓存的数据和从缓存传出的数据如何收费？
            </a>
            <section>
             <p>
              按
              <a href="../data-transfer/index.html" id="redis_question_transfer" target="_blank">
               正常费率
              </a>
              对传入缓存的数据和从缓存传出的数据收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_redis_what-charge">
             我是否可缩放用于 Redis 的 Azure
                                                缓存单位（与托管缓存中的概念相似）？
            </a>
            <section>
             <p>
              可以。在高级级别中，你可以扩大到多个 Redis 缓存单位。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_redis_which-cache-should-be-used">
             我要在 Azure
                                                中创建新的缓存，应使用哪种缓存？
            </a>
            <section>
             <p>
              我们建议新部署使用用于 Redis 的 Azure
                                                    缓存，因为其具有灵活性。此缓存基于许多编程语言（C/C++、.NET、Go、Java、Node.js、Objective
                                                    C、Perl、PHP、Python、Ruby 以及 29 种以上的更多语言）都支持的开放标准 Redis 协议。因此，在 Azure
                                                    内部生成的几乎任何工作负载都可以使用此缓存。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_redis_managed-cache">
             既然你建议使用 Redis，为什么又提供不建议的“托管缓存”选项？
            </a>
            <section>
             <p>
              这是为了支持对其应用所依赖的共享缓存做了投资的客户，使他们有足够的时间迁移到 Redis 缓存。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="pricing_redis_Is_the_storage_billed_separately">
             Redis
                                                缓存的费用中是否包含存储的费用？
            </a>
            <section>
             <p>
              是不包含存储的费用的。高级版本的 Redis 缓存是支持数据暂留的。如果您部署的是高级版，并设置了 Redis
                                                    数据暂留，会产生相应的存储的费用。
             </p>
             <p>
              存储的费用取决于您选择的存储账户类型，如果选择的是 SSD，会按照高级非托管磁盘的费用计费；如果选择的是 HDD 存储，会按照标准页
                                                    Blob 的费用收费。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="redis-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证至少 99.9% 的时间内客户在缓存端点和 Internet 网关之间存在连接。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/redis-cache/index.html" id="pricing_redis_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="MuLJbKYEn_e4JuiXw2RRMXDalIlivlk5XoEIHX05_1jxWTRi42n0eCCqzompq3gxHi7pGI3_gl4iLM1CISyY7RCvrA75oZ9bax90-FvZrNI1" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
