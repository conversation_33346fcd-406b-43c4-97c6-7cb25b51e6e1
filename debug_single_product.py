#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug脚本 - 测试单个产品的提取过程
用于快速调试和验证提取逻辑

使用方法:
    python debug_single_product.py --help
    python debug_single_product.py list-products database
    python debug_single_product.py test mysql --language zh-cn
    python debug_single_product.py test mysql --dry-run
    python debug_single_product.py test-strategy mysql --strategy complex
"""

import argparse
import sys
import os
import json
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

# 导入必要的模块
from src.core.logging import get_logger, setup_logging
from src.core.product_manager import ProductManager
from src.core.extraction_coordinator import ExtractionCoordinator
from src.core.strategy_manager import StrategyManager
from src.strategies.strategy_factory import StrategyFactory
from src.exporters.flexible_content_exporter import FlexibleContentExporter

logger = get_logger(__name__)


class SingleProductDebugger:
    """单产品提取调试器"""
    
    def __init__(self):
        """初始化调试器"""
        self.product_manager = ProductManager()
        self.strategy_manager = StrategyManager()
        
        print("🔧 单产品调试器初始化完成")
        print(f"   产品管理器: ✅")
        print(f"   策略管理器: ✅")
    
    def list_products_in_category(self, category: str):
        """列出指定类别中的所有产品"""
        print(f"\n📋 类别 '{category}' 中的产品:")
        print("="*60)
        
        try:
            products_by_category = self.product_manager.get_products_by_category(category)
            
            if category not in products_by_category:
                print(f"❌ 类别 '{category}' 不存在")
                return
            
            products = products_by_category[category]
            print(f"📦 找到 {len(products)} 个产品:")
            
            for i, product_key in enumerate(products, 1):
                try:
                    # 获取产品配置
                    config = self.product_manager.get_product_config(product_key)
                    display_name = config.get('display_name', product_key)
                    url = config.get('url', 'N/A')
                    
                    # 检查HTML文件是否存在
                    html_path_zh = self.product_manager.get_html_file_path(product_key, 'zh-cn')
                    html_path_en = self.product_manager.get_html_file_path(product_key, 'en-us')
                    
                    zh_status = "✅" if html_path_zh and os.path.exists(html_path_zh) else "❌"
                    en_status = "✅" if html_path_en and os.path.exists(html_path_en) else "❌"
                    
                    print(f"   {i:2d}. {product_key}")
                    print(f"       显示名: {display_name}")
                    print(f"       中文HTML: {zh_status}")
                    print(f"       英文HTML: {en_status}")
                    print(f"       URL: {url}")
                    print()
                    
                except Exception as e:
                    print(f"   {i:2d}. {product_key} - ❌ 配置错误: {e}")
                    print()
            
        except Exception as e:
            print(f"❌ 获取产品列表失败: {e}")
            logger.error(f"获取产品列表失败: {e}", exc_info=True)
    
    def test_product_extraction(self, product_key: str, language: str = "zh-cn", 
                              dry_run: bool = False):
        """测试单个产品的提取过程"""
        print(f"\n🧪 测试产品 '{product_key}' 的提取过程")
        print(f"   语言: {language}")
        print(f"   试运行: {'是' if dry_run else '否'}")
        print("="*60)
        
        try:
            # 1. 检查产品配置
            print("1️⃣ 检查产品配置...")
            try:
                config = self.product_manager.get_product_config(product_key)
                print(f"   ✅ 产品配置加载成功")
                print(f"   显示名: {config.get('display_name', 'N/A')}")
                print(f"   URL: {config.get('url', 'N/A')}")
                print(f"   类别: {self.product_manager.get_product_category(product_key)}")
            except Exception as e:
                print(f"   ❌ 产品配置加载失败: {e}")
                return
            
            # 2. 检查HTML文件
            print("\n2️⃣ 检查HTML文件...")
            html_path = self.product_manager.get_html_file_path(product_key, language)
            if not html_path or not os.path.exists(html_path):
                print(f"   ❌ HTML文件不存在: {html_path}")
                return
            
            file_size = os.path.getsize(html_path) / 1024  # KB
            print(f"   ✅ HTML文件存在: {html_path}")
            print(f"   文件大小: {file_size:.1f} KB")
            
            # 3. 分析页面策略
            print("\n3️⃣ 分析页面策略...")
            try:
                with open(html_path, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(html_content, 'html.parser')
                
                strategy = self.strategy_manager.determine_strategy(soup)
                print(f"   ✅ 策略分析完成")
                print(f"   推荐策略: {strategy.strategy_type.value}")
                print(f"   置信度: {strategy.confidence:.2f}")
                print(f"   原因: {strategy.reason}")
                
            except Exception as e:
                print(f"   ❌ 策略分析失败: {e}")
                return
            
            if dry_run:
                print(f"\n🔍 试运行模式 - 分析完成")
                print(f"💡 使用 --no-dry-run 参数执行实际提取")
                return
            
            # 4. 执行提取
            print("\n4️⃣ 执行内容提取...")
            start_time = time.time()
            
            output_dir = f"debug_output/{language}"
            os.makedirs(output_dir, exist_ok=True)
            
            coordinator = ExtractionCoordinator(output_dir)
            url = config.get('url', '')
            
            extracted_data = coordinator.coordinate_extraction(html_path, url)
            
            processing_time = (time.time() - start_time) * 1000  # ms
            
            if extracted_data and not extracted_data.get('error'):
                print(f"   ✅ 内容提取成功 ({processing_time:.0f}ms)")
                
                # 5. 导出结果
                print("\n5️⃣ 导出提取结果...")
                exporter = FlexibleContentExporter(output_dir)
                output_file = exporter.export_flexible_content(extracted_data, product_key)
                
                print(f"   ✅ 结果已导出: {output_file}")
                
                # 6. 显示提取统计
                self._print_extraction_stats(extracted_data, processing_time)
                
            else:
                error_msg = extracted_data.get('error', '未知错误') if extracted_data else '提取返回空结果'
                print(f"   ❌ 内容提取失败: {error_msg}")
            
        except Exception as e:
            print(f"❌ 测试提取失败: {e}")
            logger.error(f"测试提取失败: {e}", exc_info=True)
    
    def test_specific_strategy(self, product_key: str, strategy_name: str, 
                             language: str = "zh-cn"):
        """测试指定策略的提取效果"""
        print(f"\n🎯 测试产品 '{product_key}' 使用策略 '{strategy_name}'")
        print(f"   语言: {language}")
        print("="*60)
        
        try:
            # 检查HTML文件
            html_path = self.product_manager.get_html_file_path(product_key, language)
            if not html_path or not os.path.exists(html_path):
                print(f"❌ HTML文件不存在: {html_path}")
                return
            
            # 加载HTML
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 创建策略实例
            from src.core.data_models import StrategyType, ExtractionStrategy
            
            strategy_type_map = {
                'simple': StrategyType.SIMPLE_STATIC,
                'region': StrategyType.REGION_FILTER,
                'complex': StrategyType.COMPLEX
            }
            
            if strategy_name not in strategy_type_map:
                print(f"❌ 不支持的策略: {strategy_name}")
                print(f"支持的策略: {list(strategy_type_map.keys())}")
                return
            
            strategy_type = strategy_type_map[strategy_name]
            extraction_strategy = ExtractionStrategy(
                strategy_type=strategy_type,
                confidence=1.0,
                reason=f"手动指定策略: {strategy_name}"
            )
            
            # 创建策略实例
            factory = StrategyFactory()
            strategy_instance = factory.create_strategy(extraction_strategy)
            
            print(f"✅ 策略实例创建成功: {strategy_instance.__class__.__name__}")
            
            # 执行提取
            print(f"\n🚀 执行策略提取...")
            start_time = time.time()
            
            config = self.product_manager.get_product_config(product_key)
            url = config.get('url', '')
            
            extracted_data = strategy_instance.extract_flexible_content(soup, url)
            
            processing_time = (time.time() - start_time) * 1000  # ms
            
            if extracted_data and not extracted_data.get('error'):
                print(f"✅ 策略提取成功 ({processing_time:.0f}ms)")
                
                # 导出结果
                output_dir = f"debug_output/{language}/strategy_test"
                os.makedirs(output_dir, exist_ok=True)
                
                exporter = FlexibleContentExporter(output_dir)
                output_file = exporter.export_flexible_content(
                    extracted_data, f"{product_key}_{strategy_name}"
                )
                
                print(f"✅ 结果已导出: {output_file}")
                
                # 显示提取统计
                self._print_extraction_stats(extracted_data, processing_time)
                
            else:
                error_msg = extracted_data.get('error', '未知错误') if extracted_data else '提取返回空结果'
                print(f"❌ 策略提取失败: {error_msg}")
            
        except Exception as e:
            print(f"❌ 测试策略失败: {e}")
            logger.error(f"测试策略失败: {e}", exc_info=True)
    
    def _print_extraction_stats(self, extracted_data: Dict[str, Any], processing_time: float):
        """打印提取统计信息"""
        print(f"\n📊 提取统计信息:")
        print(f"   处理时间: {processing_time:.0f}ms")
        
        # 基本信息
        metadata = extracted_data.get('extraction_metadata', {})
        print(f"   使用策略: {metadata.get('strategy_used', 'N/A')}")
        print(f"   提取时间: {metadata.get('extraction_timestamp', 'N/A')}")
        
        # 内容统计
        content_data = extracted_data.get('content_data', {})
        
        # Banner信息
        banner = content_data.get('banner', {})
        if banner:
            print(f"   Banner: ✅ (标题: {len(banner.get('title', ''))}, 描述: {len(banner.get('description', ''))})")
        
        # 内容组统计
        content_groups = content_data.get('content_groups', [])
        print(f"   内容组数量: {len(content_groups)}")
        
        for i, group in enumerate(content_groups):
            group_type = group.get('group_type', 'unknown')
            items_count = len(group.get('items', []))
            print(f"     组 {i+1}: {group_type} ({items_count} 项)")
        
        # 页面配置
        page_config = content_data.get('page_config', {})
        filters = page_config.get('filters', [])
        tabs = page_config.get('tabs', [])
        
        if filters:
            print(f"   筛选器: {len(filters)} 个")
        if tabs:
            print(f"   标签页: {len(tabs)} 个")
        
        # 通用区块
        common_sections = content_data.get('common_sections', [])
        if common_sections:
            print(f"   通用区块: {len(common_sections)} 个")
            for section in common_sections:
                section_type = section.get('section_type', 'unknown')
                print(f"     - {section_type}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='单产品提取调试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s list-products database
  %(prog)s test mysql --language zh-cn
  %(prog)s test mysql --dry-run
  %(prog)s test-strategy mysql --strategy complex
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # list-products 命令
    list_parser = subparsers.add_parser('list-products', help='列出指定类别中的产品')
    list_parser.add_argument('category', help='产品类别名称')
    
    # test 命令
    test_parser = subparsers.add_parser('test', help='测试单个产品的提取过程')
    test_parser.add_argument('product', help='产品名称')
    test_parser.add_argument('--language', '-l', choices=['zh-cn', 'en-us'], 
                            default='zh-cn', help='语言版本 (默认: zh-cn)')
    test_parser.add_argument('--dry-run', action='store_true', 
                            help='试运行，不执行实际提取')
    
    # test-strategy 命令
    strategy_parser = subparsers.add_parser('test-strategy', help='测试指定策略的提取效果')
    strategy_parser.add_argument('product', help='产品名称')
    strategy_parser.add_argument('--strategy', '-s', choices=['simple', 'region', 'complex'],
                                required=True, help='要测试的策略')
    strategy_parser.add_argument('--language', '-l', choices=['zh-cn', 'en-us'], 
                                default='zh-cn', help='语言版本 (默认: zh-cn)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 初始化日志系统
    try:
        setup_logging()
        print("📝 日志系统初始化完成")
    except Exception as e:
        print(f"⚠ 日志系统初始化失败: {e}")
    
    # 创建调试器
    try:
        debugger = SingleProductDebugger()
    except Exception as e:
        print(f"❌ 调试器初始化失败: {e}")
        return 1
    
    # 执行命令
    try:
        if args.command == 'list-products':
            debugger.list_products_in_category(args.category)
        elif args.command == 'test':
            debugger.test_product_extraction(args.product, args.language, args.dry_run)
        elif args.command == 'test-strategy':
            debugger.test_specific_strategy(args.product, args.strategy, args.language)
        else:
            print(f"❌ 未知命令: {args.command}")
            return 1
            
        print(f"\n✅ 命令 '{args.command}' 执行完成")
        return 0
        
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        logger.error(f"命令执行失败: {e}", exc_info=True)
        return 1


if __name__ == '__main__':
    sys.exit(main())
