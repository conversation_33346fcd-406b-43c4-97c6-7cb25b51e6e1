<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, 逻辑应用" name="keywords"/>
  <meta content="通过 Azure 逻辑应用，IT 专业人员和开发人员使用易用的可视化设计器对业务流程执行和工作流进行自动化。" name="description"/>
  <title>
   逻辑应用定价 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/logic-apps/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="logic-apps" wacn.date="11/27/2015">
       </tags>
       <!-- <div class="hide-info" style="display:none;">
                            <div class="bg-box">
                                <div class="cover-bg">&nbsp;</div>
                            </div>
                            <div class="msg-box">
                                <div class="pricing-unavailable-message">所选区域不可用</div>
                            </div>
                        </div> -->
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/product-banner-logic-apps.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/logicapps-icon.svg"/>
          <h2>
           逻辑应用
           <span>
            Logic Apps
           </span>
          </h2>
          <h4>
           自动化业务进程
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         通过
         <a href="https://azure.microsoft.com/zh-cn/services/logic-apps/">
          Azure 逻辑应用
         </a>
         ，IT
                                专业人员和开发人员使用易用的可视化设计器对业务流程执行和工作流进行自动化。
        </p>
        <h2>
         定价详细信息
        </h2>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <!-- <div class="dropdown-container software-kind-container" style="display:none;">
                                            <label>OS/软件:</label>
                                            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                <span class="selected-item">Logic Apps</span>
                                                <i class="icon"></i>
                                                <ol class="tab-items">
                                                    <li class="active"><a href="javascript:void(0)"
                                                            data-href="#tabContent1" id="home_logic-apps">Logic Apps</a>
                                                    </li>
                                                </ol>
                                            </div>
                                            <select class="dropdown-select software-box hidden-lg hidden-md"
                                                id="software-box">
                                                <option selected="selected" data-href="#tabContent1" value="Logic Apps">
                                                    Logic Apps</option>
                                            </select>
                                        </div> -->
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
                <li>
                    <a data-href="#east-china3" href="javascript:void(0)" id="#east-china3">
                        中国东部 3
                    </a>
                   </li>
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
            <option data-href="#east-china3" selected="selected" value="east-china3">
                中国东部 3
            </option>
             <option data-href="#north-china3" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTAINER-3 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 2 -->
          <!-- BEGIN: Tab level 2 content 3 -->
          <!-- BEGIN: TAB-CONTROL -->
          <div class="pricing-page-section">
           <h3>
            标准计划(单租户)
           </h3>
           <p>
            逻辑应用标准计划支持在 Windows、Linux 和 Mac
                                            上进行本地开发，提供支持复杂工作流的新布局引擎，并启用自定义连接器扩展。利用容器化运行时在本地、云中或具有虚拟网络功能的內部部署环境中运行逻辑应用。
           </p>
           <table cellpadding="0" cellspacing="0" id="logic-apps-integration-service-environment-table1" width="100%">
            <tr>
             <th align="left" style="width: 48%;">
              <strong>
              </strong>
             </th>
             <th align="left">
              <strong>
               价格/小时
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              vCPU
             </td>
             <td>
              ￥1.250376
             </td>
            </tr>
            <tr>
             <td>
              内存
             </td>
             <td>
              ￥0.08904
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="logic-apps-integration-service-environment-north3-china-table" width="100%">
            <tr>
             <th align="left" style="width: 48%;">
              <strong>
              </strong>
             </th>
             <th align="left">
              <strong>
               价格/小时
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              vCPU
             </td>
             <td>
              ￥2.032147
             </td>
            </tr>
            <tr>
             <td>
              内存
             </td>
             <td>
              ￥0.145517
             </td>
            </tr>
           </table>
           <h3>
            消耗计划(多租户)
           </h3>
           <p>
            一种即用即付定价模型，根据逻辑应用中指定的触发器和操作收费。此计划在多租户环境中运行，还利用连接器为用户提供对 SaaS
                                            应用程序和本地数据源的访问。请访问文档，了解我们不断增长的标准和企业连接器列表。连接器可以轻松解锁防火墙后面的数据和应用程序，安全地连接到基于云的服务，并利用已投资的本地系统的丰富集成。
           </p>
           <p>
            每当逻辑应用定义运行触发器时，都将计量操作和连接器执行数。
           </p>
           <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
             <th align="left">
              <strong>
              </strong>
             </th>
             <th align="left">
              <strong>
               每次执行价格
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              操作
             </td>
             <td>
              ￥0.000254
             </td>
            </tr>
            <tr>
             <td>
              标准连接器
             </td>
             <td>
              ￥0.001272
             </td>
            </tr>
            <tr>
             <td>
              企业连接器
             </td>
             <td>
              ￥0.010176
             </td>
            </tr>
           </table>
          </div>
          <div class="tags-date">
           <div class="ms-date">
            数据保留：￥1.2211 GB/月
           </div>
          </div>
          <h3>
           连接器
          </h3>
          <p>
           逻辑应用连接器为用户提供访问 SaaS 应用程序和本地数据源的权限，单击
           <a href="https://docs.azure.cn/zh-cn/connectors/apis-list" style="background-color:white;float:none;margin:0 6px 0 6px;padding:0;display:inline;">
            此处
           </a>
           了解我们不断增加的标准连接器和企业连接器。连接器可利用已投资的本地系统，轻松解锁被防火墙阻挡的数据和应用程序、安全连接到基于云的服务、利用丰富的集成。
          </p>
          <div id="ji_cheng_zhang_hu">
            <h3>
            集成帐户
            </h3>
            <p>
            集成帐户是 Azure 基于云的解决方案，用于无缝集成业务功能和数据源。通过集成帐户，客户可以利用逻辑应用 B2B/EDI 和 XML 处理功能。
            </p>
          </div>
          
          <!-- END: TAB-CONTROL -->
         </div>
        </div>
       </div>
       <div id="logicAppsSelect">
       </div>
       <style>
        .waterBearSelect {
                                display: flex;
                                align-items: center;
                                margin-top: 30px;
                                width: 500px;
                                display: none;
                            }

                            .waterBearSelect .title {}

                            .waterBearSelect .valueArea {}

                            .waterBearSelect .value {
                                border: solid 1px rgb(169, 169, 169);
                                width: 421.75px;
                                height: 48px;
                                line-height: 48px;
                                padding-left: 10px;
                                cursor: pointer;
                            }

                            .waterBearSelect .value .icon {
                                width: 38px;
                                height: 48px;
                                display: inline-block;
                                float: right;
                                vertical-align: middle;
                                background: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAzMiAzNiI+DQogIDxkZWZzPg0KICAgIDxzdHlsZT4NCiAgICAgIC5jbHMtMSB7DQogICAgICAgIGZpbGw6IG5vbmU7DQogICAgICB9DQoNCiAgICAgIC5jbHMtMiB7DQogICAgICAgIGZpbGw6ICM2MTYxNjE7DQogICAgICB9DQogICAgPC9zdHlsZT4NCiAgPC9kZWZzPg0KICA8ZyBpZD0iZHJvcC1kb3duIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg0IDQpIj4NCiAgICA8cmVjdCBpZD0iRm9vdHByaW50IiBjbGFzcz0iY2xzLTEiIHdpZHRoPSIzMiIgaGVpZ2h0PSIzNiIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoLTQgLTQpIi8+DQogICAgPHBhdGggaWQ9IkFycm93IiBjbGFzcz0iY2xzLTIiIGQ9Ik01LjYxMS00LjI1bDUuMDA3LTUuMDE0LjQ5My40OTMtNS41LDUuNTA3TC4xMTEtOC43NzEuNi05LjI2NFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDQuODg5IDIwLjI2NCkiLz4NCiAgPC9nPg0KPC9zdmc+DQo=) right no-repeat;
                            }

                            .waterBearSelect .options {
                                position: absolute;
                                width: 421.75px;
                                background-color: #fff;
                                box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .15);
                                z-index: 99;
                            }


                            .waterBearSelect .options .option {
                                height: 48px;
                                line-height: 48px;
                                padding-left: 10px;
                                cursor: pointer;
                            }

                            .waterBearSelect .options .selected {
                                background-color: #0078D4;
                                color: #fff;
                            }

                            .waterBearSelect .options .option:hover {
                                background-color: #ebebeb;
                            }

                            .waterBearSelect .options .selected:hover {
                                background-color: #0078D4;
                                color: #fff;
                            }

                            .waterBear .table {
                                width: 100%;
                            }

                            .waterBear .table td {
                                width: 30%;
                            }
       </style>
       <script type="module">
        import { h, Component, render } from '/Static/Preact/preact.module.js';
                            import { useState, useEffect } from '/Static/Preact/hooks.module.min.js';
                            import htm from '/Static/Preact/htm.module.js';

                            // Initialize htm with Preact
                            const html = htm.bind(h);

                            function App() {
                                const [show, setShow] = useState(false);
                                const [list, setList] = useState([
                                    {
                                        title: '中国东部 1',
                                        value: 'a',
                                        id: 1
                                    },
                                    {
                                        title: '中国北部',
                                        value: 'a',
                                        id: 2
                                    },
                                    {
                                        title: '中国东部 2',
                                        value: 'a',
                                        id: 3
                                    },
                                    {
                                        title: '中国北部 2',
                                        value: 'a',
                                        id: 4
                                    },
                                    {
                                        title: '中国东部 3',
                                        value: 'b',
                                        id: 5
                                    },
                                    {
                                        title: '中国北部 3',
                                        value: 'b',
                                        id: 6
                                    },
                                    {
                                        title: '中国东部 3',
                                        value: 'b',
                                        id: 7
                                    }]);

                                const [selected, setSelected] = useState(list[0]);
                                const [tableKey, setTableKey] = useState('a');
                                const table = {
                                    a: [
                                        ['EDI 贸易协议', '1', '1,000'],
                                        ['EDI 贸易合作伙伴', '2', '1,000'],
                                        ['地图', '500', '1,000'],
                                        ['架构', '500', '1,000'],
                                        ['程序集', '25', '1,000'],
                                        ['证书', '2', '1,000'],
                                        ['Batch 配置', '1', '50'],
                                        ['每小时价格', '￥4.104', '￥13.716'],
                                    ],
                                    b: [
                                        ['EDI 贸易协议', '1', '1,000'],
                                        ['EDI 贸易合作伙伴', '2', '1,000'],
                                        ['地图', '500', '1,000'],
                                        ['架构', '500', '1,000'],
                                        ['程序集', '25', '1,000'],
                                        ['证书', '2', '1,000'],
                                        ['Batch 配置', '1', '50'],
                                        ['每小时价格', '￥2.419', '￥8.065'],
                                    ]
                                }

                                useEffect(() => {
                                    setInterval(() => {
                                        const value = document.querySelector('.dropdown-box .selected-item').innerText
                                        document.querySelector('#logicAppsSelect').style.display = "block";
                                        document.querySelector('#ji_cheng_zhang_hu').style.display = "block";
                                        
                                        switch (value) {
                                            case "中国东部 3":
                                                setTableKey('b');
                                                document.querySelector('#logicAppsSelect').style.display = "none";
                                                document.querySelector('#ji_cheng_zhang_hu').style.display = "none";
                                                document.querySelector('#logic-apps-integration-service-environment-table1').style.display = "none";
                                                document.querySelector('#logic-apps-integration-service-environment-north3-china-table').style.display="inline-table";
                                                break;
                                            case "中国北部":
                                                setTableKey('a');
                                                document.querySelector('#logic-apps-integration-service-environment-north3-china-table').style.display="none";
                                                document.querySelector('#logic-apps-integration-service-environment-table1').style.display = "inline-table";
                                                break;
                                            case "中国北部 2":
                                                setTableKey('a');
                                                document.querySelector('#logic-apps-integration-service-environment-north3-china-table').style.display="none";
                                                document.querySelector('#logic-apps-integration-service-environment-table1').style.display = "inline-table";
                                                break;
                                            case "中国东部":
                                                setTableKey('a');
                                                document.querySelector('#logic-apps-integration-service-environment-north3-china-table').style.display="none";
                                                document.querySelector('#logic-apps-integration-service-environment-table1').style.display = "inline-table";
                                                break;
                                            case "中国东部 2":
                                                setTableKey('a');
                                                document.querySelector('#logic-apps-integration-service-environment-north3-china-table').style.display="none";
                                                document.querySelector('#logic-apps-integration-service-environment-table1').style.display = "inline-table";
                                                break;
                                            case "中国北部 3":
                                                setTableKey('b');
                                                document.querySelector('#logic-apps-integration-service-environment-table1').style.display = "none";
                                                document.querySelector('#logic-apps-integration-service-environment-north3-china-table').style.display="inline-table";
                                                break;
                                            default:
                                                break;
                                        }
                                        console.log(value)
                                    }, 1000)
                                }, [])

                                return html`<div class="waterBearSelect"><div class="title">地区：</div>
                                <div class="valueArea">
                                    <div class="value" onClick=${() => {
                                        setShow(!show);
                                    }}>${selected.title}<span class="icon" style="transform: ${show ? 'rotate(180deg)' : 'rotate(0deg)'}"></span></div>
                                    ${show && html`<div class="options">
                                    ${list.map(item => {
                                        return html`<div class="option ${item.id === selected.id ? 'selected' : 'unselected'}" onClick=${() => {
                                            setSelected(item);
                                            setShow(false);
                                        }}>${item.title}</div>`
                                    })}
                                </div>`}
                                </div>
                                </div>
                                <div class="waterBear tables">
                                <table class="table">
                                    <tr>
                                        <td></td>
                                        <td><strong>基本</strong></td>
                                        <td><strong>标准</strong></td>
                                    </tr>
                                    ${table[tableKey].map(item => {
                                        return html`
                                            <tr>
                                                <td>${item[0]}</td>
                                                <td>${item[1]}</td>
                                                <td>${item[2]}</td>
                                            </tr>
                                            `
                                    })
                                    }
                                </table>
                                    </div>
                                `;
                            }

                            render(html`<${App} />`, document.getElementById("logicAppsSelect"));
       </script>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="logic-apps-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         逻辑应用服务预览期间，不提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/legal/sla/index.html" id="pricing_logic-apps_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

                <h2>支持和服务级别协议</h2>
                <p>Azure 支持功能：</p>
                <p>我们免费向用户提供以下支持服务：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left">&nbsp;</th>
                        <th align="left"><strong>是否支持</strong></th>
                    </tr>
                    <tr>
                        <td>计费和订阅管理</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>服务仪表板</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>Web事件提交</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>中断/修复不受限制</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>电话支持</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                    <tr>
                        <td>ICP备案支持</td>
                        <td><i class="icon icon-tick"></i></td>
                    </tr>
                </table>
                <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                <h2>服务热线：</h2>
                <ul>
                    <li>400-089-0365</li>
                    <li>010-84563652</li>
                </ul>
                <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

          -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="oBKiwkao1jHZoOnRJptrbpLmHIetPhhJTzsYWWfx3sUAScGCZkwinpCeSzYWEXQIB23lnNZ-YMNJCx7yyXnkCyEkJms38r9T8wKsavfUOVE1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!--<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
  <!-- <script src='/Static/Scripts/pricing-page-detail.js' type='text/javascript'></script> -->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
