#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug脚本 - 测试src/batch模块的批量处理功能
用于人工测试按category进行产品HTML提取的批量处理

使用方法:
    python debug_batch_processing.py --help
    python debug_batch_processing.py list-categories
    python debug_batch_processing.py test-category database --dry-run
    python debug_batch_processing.py test-category database --language zh-cn --max-products 3
    python debug_batch_processing.py test-all-categories --dry-run
    python debug_batch_processing.py check-files database
"""

import argparse
import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

# 导入必要的模块
from src.core.logging import get_logger, setup_logging
from src.core.product_manager import ProductManager
from src.batch.process_engine import BatchProcessEngine
from src.batch.record_manager import BatchProcessRecordManager
from src.batch.models import BatchProcessStatus

logger = get_logger(__name__)


class BatchProcessDebugger:
    """批量处理调试器"""
    
    def __init__(self):
        """初始化调试器"""
        self.product_manager = ProductManager()
        self.record_manager = BatchProcessRecordManager()
        self.engine = BatchProcessEngine(
            record_manager=self.record_manager,
            max_workers=2,  # 调试时使用较少的工作线程
            max_retries=1
        )
        
        # 设置进度回调
        self.engine.set_progress_callback(self._progress_callback)
        
        print("🔧 批量处理调试器初始化完成")
        print(f"   产品管理器: ✅")
        print(f"   记录管理器: ✅")
        print(f"   处理引擎: ✅ (2 workers, 1 retry)")
    
    def _progress_callback(self, message: str, current: int, total: int):
        """进度回调函数"""
        percentage = (current / total * 100) if total > 0 else 0
        print(f"📊 进度: [{current}/{total}] {percentage:.1f}% - {message}")
    
    def list_categories(self):
        """列出所有可用的产品类别"""
        print("\n📋 可用的产品类别:")
        print("="*60)
        
        try:
            categories = self.product_manager.get_products_by_category()
            
            for i, (category_name, products) in enumerate(categories.items(), 1):
                print(f"{i:2d}. {category_name}")
                print(f"     产品数量: {len(products)}")
                print(f"     产品列表: {', '.join(products[:5])}")
                if len(products) > 5:
                    print(f"               ... 还有 {len(products) - 5} 个产品")
                print()
            
            print(f"📊 总计: {len(categories)} 个类别")
            
        except Exception as e:
            print(f"❌ 获取类别列表失败: {e}")
            logger.error(f"获取类别列表失败: {e}", exc_info=True)
    
    def check_files_for_category(self, category: str, language: str = "zh-cn"):
        """检查指定类别的HTML文件是否存在"""
        print(f"\n🔍 检查类别 '{category}' 的HTML文件 (语言: {language})")
        print("="*60)
        
        try:
            # 获取该类别的产品
            products_by_category = self.product_manager.get_products_by_category(category)
            if category not in products_by_category:
                print(f"❌ 类别 '{category}' 不存在")
                return
            
            products = products_by_category[category]
            print(f"📦 类别 '{category}' 包含 {len(products)} 个产品")
            
            # 检查每个产品的HTML文件
            found_files = []
            missing_files = []
            
            for product_key in products:
                html_path = self.product_manager.get_html_file_path(product_key, language)
                if html_path and os.path.exists(html_path):
                    file_size = os.path.getsize(html_path) / 1024  # KB
                    found_files.append({
                        'product': product_key,
                        'path': html_path,
                        'size_kb': file_size
                    })
                    print(f"✅ {product_key:20s} -> {html_path} ({file_size:.1f} KB)")
                else:
                    missing_files.append({
                        'product': product_key,
                        'expected_path': html_path
                    })
                    print(f"❌ {product_key:20s} -> 文件不存在: {html_path}")
            
            print(f"\n📊 文件检查结果:")
            print(f"   找到文件: {len(found_files)} 个")
            print(f"   缺失文件: {len(missing_files)} 个")
            print(f"   完整率: {len(found_files) / len(products) * 100:.1f}%")
            
            if found_files:
                total_size = sum(f['size_kb'] for f in found_files)
                avg_size = total_size / len(found_files)
                print(f"   总大小: {total_size:.1f} KB")
                print(f"   平均大小: {avg_size:.1f} KB")
            
            return found_files, missing_files
            
        except Exception as e:
            print(f"❌ 检查文件失败: {e}")
            logger.error(f"检查文件失败: {e}", exc_info=True)
            return [], []
    
    def test_category_processing(self, category: str, language: str = "zh-cn", 
                               max_products: Optional[int] = None, 
                               dry_run: bool = False):
        """测试指定类别的批量处理"""
        print(f"\n🧪 测试类别 '{category}' 的批量处理")
        print(f"   语言: {language}")
        print(f"   最大产品数: {max_products or '无限制'}")
        print(f"   试运行: {'是' if dry_run else '否'}")
        print("="*60)
        
        try:
            # 首先检查文件
            found_files, missing_files = self.check_files_for_category(category, language)
            
            if not found_files:
                print(f"❌ 类别 '{category}' 没有可用的HTML文件")
                return
            
            # 限制产品数量（用于调试）
            if max_products and len(found_files) > max_products:
                found_files = found_files[:max_products]
                print(f"🔧 限制处理产品数量为: {max_products}")
            
            if dry_run:
                print(f"\n🔍 试运行模式 - 将处理以下产品:")
                for i, file_info in enumerate(found_files, 1):
                    print(f"   {i:2d}. {file_info['product']} ({file_info['size_kb']:.1f} KB)")
                print(f"\n💡 使用 --no-dry-run 参数执行实际处理")
                return
            
            # 执行实际的批量处理
            print(f"\n🚀 开始批量处理...")
            start_time = datetime.now()
            
            report = self.engine.process_product_group(
                group_name=category,
                output_dir=f"debug_output/{language}",
                force_refresh=True,  # 强制刷新以便调试
                html_base_dir="data/prod-html",
                language=language
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 打印详细报告
            self._print_detailed_report(report, duration)
            
        except Exception as e:
            print(f"❌ 测试处理失败: {e}")
            logger.error(f"测试处理失败: {e}", exc_info=True)
    
    def test_all_categories(self, language: str = "zh-cn", dry_run: bool = False):
        """测试所有类别的批量处理"""
        print(f"\n🧪 测试所有类别的批量处理")
        print(f"   语言: {language}")
        print(f"   试运行: {'是' if dry_run else '否'}")
        print("="*60)
        
        try:
            categories = self.product_manager.get_products_by_category()
            
            if dry_run:
                print(f"\n🔍 试运行模式 - 将处理以下类别:")
                for i, (category_name, products) in enumerate(categories.items(), 1):
                    found_files, _ = self.check_files_for_category(category_name, language)
                    print(f"   {i:2d}. {category_name:15s} - {len(found_files)} 个可用文件")
                print(f"\n💡 使用 --no-dry-run 参数执行实际处理")
                return
            
            # 执行实际的批量处理
            print(f"\n🚀 开始处理所有类别...")
            start_time = datetime.now()
            
            reports = self.engine.process_all_products(
                output_dir=f"debug_output/{language}",
                force_refresh=True,
                html_base_dir="data/prod-html",
                language=language
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 打印汇总报告
            self._print_summary_report(reports, duration)
            
        except Exception as e:
            print(f"❌ 测试所有类别失败: {e}")
            logger.error(f"测试所有类别失败: {e}", exc_info=True)
    
    def _print_detailed_report(self, report, duration: float):
        """打印详细的处理报告"""
        print(f"\n📊 处理报告 (批次: {report.batch_id})")
        print("="*60)
        
        print(f"开始时间:        {report.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"结束时间:        {report.end_time.strftime('%Y-%m-%d %H:%M:%S') if report.end_time else 'N/A'}")
        print(f"总耗时:          {duration:.1f}s")
        print(f"处理产品数:      {report.total_products}")
        print(f"成功:            {report.successful_products} ({report.success_rate:.1f}%)")
        print(f"失败:            {report.failed_products}")
        
        if report.average_processing_time_ms:
            print(f"平均处理时间:    {report.average_processing_time_ms:.0f}ms")
        
        # 策略使用情况
        if report.products_by_strategy:
            print(f"\n🎯 策略使用情况:")
            for strategy, count in report.products_by_strategy.items():
                print(f"   {strategy}: {count} 个产品")
        
        # 显示失败的产品
        failed_results = [r for r in report.processing_results if not r.success]
        if failed_results:
            print(f"\n❌ 失败的产品 ({len(failed_results)}):")
            for result in failed_results:
                error_msg = result.error_message[:50] + "..." if len(result.error_message) > 50 else result.error_message
                print(f"   - {result.product_key}: {error_msg}")
        
        # 显示成功的产品
        successful_results = [r for r in report.processing_results if r.success]
        if successful_results:
            print(f"\n✅ 成功的产品 ({len(successful_results)}):")
            for result in successful_results[:5]:  # 只显示前5个
                print(f"   - {result.product_key}: {result.processing_time_ms}ms -> {result.output_file_path}")
            if len(successful_results) > 5:
                print(f"   ... 还有 {len(successful_results) - 5} 个成功的产品")
    
    def _print_summary_report(self, reports: Dict[str, Any], duration: float):
        """打印汇总报告"""
        print(f"\n📊 所有类别处理汇总报告")
        print("="*60)
        
        total_products = 0
        total_successful = 0
        total_failed = 0
        
        print(f"总耗时:          {duration:.1f}s")
        print(f"\n各类别处理结果:")
        
        for category_name, report in reports.items():
            total_products += report.total_products
            total_successful += report.successful_products
            total_failed += report.failed_products
            
            print(f"   {category_name:15s}: {report.successful_products:2d}/{report.total_products:2d} "
                  f"({report.success_rate:5.1f}%) - {report.duration_seconds:.1f}s")
        
        overall_success_rate = (total_successful / max(total_products, 1)) * 100
        print(f"\n总计:")
        print(f"   处理产品数:      {total_products}")
        print(f"   成功:            {total_successful} ({overall_success_rate:.1f}%)")
        print(f"   失败:            {total_failed}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='批量处理调试工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s list-categories
  %(prog)s check-files database
  %(prog)s test-category database --dry-run
  %(prog)s test-category database --language zh-cn --max-products 3
  %(prog)s test-all-categories --dry-run
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # list-categories 命令
    list_parser = subparsers.add_parser('list-categories', help='列出所有可用的产品类别')
    
    # check-files 命令
    check_parser = subparsers.add_parser('check-files', help='检查指定类别的HTML文件')
    check_parser.add_argument('category', help='产品类别名称')
    check_parser.add_argument('--language', '-l', choices=['zh-cn', 'en-us'], 
                             default='zh-cn', help='语言版本 (默认: zh-cn)')
    
    # test-category 命令
    test_parser = subparsers.add_parser('test-category', help='测试指定类别的批量处理')
    test_parser.add_argument('category', help='产品类别名称')
    test_parser.add_argument('--language', '-l', choices=['zh-cn', 'en-us'], 
                            default='zh-cn', help='语言版本 (默认: zh-cn)')
    test_parser.add_argument('--max-products', '-m', type=int, 
                            help='限制处理的产品数量 (用于调试)')
    test_parser.add_argument('--dry-run', action='store_true', 
                            help='试运行，不执行实际处理')
    
    # test-all-categories 命令
    test_all_parser = subparsers.add_parser('test-all-categories', help='测试所有类别的批量处理')
    test_all_parser.add_argument('--language', '-l', choices=['zh-cn', 'en-us'], 
                                default='zh-cn', help='语言版本 (默认: zh-cn)')
    test_all_parser.add_argument('--dry-run', action='store_true', 
                                help='试运行，不执行实际处理')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 初始化日志系统
    try:
        setup_logging()
        print("📝 日志系统初始化完成")
    except Exception as e:
        print(f"⚠ 日志系统初始化失败: {e}")
    
    # 创建调试器
    try:
        debugger = BatchProcessDebugger()
    except Exception as e:
        print(f"❌ 调试器初始化失败: {e}")
        return 1
    
    # 执行命令
    try:
        if args.command == 'list-categories':
            debugger.list_categories()
        elif args.command == 'check-files':
            debugger.check_files_for_category(args.category, args.language)
        elif args.command == 'test-category':
            debugger.test_category_processing(
                args.category, args.language, args.max_products, args.dry_run
            )
        elif args.command == 'test-all-categories':
            debugger.test_all_categories(args.language, args.dry_run)
        else:
            print(f"❌ 未知命令: {args.command}")
            return 1
            
        print(f"\n✅ 命令 '{args.command}' 执行完成")
        return 0
        
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        logger.error(f"命令执行失败: {e}", exc_info=True)
        return 1


if __name__ == '__main__':
    sys.exit(main())
