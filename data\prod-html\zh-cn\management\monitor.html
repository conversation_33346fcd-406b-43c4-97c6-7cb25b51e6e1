<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure 监控器价格, Azure 监控器报价" name="keywords"/>
  <meta content="了解Azure 监控器的价格详情。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。" name="description"/>
  <title>
   Azure 监控器价格_预算报价 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/monitor/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
   @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="monitor" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <style>
        .monitor-icon {
                            width: 48px;
                            height: 48px;
                            display: inline-block;
                            float: left;
                            background: url("data:image/svg+xml;base64,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") no-repeat center;
                        }
       </style>
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product_banner_monitor.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <div class="monitor-icon">
          </div>
          <h2>
           Azure 监控器
           <span>
            Azure Monitor
           </span>
          </h2>
          <h4>
           高精度、实时监控所有 Azure 资源的数据
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure Monitor
                            提供用于收集、分析和处理来自云与本地环境的遥测数据的综合解决方案，可将应用程序和服务的可用性和性能最大化。它可以帮助你了解应用程序的性能，并主动识别影响应用程序及其所依赖资源的问题。
        </p>
       </div>
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              azure-monitor
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_azure-monitor">
                azure-monitor
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="azure-monitor">
              azure-monitor
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 3
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
                <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                 中国东部 3
                </a>
              </li>
              <li>
               <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                中国北部 3
               </a>
              </li>
              <li>
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china3" selected="selected" value="east-china3">
              中国东部 3
             </option>
             <option data-href="#north-china3" value="north-china3">
              中国北部 3
             </option>
             <option data-href="#east-china2" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Table1-Content-->
          <h2>
           定价详细信息
          </h2>
          <h3>
           日志数据引入
          </h3>
          <p>
           Log Analytics 和 Application Insights 对其引入的数据收费。
                                    对于 Log Analytics 和基于工作区的Application Insights，
                                    可将数据引入为两种不同类型的日志:
           <strong>
            分析日志
           </strong>
           和
           <strong>
            基本日志
           </strong>
           。借助分析日志，
                                    数据可用于功能强大的分析查询，其保留选项最长为 2 年，数据存档最长为 7 年。使用基本日志，
                                    你可以通过交互方式搜索最长 8 天的数据，并且此数据可存档长达 7 年。基本日志可用于降低存储用于调试、
                                    故障排除和审核的大量详细日志的成本，但不能用于深度分析和警报。
          </p>
          <!-- <p>Log Analytics 和 Application Insights 对其>引入的数据收费。对于 Log Analytics
                                    和基于工作区的Application Insights，可将数据引入为两种不同类型的日志:
                                    分析日志和基本日志。借助分析日志，数据可用于功能强大的分析查询，其保留选项最长为 2 年，数据存档最长为 7 年。使用基本日志，你可以通过交互方式搜索最长 8
                                    天的数据，并且此数据可存档长达 7 年。基本日志可用于降低存储用于调试、故障排除和审核的大量详细日志的成本，但不能用于深度分析和警报。</p> -->
          <p>
           可计费数据量是使用客户友好、经济高效的方法计算的。计费数据量仅定义为将存储的数据的大小，不包括一组标准列和任何 JSON
                                    包装器(该包装器属于接收后引入的数据的一部分)。因此，可计费数据量明显小于整个 JSON 打包事件的大小，通常小于
                                    50%。在估算成本时，必须了解此计费数据大小计算。
           <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/cost-logs">
            了解详细信息。
           </a>
          </p>
          <h3>
           分析日志
          </h3>
          <p>
           可以通过两种方式支付将数据引入为分析日志的费用:
                                    即用即付和承诺层级。即用即付定价提供了灵活的按使用情况付费，即只需对引入的数据量收费。如果使用承诺层级定价，则将向你收取固定的可预测费用，起价为每天
                                    100
                                    GB。如果引入数据超过承诺层级，则按当前层级每 GB 价格计费。承诺层级方式将根据所选承诺层级为数据引入提供折扣。承诺层级具有 31 天承诺期(
           <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/cost-logs">
            详细了解
           </a>
           )。
                                    对于 Application Insights 用户，资源必须
           <a href="https://docs.azure.cn/zh-cn/azure-monitor/app/convert-classic-resource">
            基于工作区
           </a>
           才能利用承诺层级。一些数据类型，
                                    包括
           <a href="https://docs.azure.cn/zh-cn/azure-monitor/essentials/activity-log">
            Azure
                                    活动日志
           </a>
           ，
           <a href="https://docs.azure.cn/zh-cn/azure-monitor/essentials/activity-log">
            不会产生数据引入费用
           </a>
           。引入为基本日志的数据(参见下文)不会作为即用即付分析或按承诺层级计费。
          </p>
          <table cellpadding="0" cellspacing="0" width="100%">
           <thead>
            <tr>
             <th align="left" style="width: 326px;">
              <strong>
               容量
              </strong>
             </th>
             <th align="left" style="width: 384px;">
              <strong>
               价格
              </strong>
             </th>
             <th align="left" style="width: 384px;">
              <strong>
               每 GB
                                            有效价格
               <sup>
                1
               </sup>
              </strong>
             </th>
             <th align="left">
              <strong>
               折扣力度优于即用即付
              </strong>
             </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
              即用即付
             </td>
             <td class="left_align">
              ¥ 23.4/GB
                                            (包含每个客户每月 5 GB)
             </td>
             <td class="left_align">
              ¥ 23.4/GB
             </td>
             <td class="left_align">
              0%
             </td>
            </tr>
            <tr>
             <td class="left_align">
              100 GB/天
             </td>
             <td class="left_align">
              ¥ 1994.5/天
             </td>
             <td class="left_align">
              ¥ 19.94/GB
             </td>
             <td class="left_align">
              15%
             </td>
            </tr>
            <tr>
             <td class="left_align">
              200 GB/天
             </td>
             <td class="left_align">
              ¥ 3744.77/天
             </td>
             <td class="left_align">
              ¥ 18.72/GB
             </td>
             <td class="left_align">
              20%
             </td>
            </tr>
            <tr>
             <td class="left_align">
              300 GB/天
             </td>
             <td class="left_align">
              ¥ 5495.04/天
             </td>
             <td class="left_align">
              ¥ 18.31/GB
             </td>
             <td class="left_align">
              22%
             </td>
            </tr>
            <tr>
             <td class="left_align">
              400 GB/天
             </td>
             <td class="left_align">
              ¥ 7163.9/天
             </td>
             <td class="left_align">
              ¥ 17.9/GB
             </td>
             <td class="left_align">
              23%
             </td>
            </tr>
            <tr>
             <td class="left_align">
              500 GB/天
             </td>
             <td class="left_align">
              ¥ 8802.24/天
             </td>
             <td class="left_align">
              ¥ 17.6/GB
             </td>
             <td class="left_align">
              25%
             </td>
            </tr>

            <!-- 2024-3-5 添加 -->
            <tr>
                <td class="left_align">
                    1,000 GB/天
                </td>
                <td class="left_align">
                    ¥ 17,299.2/天
                </td>
                <td class="left_align">
                    ¥ 17.3/GB
                </td>
                <td class="left_align">
                    26%
                </td>
            </tr>
            <tr>
                <td class="left_align">
                    2,000 GB/天
                </td>
                <td class="left_align">
                    ¥ 33,784.32/天
                </td>
                <td class="left_align">
                    ¥ 16.7/GB
                </td>
                <td class="left_align">
                    28%
                </td>
            </tr>
            <tr>
                <td class="left_align">
                    5,000 GB/天
                </td>
                <td class="left_align">
                    ¥ 81,916.8 /天
                </td>
                <td class="left_align">
                    ¥ 16.38/GB
                </td>
                <td class="left_align">
                    30%
                </td>
            </tr>
            <tr>
                <td class="left_align">
                    10,000 GB/天
                </td>
                <td class="left_align">
                    ¥ 159,152.64/天
                </td>
                <td class="left_align">
                    ¥ 15.9/GB
                </td>
                <td class="left_align">
                    32%
                </td>
            </tr>
            <tr>
                <td class="left_align">
                    25,000 GB/天
                </td>
                <td class="left_align">
                    ¥ 386,179.2/天
                </td>
                <td class="left_align">
                    ¥ 15.45/GB
                </td>
                <td class="left_align">
                    34%
                </td>
            </tr>
            <tr>
                <td class="left_align">
                    50,000 GB/天
                </td>
                <td class="left_align">
                    ¥ 748,953.6/天
                </td>
                <td class="left_align">
                    ¥ 14.98/GB
                </td>
                <td class="left_align">
                    36%
                </td>
            </tr>                                                              
           </tbody>
          </table>
          <div class="tags-date">
           <div class="ms-date">
            <sup>
             1
            </sup>
            数据大小以 GB 为单位进行测量（10^9 字节）。其计算的详细信息可用于
            <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/cost-logs#pricing-model">
             Log
                                        Analytics
            </a>
            和
            <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/cost-logs#data-size-calculation">
             Application
                                        Insights
            </a>
            。
           </div>
          </div>
          <div class="scroll-table" style="display: block;">
           <h3>
            基本日志
           </h3>
           <p>
            可将无需全套分析功能即可托管的特定类型大量数据作为
            <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/basic-logs-configure?tabs=portal-1%2Capi-2">
             基本日志
            </a>
            引入到 Log
                                        Analytics 中。基本日志只包含 8 天的保留期，而且最多可以存档 7 年。在基本日志中搜索数据需要额外计费。目前尚未启用基本日志搜索的计费。搜索计费开始之前将提前通知。
           </p>
           <table cellpadding="0" cellspacing="0" id="monitor-basic-log" width="100%">
            <thead>
             <tr>
              <th align="left">
               <strong>
                功能
               </strong>
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
            </thead>
            <tbody>
             <tr>
              <td class="left_align">
               基本日志数据引入
              </td>
              <td class="left_align">
               每 GB 引入数据为 ￥5.088
              </td>
             </tr>
             <tr>
              <td class="left_align">
               基本日志查询
              </td>
              <td class="left_align">
               每 GB 扫描数据为 ￥0.05088
              </td>
             </tr>
            </tbody>
           </table>
          </div>
          <!-- <h3>即用即付</h3>
                <p>使用即用即付定价时，对于引入到 Log Analytics 工作区中的数据，我们将按每十亿字节 (GB) 进行收费。</p>
                <div class="tags-date">
                    <div class="ms-date">*以下价格均为含税价格。</div>
                </div>
                <table  cellpadding="0" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                        <th align="left" style="width: 327px;"><strong>功能</strong></th>
                        <th align="left" style="width: 384px;"><strong>内附免费单位数</strong></th>
                        <th align="left"><strong>价格</strong></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="left_align">数据引入</td>
                            <td class="left_align">每个客户每月 5 GB<sup>3</sup></td>
                            <td class="left_align">¥23.4/GB</td>
                        </tr>
                    </tbody>
                </table>
                <div class="tags-date">
                    <div class="ms-date"><sup>3</sup>每个客户每月引入 Azure Log Analytics 服务的前 5 GB 数据免费。</div>
                </div> -->
          <h3>
           数据保留
          </h3>
          <p>
           引入到 Azure Monitor Log Analytics 工作区中的每 GB 数据在前 31 天是免费保留的。对于前 31
                                    天过后仍然保留的数据，我们将按下面列出的数据保留价格进行收费。
          </p>
          <div class="tags-date">
           <div class="ms-date">
            *以下价格均为含税价格。
           </div>
          </div>
          <table cellpadding="0" cellspacing="0" width="100%">
           <thead>
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               内附免费单位数
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
              数据保留
             </td>
             <td class="left_align">
              31 天
              <sup>
               4
              </sup>
             </td>
             <td class="left_align">
              ¥1.02/GB/月
             </td>
            </tr>
           </tbody>
          </table>
          <div class="tags-date">
           <div class="ms-date">
            <sup>
             4
            </sup>
            对于启用了 Azure Sentinel 的工作区，数据将免费保留 90 天。
           </div>
          </div>
          <div class="scroll-table" style="display: block;">
           <h3>
            日志数据存档和还原
           </h3>
           <p>
            引入到 Log Analytics 的数据还可以
            <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/data-retention-archive?tabs=portal-1%2Cportal-2">
             存档
            </a>
            ，
                                    因此其存储成本低于正常分析保留。搜索存档日志是使用异步
            <a href="https://learn.microsoft.com/zh-cn/azure/azure-monitor/logs/search-jobs">
             搜索作业
            </a>
            完成的，这会产生执行搜索时扫描数据的成本以及引入搜索结果的成本(按正常的日志数据引入价格计算)。
                                    存档日志还可以
            <a href="https://learn.microsoft.com/zh-cn/azure/azure-monitor/logs/restore?tabs=api-1%2Capi-2">
             还原
            </a>
            ，以启用完整的交互式分析查询功能。根据数据保持还原的时间和还原的数据量(受最短还原持续时间和数据量的约束)，维护还原的日志会产生每天和每 GB 按比例计算的成本。
                                    对还原的日志进行查询不收取任何费用。详细了解
            <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/log-analytics-workspace-overview">
             数据存档、还原和搜索作业
            </a>
            。
           </p>
           <table cellpadding="0" cellspacing="0" id="monitor-log-data-archive" width="100%">
            <thead>
             <tr>
              <th align="left">
               <strong>
                功能
               </strong>
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
            </thead>
            <tbody>
             <tr>
              <td class="left_align">
               数据存档
              </td>
              <td class="left_align">
               ¥0.2034/GB/月
              </td>
             </tr>
             <tr>
              <td class="left_align">
               存档的日志搜索作业
              </td>
              <td class="left_align">
               每GB扫描数据为￥0.05088
              </td>
             </tr>
             <tr>
              <td class="left_align">
               数据还原
              </td>
              <td class="left_align">
               ￥1.02/GB/天（2TB，最小为12小时）
               <sub>
                1
               </sub>
              </td>
             </tr>
            </tbody>
           </table>
           <div class="tags-date">
            <div class="ms-date">
             <sup>
              1
             </sup>
             如果还原保留时间少于 12 小时，则还原将按 12 小时的最短持续时间计费。如果还原的数据少于 2 TB，则已还原数据的计费量将在保留还原的每天(或部分日期)向上舍入到 2 TB。
            </div>
           </div>
          </div>

          <h3>
            导出
           </h3>
           <p>
            从 Log Analytics 工作区中导出的每 GB 数据的 <a href="https://docs.azure.cn/zh-cn/azure-monitor/logs/logs-data-export?tabs=portal">
            Log Analytics 数据导出
            </a> 帐单。此页中“平台日志”部分下面介绍了通过诊断设置导出数据。
           </p>

           <table cellpadding="0" cellspacing="0" width="100%" id = "monitor-export-n3e3">
            <thead>
             <tr>
              <th align="left">
               <strong>
                功能
               </strong>
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
            </thead>
            <tbody>
             <tr>
              <td class="left_align">
                Log Analytics 数据导出
              </td>
              <td class="left_align">
                ￥1.02/GB
                <sup>
                    1
                </sup> 
              </td>
             </tr>
            </tbody>
           </table>
           <div class="tags-date">
            <div class="ms-date">
                <sup>
                    1
                </sup>
                Log Analytics 数据导出功能导出的数据大小是导出的 JSON 格式数据中的字节数。1 GB = 10^9 字节。
            </div>
           </div>

          <h3>
           Application Insights
          </h3>
          <p>
           根据应用程序发送的遥测数据量和选择运行的 Web 测试数对 Application Insights 计费。遥测数据按 Azure Log Analytics
                                    数据引入费率计费。
          </p>
          <div class="tags-date">
           <div class="ms-date">
            *以下价格均为含税价格。
           </div>
          </div>
          <table cellpadding="0" cellspacing="0" width="100%">
           <thead>
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
              标准Web测试
             </td>
             <td class="left_align">
              ￥50.088 / 每10K个计划的测试执行
             </td>
            </tr>
            <tr>
             <td class="left_align">
              Ping Web测试
             </td>
             <td class="left_align">
              免费
             </td>
            </tr>
           </tbody>
          </table>

          <h3>
            度量值
          </h3>

          <h3>
            本机指标
          </h3>
          <p>
            本机指标包括平台指标和自定义指标。自定义指标的成本基于引入的样本数和针对查询进行的 API 调用数。平台和自定义指标将保留 90 天。尚未为自定义指标启用计费。
          </p>
          <table cellpadding="0" cellspacing="0" width="100%">
           <thead>
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               内附免费单位数
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
                平台指标引入 <sup>5</sup>
             </td>
             <td class="left_align">
                无限制
             </td>
             <td class="left_align">
                免费
             </td>
            </tr>
            <tr>
                <td class="left_align">
                    平台指标查询
                </td>
                <td class="left_align">
                    每月前 1,000,000 个 API 调用
                </td>
                <td class="left_align">
                    ￥0.101 / 1,000 API 调用
                </td>
               </tr>
           </tbody>
          </table>

          <div class="tags-date">
            <div class="ms-date">
             <sup>
              5
             </sup> 
             平台指标免费纳入每个 计费账户，并包括来自 Azure 资源、服务和第一方解决方案的选择指标。请参阅<a href="https://docs.microsoft.com/zh-cn/azure/monitoring-and-diagnostics/monitoring-supported-metrics">标准指标列表</a>。
            </div>
           </div>

         
          <h3>
            Prometheus 指标的托管服务
          </h3>
          <p>
            Prometheus 指标的成本基于为查询引入和处理的样本数。指标引入成本包括 18 个月的数据保留期。
          </p>
          <table cellpadding="0" cellspacing="0" width="100%">
           <thead>
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
                指标引入
             </td>
             <td class="left_align">
                已引入￥1.72/1000 万个样本
             </td>
            </tr>
            <tr>
                <td class="left_align">
                    指标查询  <sup>6</sup>
                </td>
                
                <td class="left_align">
                    已处理 ￥0.01 / 1 千万个样本
                </td>
               </tr>
           </tbody>
          </table>

          <div class="tags-date">
            <div class="ms-date">
                <sup>6</sup> 处理的示例是使用 Prometheus 查询语言(PromQL)在给定时间范围内查询的数据点总数。Prometheus 指标警报定价仅基于指标预览定价。
            </div>
           </div>
         

         
          <h3>
           预警规则
          </h3>
          <p>
           <a href="https://docs.azure.cn/zh-cn/azure-monitor/alerts/alerts-overview">预警规则</a>根据它监视的信号类型和数量收费。信号可以是资源指标、日志或活动日志。监视多个信号的预警规则的成本是监视每个信号和任何已启用功能的成本之和。<a href="https://docs.azure.cn/zh-cn/azure-monitor/alerts/alerts-types">指标预警规则</a>
           按每个受监视的时序计费。<a href="https://docs.azure.cn/zh-cn/azure-monitor/alerts/alerts-types">日志预警规则</a>
           按执行查询的时间间隔计费。如果查询生成多个维度(<a href="https://docs.azure.cn/zh-cn/azure-monitor/alerts/alerts-types">大规模日志监视</a>)，则评估的每个维度(时序)都会产生额外费用。对于<a href="https://docs.azure.cn/zh-cn/azure-monitor/alerts/alerts-types#activity-log-alerts">活动日志预警规则</a>
           或禁用状态下的其他预警规则，不收取任何费用。
          </p>
         
          <table cellpadding="0" cellspacing="0" width="100%">
           <thead>
            <tr>
             <th align="left">
              <strong>
                警报类型
              </strong>
             </th>
             <th align="left">
              <strong>
               内附免费单位数
              </strong>
             </th>
             <th align="left">
              <strong>
                警报规则价格<br>
                (每月)
              </strong>
             </th>
             <th align="left">
                <strong>
                    时序价格<br>
                  (每月)
                </strong>
               </th>
               <th align="left">
                <strong>
                    带动态阈值的指标警报的额外成本
                        <sup>
                        4
                       </sup>
                </strong>
               </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
                活动日志警报
             </td>
             <td class="left_align">
                每个订阅限制为 100 条规则
             </td>
             <td class="left_align">
                N/A
             </td>
             <td class="left_align">
                N/A
            </td>
            <td class="left_align">
                N/A
            </td>
            </tr>
            <tr>
             <td class="left_align">
              本机指标
              <sup>
                1,2
               </sup>
             </td>
             <td class="left_align">
                每月 10 个受监视的时间指标系列
                <sup>
                    3
                </sup>
             </td>
             <td class="left_align">
                N/A
             </td>
             <td class="left_align">
                ￥1.08
             </td>
             <td class="left_align">
                ￥1.08
             </td>
            </tr>
            <tr>
                <td class="left_align">
                    Prometheus 指标的托管服务
                </td>
                <td class="left_align">
                    Prometheus 指标警报仅收取 Prometheus 指标查询的费用(见上文)。
                </td>
                <td class="left_align">
                   N/A
                </td>
                <td class="left_align">
                   N/A
               </td>
               <td class="left_align">
                   N/A
               </td>
               </tr>
            <tr>
             <td class="left_align">
                日志警报(15 分钟频率)
             </td>
             <td class="left_align">
                日志警报规则中包含第一个时序
             </td>
             <td class="left_align">
                ￥5.4
             </td>
             <td class="left_align">
                ￥0.5
             </td>
             <td class="left_align">
                N/A
             </td>
            </tr>
            <tr>
             <td class="left_align">
                日志警报(10 分钟频率)
             </td>
             <td class="left_align">
                日志警报规则中包含第一个时序
             </td>
             <td class="left_align">
                ￥10.8
             </td>
             <td class="left_align">
                ￥1.02
             </td>
             <td class="left_align">
                N/A
             </td>
            </tr>

            <tr>
                <td class="left_align">
                    日志警报(5 分钟频率)
                </td>
                <td class="left_align">
                    日志警报规则中包含第一个时序
                </td>
                <td class="left_align">
                    ￥16.18
                </td>
                <td class="left_align">
                    ￥1.52
                </td>
                <td class="left_align">
                   N/A
                </td>
               </tr>

               <tr>
                <td class="left_align">
                    日志警报(1 分钟频率)
                </td>
                <td class="left_align">
                    日志警报规则中包含第一个时序
                </td>
                <td class="left_align">
                    ￥30.5
                </td>
                <td class="left_align">
                    ￥3
                </td>
                <td class="left_align">
                   N/A
                </td>
               </tr>
           </tbody>
          </table>
          <div class="tags-date">
           <div class="ms-date">
            1.此处列出的价格表示监视单个资源的单个时间指标系列的预警规则的价格。
           </div>
           <br/>
           <div class="ms-date">
            2.定价适用于“警报”下新一代的可用指标预警规则，上一代的预警规则 (Alerts Classic) 将继续免费提供。
            <ul style="margin: 0;">
                <li style="font-size: 12px;height: 14px;padding: 0;line-height: 14px;">
                    提供 90 天内的活动日志，无需任何费用。要保留 90 天以上的活动日志数据，可将活动日志数据路由到存储帐户或事件中心。存储和事件中心将分别收取相应费用。API 调用拉取活动日志数据不会产生任何费用。
                </li>
                <li style="font-size: 12px;height: 14px;padding: 0;line-height: 14px;">
                    作为 Azure 安全中心(ASC)一部分提供的警报当前不收取费用。
                </li>
            </ul>
           </div>
           <br/>
           <div class="ms-date">
            3.大规模日志监视的定价从计划查询规则 API 版本 2021-02-01 正式发布后开始适用
           </div>
           <br/>
           <div class="ms-date">
            4.动态警报将针对动态阈值功能和基础指标警报进行计费。
           </div>
          </div>
          <h3>
           通知
          </h3>
          <p>
           根据你选择发送的通知类型和数量来计费。
          </p>
          <table cellpadding="0" cellspacing="0" width="100%">
           <thead>
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               内附免费单位数
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
           </thead>
           <tbody>
            <tr>
             <td class="left_align">
              电子邮件
             </td>
             <td class="left_align">
              每月电子邮件数 1,000
             </td>
             <td class="left_align">
              ¥20.3/100,000 电子邮件
             </td>
            </tr>
            <!-- <tr>
                        <td class="left_align">推送通知（面向 Azure 应用）</td>
                        <td class="left_align">每月 1,000 个通知</td>
                        <td class="left_align">¥20.3/100,000 个通知</td>
                    </tr> -->
            <tr>
             <td class="left_align">
              Webhook
             </td>
             <td class="left_align">
              每月 100,000 个 Web 挂钩
             </td>
             <td class="left_align">
              ¥6.1/1,000,000 个 Web 挂钩
             </td>
            </tr>
            <tr>
             <td class="left_align">
              短信
             </td>
             <td class="left_align">
             </td>
             <td class="left_align">
             </td>
            </tr>
            <tr>
             <td class="left_align">
              <strong>
               国家/地区代码
              </strong>
             </td>
             <td class="left_align">
              <strong>
               内附免费单位数
              </strong>
             </td>
             <td class="left_align">
              <strong>
               价格
              </strong>
             </td>
            </tr>
            <tr>
             <td>
              中国（+ 86）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.219165/每条短信
             </td>
            </tr>
            <tr>
             <td>
              中国香港特别行政区（+ 852）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.179988/每条短信
             </td>
            </tr>
            <tr>
             <td>
              澳大利亚（+ 61）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.36252/每条短信
             </td>
            </tr>
            <tr>
             <td>
              奥地利 (+43)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.88/每条短信
             </td>
            </tr>
            <tr>
             <td>
              比利时 (+32)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.643/每条短信
             </td>
            </tr>
            <tr>
             <td>
              巴西（+ 55）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.148188/每条短信
             </td>
            </tr>
            <tr>
             <td>
              智利 (+56)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.316/每条短信
             </td>
            </tr>
            <tr>
             <td>
              捷克共和国 (+420)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.535/每条短信
             </td>
            </tr>
            <tr>
             <td>
              丹麦 (+45)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.269/每条短信
             </td>
            </tr>
            <tr>
             <td>
              爱沙尼亚 (+372)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.722/每条短信
             </td>
            </tr>
            <tr>
             <td>
              芬兰 (+358)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.757/每条短信
             </td>
            </tr>
            <tr>
             <td>
              法国（+ 33）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.34344/每条短信
             </td>
            </tr>
            <tr>
             <td>
              德国（+ 49）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.634855/每条短信
             </td>
            </tr>
            <tr>
             <td>
              印度（+ 91）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.026711/每条短信
             </td>
            </tr>
            <tr>
             <td>
              爱尔兰（+ 353）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.251856/每条短信
             </td>
            </tr>
            <tr>
             <td>
              以色列 (+972)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.1/每条短信
             </td>
            </tr>
            <tr>
             <td>
              意大利 (+39)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.532/每条短信
             </td>
            </tr>
            <tr>
             <td>
              日本（+ 81）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.29256/每条短信
             </td>
            </tr>
            <tr>
             <td>
              约旦 (+962)
             </td>
             <td>
              –
             </td>
             <td>
              ¥1.241/每条短信
             </td>
            </tr>
            <tr>
             <td>
              卢森堡 (+352)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.392/每条短信
             </td>
            </tr>
            <tr>
             <td>
              马来西亚 (+60)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.611/每条短信
             </td>
            </tr>
            <tr>
             <td>
              墨西哥 (+52)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.168/每条短信
             </td>
            </tr>
            <tr>
             <td>
              荷兰（+ 31）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.8586/每条短信
             </td>
            </tr>
            <tr>
             <td>
              新西兰 (+64)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.772/每条短信
             </td>
            </tr>
            <tr>
             <td>
              挪威 (+47)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.551/每条短信
             </td>
            </tr>
            <tr>
             <td>
              葡萄牙 (+351)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.221/每条短信
             </td>
            </tr>
            <tr>
             <td>
              罗马尼亚 (+40)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.545/每条短信
             </td>
            </tr>
            <tr>
             <td>
              俄罗斯 (+7)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.507/每条短信
             </td>
            </tr>
            <tr>
             <td>
              新加坡（+ 65）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.197605/每条短信
             </td>
            </tr>
            <tr>
             <td>
              南非（+ 27）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.158491/每条短信
             </td>
            </tr>
            <tr>
             <td>
              韩国（+ 82）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.29892/每条短信
             </td>
            </tr>
            <tr>
             <td>
              西班牙 (+34)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.56/每条短信
             </td>
            </tr>
            <tr>
             <td>
              瑞士 (+41)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.392/每条短信
             </td>
            </tr>
            <tr>
             <td>
              台湾 (+886)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.399/每条短信
             </td>
            </tr>
            <tr>
             <td>
              阿拉伯联合酋长国 (+971)
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.254/每条短信
             </td>
            </tr>
            <tr>
             <td>
              英国（+ 44）
             </td>
             <td>
              –
             </td>
             <td>
              ¥0.22896/每条短信
             </td>
            </tr>
            <tr>
             <td>
              美国（+ 1）
             </td>
             <td>
              每月 100 条短信
             </td>
             <td>
              ¥0.065635/每条短信
             </td>
            </tr>
           </tbody>
          </table>
         </div>
        </div>
       </div>

       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <h3>
          Log Analytics 常见问题解答
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="home_monitor_log_analytics_question1" id="home_monitor_log_analytics_question1">
             如果我正在使用按节点计价的见解和分析定价模型，我可以继续在该定价模型上使用
                                            Log Analytics 吗？
            </a>
            <section>
             <p>
              可以。你可以继续在按节点计价的见解和分析定价模型上使用 Log Analytics。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="home_monitor_log_analytics_question2" id="home_monitor_log_analytics_question2">
             如果我正在使用按节点计价的见解和分析定价计划，我如何知道按
                                            GB 计价的新定价模型是否更适合我？
            </a>
            <section>
             <p>
              若要预估按 GB 计价的定价模型的影响，请访问
              <a href="https://docs.azure.cn/azure-monitor/platform/usage-estimated-costs">
               使用情况和预估成本
              </a>
              页面上的文档。请注意，在新的定价模型中，数据引入和保留的计费都不相同，具体取决于
                                                Log Analytics 工作区所在的区域。进行任何预估成本比较时请务必注意这一点。另外，如果购买了 OMS
                                                套件，请务必与客户代表交流，充分了解任何更改会造成的影响。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="home_monitor_log_analytics_question3" id="home_monitor_log_analytics_question3">
             如果我正在使用按节点计价的见解和分析定价模型，可以对新订阅使用按节点计价的定价计划吗？
            </a>
            <section>
             <p>
              可以。对于使用按节点计价的见解和分析定价模型的企业协议 (EA) 客户，其链接到现有 EA 的任何新订阅可继续使用按节点计价的定价模型。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="home_monitor_log_analytics_question4" id="home_monitor_log_analytics_question4">
             如何计算数据保留成本？
            </a>
            <section>
             <p>
              可以选择 30 天至 730 天的保留期。计算保留期成本时应除开 31 天的内附保留期，并按照根据每月每 GB
                                                价格计算得出的每日粒度计算。
             </p>
            </section>
           </div>
          </li>
         </ul>
         <h3>
          警报常见问题解答
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="home_monitor_alert_question1" id="home_monitor_alert_question1">
             以上定价适用于活动日志、服务运行状况和资源运行状况预警规则吗？
            </a>
            <section>
             <p>
              活动日志、服务运行状况和资源运行状况预警规则将继续免费提供。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="home_monitor_alert_question2" id="home_monitor_alert_question2">
             警报通知（通过 Azure
                                            操作组）费用是否包含在预警规则费用中？
            </a>
            <section>
             <p>
              警报通知根据使用的通知类型单独收费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="home_monitor_alert_question3" id="home_monitor_alert_question3">
             如果我是经典警报平台的现有客户，上面的预警规则定价是否对我适用？
            </a>
            <section>
             <p>
              上面列出的定价适用于新的警报平台。如果将现有预警规则从经典警报平台转到新的平台，则适用上面列出的定价。
             </p>
            </section>
           </div>
          </li>
         </ul>
         <h3>
          Application Insights 常见问题解答
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="如何控制或限制 Application Insights 的成本？" id="home_monitor_application_insights_cost_application_insights_billing">
             如何控制或限制 Application
                                            Insights 的成本？
            </a>
            <section>
             <p>
              可以在 Application Insights 从应用程序接收的数据量中设置每日上限，以便控制成本。达到每日上限后，当天（UTC
                                                时间）余下的时间内将忽略超额数据，并在次日开始时恢复正常。
             </p>
             <p>
              此外，可使用
              <a href="https://docs.azure.cn/azure-monitor/app/sampling">
               采样
              </a>
              来减少应用程序发送到
                                                Application Insights 的数据量。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="什么是节点？如何根据 Application Insights Enterprise 定价选项应用每节点数据限额？" id="home_monitor_application_insights_application_insights_nodes">
             什么是节点？如何根据
                                            Application Insights Enterprise 定价选项应用每节点数据限额？
            </a>
            <section>
             <p>
              节点可以是托管应用的物理计算机、虚拟机或平台即服务 (PaaS) 实例。例如，如果应用程序在3个 Azure
                                                应用服务实例和1台虚拟机上运行，则有4个托管应用程序的节点。每小时对发送遥测数据的不同节点进行。如果某节点在特定小时内未发送任何遥测数据，则不计入该节点。上述每月每节点定价假定节点在该月的每个小时都在发送遥测数据，因此，如果应用程序在一个月中有停止活动的时间段，则实际收费会更低。
             </p>
             <p>
              随着应用程序的扩展或缩减（例如在活动高峰期添加额外的 Web 服务器），Application Insights Enterprise
                                                费用也会相应地增加或减少。
             </p>
             <p>
              下述几种情况不纳入节点计数中（尽管始终对数据量计数）：
             </p>
             <ul>
              <li>
               调试期间运行应用程序的开发人员工作站不被计为节点。
              </li>
              <li>
               使用 JavaScript 浏览器客户端 SDK（或其他某些不报告“roleInstance”的
                                                    SDK）时，最终用户的计算机将不计为节点。
              </li>
              <li>
               使用 HockeyApp Bridge 应用时，HockeyApp 监视的移动设备均不计为节点。
              </li>
             </ul>
             <p>
              如果将同一节点用于正在监视的多个应用程序，会出现什么情况？没有任何问题。仅对 Azure
                                                订阅中发送遥测数据的唯一节点计数（计费帐户）。例如，如果同一物理服务器上运行 5 个单独的网站，且每个网站均配置有
                                                Application Insights Enterprise（按节点收费），则总共记为 1 个节点。
             </p>
             <p>
              此外，同一 Azure 订阅中还可存在使用 Application Insights Basic（按 GB
                                                收费）的应用程序，这不会影响使用 Application Insights Enterprise 的应用程序的节点计数。
             </p>
             <p>
              Application Insights Enterprise 中如何使用每日 200 MB 的数据？选择 Enterprise
                                                级定价选项时，将根据发送遥测的节点数为应用程序提供每日限额。如果有 5 个节点发送数据，则应用程序将获得每天 1GB
                                                的池化限额（根据设置的 Application Insights 资源定义）。无所谓某些节点是否比其他节点发送更多数据，因为给定的
                                                Azure 订阅的所有节点均共享已包含的数据。如果某天发送的数据多于每日数据池包含的数据，则按 GB
                                                对超额数据收费。每日限额中未使用的数据不会累计。
             </p>
             <p>
              每日池化数据限额的计算方式为所有节点在一天中发送遥测的小时数除以 24 再乘以 200 MB。因此，如果有 4 个节点一天 24 小时中有
                                                15 个小时在发送遥测，则当天包括的数据为 ((4 x 15) / 24) x 200 MB = 500 MB。
             </p>
             <p>
              如果在同一 Azure 订阅中为多个应用程序选择 Enterprise 定价选项，这些应用程序会共享每日数据限额。选择 Basic
                                                定价选项时，应用程序不共享每日数据限额。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="/support/contact/" id="monitor-contact-page">
          Azure
                            支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         使用 Azure 监控器，您可以收集详尽的性能和使用情况数据、活动和诊断日志，并以一致的方式定义 Azure 资源的警报和通知。我们保证，在不少于 99.9%
                            的时间内，通知会成功送达。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="/support/sla/monitor/" id="pricing_monitor_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>

      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="fJH501NPORMdtta1zJPga6VjM1ig_a_6YEr2T3-K8pcBkegj_rGB1z9DyVf_GIixZy-eItzQuuaLzQ4_0H2RFA3MGrnmU7qnukfHQ3ybQUg1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="/Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!--    <script src='../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js' type='text/javascript'></script>-->
  <!--    <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
