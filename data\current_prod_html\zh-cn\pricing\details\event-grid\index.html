<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="事件处理程序和事件处理, 发布订阅和发布订阅模型, 反应式编程和基于事件的编程" name="keywords"/>
  <meta content="了解 Azure 事件网格定价，它是一款完全托管的“发布-订阅”事件处理程序。它基于操作数进行计费，每月前 100,000 个操作免费。" name="description"/>
  <title>
   定价 - 事件网格 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/event-grid/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="event-grid" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/event-grid_banner.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           事件网格
           <span>
            Event Grid
           </span>
          </h2>
          <h4>
           实现大规模的可靠事件交付
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         事件网格是一种完全托管的事件路由服务，提供大规模的可靠消息传递。开发人员使用事件网格在新式无服务器计算体系结构中生成事件驱动型响应式应用，消除轮询及其相关的成本和延迟。一对多映射（其中，一个事件可触发多个操作）提供统一的事件消耗体验。按操作付费模式对无服务器解决方案给予补充和延伸，使你专注于创新而非基础结构。
        </p>
       </div>
       <div class="pricing-page-section">
        <h2>
         事件网格消耗定价
        </h2>
        <p>
         事件网格基于执行的操作按使用量计费。这些操作包括事件引入域或主题、高级匹配、交付尝试和管理调用。计划定价包括每月免费授予的 100,000 个操作数。
        </p>
        <table cellpadding="0" cellspacing="0" width="100%">
         <tbody>
          <tr>
           <th align="left">
            每百万个操作的价格
           </th>
           <th align="left">
            每月免费使用量
           </th>
          </tr>
          <tr>
           <td align="left">
            ￥ 0.611
           </td>
           <td align="left">
            100,000 个操作
           </td>
          </tr>
         </tbody>
        </table>
        <h2>
         事件网格定价示例 1
        </h2>
        <p>
         <a href="../../../home/<USER>/azure-functions">
          Azure Functions
         </a>
         通过事件网格连接到
         <a href="../../../home/<USER>/storage/blobs">
          Blob 存储
         </a>
         ，在每次添加新图像时处理图像。BLOB 存储容器中创建了 5 百万个图像，每一个都通过事件网格触发函数。
        </p>
        <ul>
         <li>
          一个月向事件网格发布 5 百万个事件。
         </li>
         <li>
          所有事件都发布到 1 个 https 终结点。
         </li>
        </ul>
        <table cellpadding="0" cellspacing="0" width="100%">
         <tbody>
          <tr>
           <th align="left">
            <strong>
             操作数量
            </strong>
           </th>
           <th align="left">
           </th>
          </tr>
          <tr>
           <td align="left">
            已发布事件
           </td>
           <td align="left">
            5 百万个操作
           </td>
          </tr>
          <tr>
           <td align="left">
            交付尝试
           </td>
           <td align="left">
            5 百万个操作
           </td>
          </tr>
          <tr>
           <td align="left">
            每月免费授予量
           </td>
           <td align="left">
            - 100,000 个操作
           </td>
          </tr>
          <tr>
           <td align="left">
            总操作数
            <br/>
            每百万个操作的价格
           </td>
           <td align="left">
            9.9 百万
            <br/>
            ￥ 0.611
           </td>
          </tr>
          <tr>
           <td align="left">
            <strong>
             每月总成本
            </strong>
           </td>
           <td align="left">
            ￥ 6.0489
           </td>
          </tr>
         </tbody>
        </table>
        <h2>
         事件网格定价示例 2
        </h2>
        <p>
         通过
         <a href="../../../home/<USER>/event-hubs.html">
          事件中心
         </a>
         捕获，将事件中心的传入日志发送到存储。事件网格将五百万个日志批处理事件推送到
         <a href="../../../home/<USER>/logic-apps.html">
          逻辑应用
         </a>
         ，供其监视。同时，还根据事件类型（在某些情况下根据事件来源），将所有事件推送到一个自定义的监视终结点。其中一百万个事件需要高级匹配。其中一个终结点出现中断，必须重试才能成功交付事件。
        </p>
        <ul>
         <li>
          一个月向事件网格发布 5 百万个事件。
         </li>
         <li>
          所有事件都发布到 2 个 https 终结点。
         </li>
         <li>
          其中 1 百万个事件需要高级匹配。
         </li>
         <li>
          其中 1 百万个事件需要 2 次交付尝试。
         </li>
        </ul>
        <table cellpadding="0" cellspacing="0" width="100%">
         <tbody>
          <tr>
           <th align="left">
            <strong>
             操作数量
            </strong>
           </th>
           <th align="left">
           </th>
          </tr>
          <tr>
           <td align="left">
            已发布事件
           </td>
           <td align="left">
            5 百万个操作
           </td>
          </tr>
          <tr>
           <td align="left">
            交付尝试
           </td>
           <td align="left">
            11 百万个操作（第二次交付尝试为 1 百万个）
           </td>
          </tr>
          <tr>
           <td align="left">
            高级匹配
           </td>
           <td align="left">
            1 百万个操作
           </td>
          </tr>
          <tr>
           <td align="left">
            每月免费授予量
           </td>
           <td align="left">
            - 100,000 个操作
           </td>
          </tr>
          <tr>
           <td align="left">
            总操作数
            <br/>
            每百万个操作的价格
           </td>
           <td align="left">
            16.9 百万
            <br/>
            ￥ 0.611
           </td>
          </tr>
          <tr>
           <td align="left">
            <strong>
             每月总成本
            </strong>
           </td>
           <td align="left">
            ￥ 10.3259
           </td>
          </tr>
         </tbody>
        </table>
       </div>
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <h3>
          综合
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="什么是操作以及如何对它们计费？" id="home_event-grid_faq_what-are-operations-and-how-are-they-billed">
             什么是操作以及如何对它们计费？
            </a>
            <section>
             <p>
              事件网格中的操作包括所有入口事件、高级匹配、交付尝试和管理调用。按照每百万个操作收费，每个月前 100,000 个操作免费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="每个帐户可以有多少个事件订阅？" id="home_event-grid_faq_how-many-Event-Subscriptions-can-I-have-for-each-account">
             每个帐户可以有多少个事件订阅？
            </a>
            <section>
             <p>
              根据事件网格限制文档，默认情况下每个主题最多可有 500 个事件订阅。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="每个帐户可以有多少个自定义主题？" id="home_event-grid_faq_how-many-custom-Topics-can-I-have-for-each-account">
             每个帐户可以有多少个自定义主题？
            </a>
            <section>
             <p>
              自定义主题的默认限制是每个 Azure 订阅 100 个主题
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="每个帐户可以有多少个事件网格订阅？" id="home_event-grid_faq_how-many-Event-Grid-subscriptions-can-I-have-for-each-account">
             每个帐户可以有多少个事件网格订阅？
            </a>
            <section>
             <p>
              在预览期间，最多可以有 1,000 个事件网格订阅。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="事件网格中支持哪些终结点？" id="home_event-grid_faq_which-endpoints-are-supported-in-Event-Grid">
             事件网格中支持哪些终结点？
            </a>
            <section>
             <p>
              目前，我们支持 https 终结点。之后会支持其他终结点。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="交付尝试次数是否有限制？" id="home_event-grid_faq_Is-there-a-limit-on-delivery-attempts">
             交付尝试次数是否有限制？
            </a>
            <section>
             <p>
              事件网格将对所有交付执行指数退避。如果 WebHook 不返回 2xx，重试将立即开始。当退避间隔达到一小时的时候，将每小时进行重试。24 小时后，服务将停止尝试交付事件。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="最长的保留期是多久？" id="home_event-grid_faq_What-is-the-maximum-retention-period">
             最长的保留期是多久？
            </a>
            <section>
             <p>
              事件网格将缓冲数据最多 24 小时。在此时间结束时，事件将被删除。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="如何计算事件网格事件的大小，如何根据事件大小收费？" id="home_event-grid_faq_How-is-Event-Grid-event-size-calculated-and-charged">
             如何计算事件网格事件的大小，如何根据事件大小收费？
            </a>
            <section>
             <p>
              交付数据区块每 64KB 按 1 个请求计费。例如，256KB 的一个事件将按 4 个事件计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="使用事件域是否会单独收费？" id="home_event-grid_faq_Is-there-a-separate-charge-for-using-Event-Domains">
             使用事件域是否会单独收费？
            </a>
            <section>
             <p>
              不会，域和主题采用相同的计费方式。事件引入 Azure 事件网格系统是一个操作，与入口点无关。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a aria-label="event-grid-contact-page" href="https://support.azure.cn/support/contact" id="event-grid-contact-page" target="_blank">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         若要了解有关我们的服务器级别协议的详细信息，请访问
         <a aria-label="pricing_event-grid_sla" href="../../../support/sla/event-grid/index.html" id="pricing_event-grid_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <!-- END: TAB-CONTROL -->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="ssXqYw_EwSjxtMqN_SnkhRrzK-AL8jiTpXR4TfIByijh_RvVS1OWaOAwew0q8FKzJYL5kvLBm1_T8UmD2WX1BngCjKa2ZrCVlWSop1wbf4Q1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
