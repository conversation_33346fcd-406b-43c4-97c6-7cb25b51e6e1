<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Azure Database for MariaDB, 价格详情, 定价, 计费" name="keywords"/>
  <meta content="Azure Database for MariaDB 服务定价层和详细信息" name="description"/>
  <title>
   Azure Database for MariaDB 定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/mariadb/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <style type="text/css">
        .cognitive-banner{
                    margin: 30px 0;
                }
                .cognitive-figure{
                    padding-bottom: 30px;
                    margin-bottom: 30px;
                    border-bottom: 1px #ccc solid;
                    overflow: hidden;
                }
                .cognitive-figure .col-md-3{
                    padding-left: 0;
                    padding-right: 0;
                }
                .cognitive-figure .col-md-9{
                    padding-left: 2%;
                    padding-right: 0;
                }
                .pricing-page-section .cognitive-figure .col-md-9 h2, .container .pure-content .pricing-page-section .cognitive-icon-group .cognitive-text h2{
                    margin-top: 4px !important;
                }
                .cognitive-icon-group{
                    margin-top: 20px;
                    margin-bottom: 0;
                    list-style:none;
                    padding-left:0 !important;
                }
                .cognitive-icon-group li{
                    width:100%;
                    overflow:hidden;
                    margin-bottom: 30px !important;
                }
                .cognitive-icon-group li div{
                    float: left;
                }
                .cognitive-icon{
                    width: 10%;
                    min-width: 100px;
                    margin-right:-120px;
                }
                .cognitive-text{                   
                    margin-left:120px;
                }
                .cognitive-icon-group li:last-child{
                    margin-bottom: 0 !important;
                }
                @media only screen and (max-width: 769px){
                    .cognitive-figure{
                        padding-bottom: 20px;
                    }
                    .cognitive-figure .col-md-9{
                        padding-left: 0;
                    }
                    .pricing-page-section .cognitive-figure .col-md-9 h2{
                        margin-top: 26px !important;
                    }
                }
            .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;              
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;              
            }
            .pure-content .technical-azure-selector .tags-date a,.pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
       </style>
       <tags ms.date="09/30/2015" ms.service="mariadb" wacn.date="11/27/2015">
       </tags>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/mariadb-banner.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/<EMAIL>"/>
          <h2>
           Azure
           <small>
            Database for MariaDB
           </small>
          </h2>
          <h4>
           可供企业使用的完全托管的社区 MariaDB
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <div class="cognitive-figure">
         <div class="col-md-4">
          <h3>
           内置高可用性
          </h3>
          <p>
           旨在提供高可用性，无需额外的配置、复制或费用。
          </p>
         </div>
         <div class="col-md-4">
          <h3>
           简单缩放
          </h3>
          <p>
           在几秒钟内动态更改计算，并独立缩放存储。
          </p>
         </div>
         <div class="col-md-4">
          <h3>
           灵活定价
          </h3>
          <p>
           为工作负荷选择资源，没有任何隐藏费用。
          </p>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         定价详细信息
        </h2>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              MariaDB
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_mariadb">
                MariaDB
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="MariaDB">
              MariaDB
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <li>
               <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                中国东部
               </a>
              </li>
              <li>
               <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                中国北部
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <option data-href="#east-china" value="east-china">
              中国东部
             </option>
             <option data-href="#north-china" value="north-china">
              中国北部
             </option>
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: Level 1 tab content panel 1 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 1 -->
          <div class="category-container-container">
           <div class="category-container-box">
            <div class="category-container">
             <span class="category-title hidden-lg hidden-md">
              类别：
             </span>
             <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
              <li class="active">
               <a data-href="#tabContent1-0" href="javascript:void(0)" id="mariadb-all">
                全部
               </a>
              </li>
              <li>
               <a data-href="#tabContent1-1" href="javascript:void(0)" id="mariadb-basic">
                基本
               </a>
              </li>
              <li>
               <a data-href="#tabContent1-2" href="javascript:void(0)" id="mariadb-general-purpose">
                常规用途
               </a>
              </li>
              <li>
               <a data-href="#tabContent1-3" href="javascript:void(0)" id="mariadb-memory-prioritization">
                内存优化
               </a>
              </li>
             </ul>
             <select class="dropdown-select category-tabs hidden-lg hidden-md">
              <option data-href="#tabContent1-0" id="mariadb-all" value="all">
               全部
              </option>
              <option data-href="#tabContent1-1" id="mariadb-basic" value="basic">
               基本
              </option>
              <option data-href="#tabContent1-2" id="mariadb-general-purpose" value="general-purpose">
               常规用途
              </option>
              <option data-href="#tabContent1-3" id="mariadb-memory-prioritization" value="memory-prioritization">
               内存优化
              </option>
             </select>
            </div>
           </div>
          </div>
          <!-- END: Tab level 2 navigator 1 -->
          <!-- BEGIN: Tab level 2 content -->
          <div class="tab-content">
           <!-- BEGIN: Tab level 2 content 1-1 -->
           <div class="tab-panel" id="tabContent1-1">
            <h3>
             基本:需要轻型计算和 I/O 性能的工作负荷。
            </h3>
            <h3>
             计算
            </h3>
            <p>
             在虚拟核心 (vCore) 中预配计算。vCore 表示逻辑 CPU。
            </p>
            <div class="scroll-table" style="display: block;">
             <h4>
              Gen5
             </h4>
             <p>
              生成 5 逻辑 CPU 基于 Intel E5-2673 v4 (Broadwell) 2.3 GHz 处理器。
             </p>
             <div class="tags-date">
              <div class="ms-date">
               *以下价格均为含税价格。
              </div>
              <br/>
              <div class="ms-date">
               *每月价格估算基于每个月 744 小时的使用量。
              </div>
             </div>
             <table cellpadding="0" cellspacing="0" id="mariadb-basic-compute-gen5" width="100%">
              <tbody>
               <tr>
                <th align="left">
                 <strong>
                  vCore
                 </strong>
                </th>
                <th align="left">
                 <strong>
                  价格
                 </strong>
                </th>
               </tr>
               <tr>
                <td>
                 1
                </td>
                <td>
                 ￥0.26988/小时
                 <br/>
                 ( 约￥200.79072/月 )
                </td>
               </tr>
               <tr>
                <td>
                 2
                </td>
                <td>
                 ￥0.53975/小时
                 <br/>
                 ( 约￥401.574/月 )
                </td>
               </tr>
              </tbody>
             </table>
            </div>
            <h4>
             存储
            </h4>
            <p>
             需要为对服务器预配的存储空间付费。最多可预配 1 TB 的存储空间。
            </p>
            <table cellpadding="0" cellspacing="0" width="100%">
             <tr>
              <th align="left">
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               GB/月
              </td>
              <td>
               ￥ 0.794
              </td>
             </tr>
            </table>
            <h4>
             备份
            </h4>
            <p>
             备份存储是与服务器的自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
            </p>
            <table cellpadding="0" cellspacing="0" width="100%">
             <tr>
              <th align="left">
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               LRS GB/月
              </td>
              <td>
               ￥ 0.794
              </td>
             </tr>
            </table>
           </div>
           <div class="tab-panel" id="tabContent1-2">
            <h3>
             常规用途:大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放 I/O 吞吐量。
            </h3>
            <h3>
             计算
            </h3>
            <p>
             在虚拟核心 (vCore) 中预配计算。vCore 表示逻辑 CPU。
            </p>
            <div class="scroll-table" style="display: block;">
             <h3>
              Gen5
             </h3>
             <p>
              生成 5 逻辑 CPU 基于 Intel E5-2673 v4 (Broadwell) 2.3 GHz 处理器。
             </p>
             <div class="tags-date">
              <div class="ms-date">
               *以下价格均为含税价格。
              </div>
              <br/>
              <div class="ms-date">
               *每月价格估算基于每个月 31 天的使用量。
              </div>
             </div>
             <table cellpadding="0" cellspacing="0" id="mariadb-general-purpose-compute-gen5" width="100%">
              <tbody>
               <tr>
                <th align="left">
                 <strong>
                  vCore
                 </strong>
                </th>
                <th align="left">
                 <strong>
                  价格
                 </strong>
                </th>
               </tr>
               <tr>
                <td>
                 2
                </td>
                <td>
                 ￥0.6675/小时
                 <br/>
                 （约￥496.62/月）
                </td>
               </tr>
               <tr>
                <td>
                 4
                </td>
                <td>
                 ￥1.335/小时
                 <br/>
                 （约￥993.24/月）
                </td>
               </tr>
               <tr>
                <td>
                 8
                </td>
                <td>
                 ￥2.67/小时
                 <br/>
                 （约￥1,986.48/月）
                </td>
               </tr>
               <tr>
                <td>
                 16
                </td>
                <td>
                 ￥5.3402/小时
                 <br/>
                 （约￥3,973.1088/月）
                </td>
               </tr>
               <tr>
                <td>
                 32
                </td>
                <td>
                 ￥10.6803/小时
                 <br/>
                 （约￥7,946.1432/月）
                </td>
               </tr>
               <tr>
                <td>
                 64
                </td>
                <td>
                 ￥21.3606/小时
                 <br/>
                 （约￥15,892.2864/月）
                </td>
               </tr>
              </tbody>
             </table>
            </div>
            <!-- END: Table1-Content-->
            <h4>
             存储
            </h4>
            <p>
             需要为对服务器预配的存储空间付费。最多可预配 1 TB 的存储空间。
            </p>
            <table cellpadding="0" cellspacing="0" width="100%">
             <tr>
              <th align="left">
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               GB/月
              </td>
              <td>
               ￥ 0.803
              </td>
             </tr>
            </table>
            <h4>
             备份
            </h4>
            <p>
             备份存储是与服务器的自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
            </p>
            <table cellpadding="0" cellspacing="0" width="100%">
             <tr>
              <th align="left">
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               LRS GB/月
              </td>
              <td>
               ￥ 0.794
              </td>
             </tr>
             <tr>
              <td>
               GRS GB/月
              </td>
              <td>
               ￥ 1.588
              </td>
             </tr>
            </table>
           </div>
           <div class="tab-panel" id="tabContent1-3">
            <h3>
             内存优化:高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。
            </h3>
            <h3>
             计算
            </h3>
            <p>
             在虚拟核心 (vCore) 中预配计算。vCore 表示逻辑 CPU。
            </p>
            <div class="scroll-table" style="display: block;">
             <h4>
              Gen5
             </h4>
             <p>
              生成 5 逻辑 CPU 基于 Intel E5-2673 v4 (Broadwell) 2.3 GHz 处理器。
             </p>
             <div class="tags-date">
              <div class="ms-date">
               *以下价格均为含税价格。
              </div>
              <br/>
              <div class="ms-date">
               *每月价格估算基于每个月 31 天的使用量。
              </div>
             </div>
             <table cellpadding="0" cellspacing="0" id="mariadb-memory-optimized-compute-gen5" width="100%">
              <tbody>
               <tr>
                <th align="left">
                 <strong>
                  vCore
                 </strong>
                </th>
                <th align="left">
                 <strong>
                  价格
                 </strong>
                </th>
               </tr>
               <tr>
                <td>
                 2
                </td>
                <td>
                 ￥1.8748/小时
                 <br/>
                 ( 约￥1,394.8512/月 )
                </td>
               </tr>
               <tr>
                <td>
                 4
                </td>
                <td>
                 ￥3.7497/小时
                 <br/>
                 ( 约￥2,789.7768/月 )
                </td>
               </tr>
               <tr>
                <td>
                 8
                </td>
                <td>
                 ￥7.4994/小时
                 <br/>
                 ( 约￥5,579.5536/月 )
                </td>
               </tr>
               <tr>
                <td>
                 16
                </td>
                <td>
                 ￥14.9987/小时
                 <br/>
                 ( 约￥11,159.0328/月 )
                </td>
               </tr>
               <tr>
                <td>
                 32
                </td>
                <td>
                 ￥29.9974/小时
                 <br/>
                 ( 约￥22,318.0656/月 )
                </td>
               </tr>
              </tbody>
             </table>
            </div>
            <h4>
             存储
            </h4>
            <p>
             需要为对服务器预配的存储空间付费。最多可预配 1 TB 的存储空间。
            </p>
            <table cellpadding="0" cellspacing="0" width="100%">
             <tr>
              <th align="left">
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               GB/月
              </td>
              <td>
               ￥ 0.803
              </td>
             </tr>
            </table>
            <h4>
             备份
            </h4>
            <p>
             备份存储是与服务器的自动备份关联的存储。增长备份保留期会使服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。
            </p>
            <table cellpadding="0" cellspacing="0" width="100%">
             <tr>
              <th align="left">
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               LRS GB/月
              </td>
              <td>
               ￥ 0.794
              </td>
             </tr>
             <tr>
              <td>
               GRS GB/月
              </td>
              <td>
               ￥ 1.588
              </td>
             </tr>
            </table>
           </div>
          </div>
         </div>
         <!-- END: TAB-CONTAINER-1 -->
        </div>
       </div>
       <!-- END: TAB-CONTAINER-1 -->
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <h3>
          综合
         </h3>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_mariadb_different_from_basic_general_purpose">
             基本层、常规用途层和内存优化层之间的区别是什么？
            </a>
            <section>
             <p>
              基本层专为需要轻型计算和 I/O 性能的工作负荷而设计。相关示例包括用于开发或测试的服务器，或者不常使用的小型应用程序。常规用途层适用于大多数业务工作负荷，此类工作负荷需要均衡计算和内存以及可缩放 I/O 吞吐量。相关示例包括用于托管 Web 和移动应用的服务器，以及其他企业应用程序。内存优化层适用于高性能数据库工作负荷，此类工作负荷需要内存中性能来实现更快的事务处理速度和更高的并发。相关示例包括用于处理实时数据的服务器，以及高性能事务性应用或分析应用。请参阅
              <a href="https://docs.azure.cn/mariadb/concepts-pricing-tiers">
               文档
              </a>
              ，了解详细信息。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_mariadb_calculator_billing">
             我的账单是如何计算的？
            </a>
            <section>
             <p>
              对于所有层，服务根据定价层、在 vCore 中预配的计算以及为服务器和备份预配的存储空间（GB/月）按可预测的每小时费率计费。vCore 小时数、服务器的存储空间（GB/月）和备份的存储空间（GB/月）作为单独的行项出现在订单上。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_mariadb_how_to_calculator_mariadb_when_less_thant_one_hour">
             如果一个 MariaDB 服务器处于活动状态的时间少于 1 小时或使用更高定价层的时间少于 1 小时，会怎样计费？
            </a>
            <section>
             <p>
              需要为 MariaDB 服务器存在的每个小时或不足一小时的部分付费，而不考虑服务器是否在整个小时内处于活动状态。如果已缩放数据库，在这个小时内，按照使用的最高定价层、预配的 vCore 和存储空间计费。
             </p>
             <p>
              例如：
             </p>
             <ul>
              <li>
               如果创建一个 MariaDB 服务器并在 5 分钟后删除，则按照预配的计算和存储空间收取一整个小时的费用。
              </li>
              <li>
               如果在常规用途层创建一个具有 8 个 vCore 的 MariaDB 服务器，并在常规用途层中立即将其升级为 16 个 vCore，则第一个小时按 16 个 vCore 收费。
              </li>
             </ul>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_mariadb_bandwidth_billing">
             是否需要承担任何网络数据传输费用？
            </a>
            <section>
             <p>
              需要网络流出量按标准费率收费。
              <a href="../bandwidth.html">
               带宽
              </a>
              定价页包含更多详细信息。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="home_mariadb_backup_billing">
             如何计算备份费用？
            </a>
            <section>
             <p>
              备份存储是与服务器的自动备份关联的存储。增长备份保留期会使 MariaDB 服务器使用的备份存储空间增大。如果备份存储空间未超过 100% 的总预配服务器存储空间，则无需额外付费。超出此部分的其他备份存储空间按 GB/月收费。例如，如果数据库存储空间大小为 100 GB，则可以获得 100 GB 的备份，无需额外付费。但是，如果备份为 110 GB，则需要为 10 GB 付费。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="database-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/mariadb/index.html" id="pricing_mariadb_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="HvxZ_RNVWfn9WZzj_abhTG-vp_rrZ1FdkK8Ra1iVIQP3bB1oH-0d-wItW0L5bCXtBXyFiwIgIte0LM372JNXHS9FXLjRZ5IEh5w02BbaFTI1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
  </script>
  <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!-- <script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script> -->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
