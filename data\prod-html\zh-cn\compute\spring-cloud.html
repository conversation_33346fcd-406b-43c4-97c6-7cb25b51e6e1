<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <meta content="无需 VM 上的公共 IP，Azure Bastion 服务就提供了到 Azure 虚拟机中的 Azure VM 的安全、无缝 RDP 和 SSH 连接。" name="description"/>
  <title>
   Azure Spring Apps定价
  </title>
  <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
		window.currentLocale = "zh-CN";
		window.headerTimestamp = "2019/1/23 8:25:24";
		window.footerTimestamp = "2019/1/8 8:07:06";
		window.locFileTimestamp = "2018/11/29 7:49:17";

		window.AcnHeaderFooter = {
			IsEnableQrCode: true,
			CurrentLang: window.currentLocale.toLowerCase(),
		};
  </script>
  <style>
   @media (min-width: 0) {
			.acn-header-placeholder {
				height: 48px;
				width: 100%;
			}
		}

		@media (min-width: 980px) {
			.acn-header-placeholder {
				height: 89px;
				width: 100%;
			}
		}

		.acn-header-placeholder {
			background-color: #1A1A1A;
		}

		.acn-header-service {
			position: absolute;
			top: 0;
			width: 100%;
		}
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="11/30/2020" ms.service="spring-cloud" wacn.date="11/30/2020">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav {
								padding-left: 0 !important;
								margin-top: 5px;
								margin-bottom: 0;
								overflow: hidden;
							}

							.pricing-detail-tab .tab-nav li {
								list-style: none;
								float: left;
							}

							.pricing-detail-tab .tab-nav li.active a {
								border-bottom: 4px solid #00a3d9;
							}

							.pricing-detail-tab .tab-nav li.active a:hover {
								border-bottom: 4px solid #00a3d9;
							}

							.pricing-detail-tab .tab-content .tab-panel {
								display: none;
							}

							.pricing-detail-tab .tab-content .tab-panel.show-md {
								display: block;
							}

							.pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
								padding-left: 5px;
								padding-right: 5px;
								color: #00a3d9;
								background-color: #FFF;
							}

							.pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
								color: #FFF;
								background-color: #00a3d9;
							}

							.pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
								color: #FFF;
								background-color: #00a3d9;
							}

							.pure-content .technical-azure-selector p a,
							.pure-content .technical-azure-selector table a {
								background: 0 0;
								padding: 0;
								margin: 0 6px;
								height: 21px;
								line-height: 22px;
								font-size: 14px;
								color: #00a3d9;
								float: none;
								display: inline;
							}

							.svg {
								width: 50px;
								float: left;
								margin-right: 10px;
							}
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/resources/self-serve/spring-logo.jpg"/>
          <h2>
           Azure Spring Apps定价
          </h2>
          <h4>
           联合 VMware 共同构建和运营的完全托管的 Azure Spring Apps服务
          </h4>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <p>
         借助 Azure Spring Apps，可以在云端快速、安全、轻松地部署和操作 Spring Boot 应用程序。
        </p>
        <p>
         对于基本层中的每个应用实例，Azure Spring Apps 对一个“基本 vCPU 和内存组持续时间”收费，其中包括 4 GB 内存和 2 个 vCPU。当超过“基本 vCPU 和内存组持续时间”中所包含容量的一个或两个时，将根据总“基本超额内存持续时间”和“基本超额 vCPU 持续时间”对实际额外使用量进行计费。每月前 50 vCPU 小时和 100 内存 GB 小时免费。每月前 50 个 vCPU 小时和 100 内存 GB 小时免费。
        </p>
        <p>
         对于标准层中的每个应用实例，Azure Spring Apps 对一个“标准 vCPU 和内存组持续时间”收费，其中包括 12 GB 内存和 6 个 vCPU。当超过“标准 vCPU 和内存组持续时间”中所包含容量的一个或两个时，将根据总“标准超额内存持续时间”和“标准超额 vCPU 持续时间”对实际额外使用量进行计费。每月前 50 vCPU 小时和 100 内存 GB 小时免费。每月前 50 个 vCPU 小时和 100 内存 GB 小时免费。
        </p>
        <div class="pricing-page-section">
         <h2>
          定价详细信息
         </h2>
        </div>
        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
         <div class="tab-container-container">
          <div class="tab-container-box">
           <div class="tab-container">
            <div class="dropdown-container software-kind-container" style="display:none;">
             <label>
              OS/软件:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               spring-cloud
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
               <li class="active">
                <a data-href="#tabContent1" href="javascript:void(0)" id="home_spring-cloud">
                 spring-cloud
                </a>
               </li>
              </ol>
             </div>
             <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
              <option data-href="#tabContent1" selected="selected" value="spring-cloud">
               spring-cloud
              </option>
             </select>
            </div>
            <div class="dropdown-container region-container">
             <label>
              地区:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               中国北部 3
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
               <li>
                <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                 中国北部 3
                </a>
               </li>
               <li class="active">
                <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                 中国东部 2
                </a>
               </li>
               <li>
                <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                 中国北部 2
                </a>
               </li>
               <li>
                <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                 中国东部
                </a>
               </li>
               <li>
                <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                 中国北部
                </a>
               </li>
              </ol>
             </div>
             <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
              <option data-href="#north-china3" selected="selected" value="north-china3">
               中国北部 3
              </option>
              <option data-href="#east-china2" selected="selected" value="east-china2">
               中国东部 2
              </option>
              <option data-href="#north-china2" value="north-china2">
               中国北部 2
              </option>
              <option data-href="#east-china" value="east-china">
               中国东部
              </option>
              <option data-href="#north-china" value="north-china">
               中国北部
              </option>
             </select>
            </div>
            <div class="clearfix">
            </div>
           </div>
          </div>
         </div>
         <!-- BEGIN: TAB-CONTAINER-1 -->
         <div class="tab-content">
          <!-- BEGIN: TAB-CONTENT-1 -->
          <div class="tab-panel" id="tabContent1">
           <!-- BEGIN: Tab level 2 navigator 2 -->
           <!-- BEGIN: Tab level 2 content 3 -->
           <ul class="tab-nav" style="display:none">
            <li class="active">
             <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)" id="gpv1">
              常规用途 v1
             </a>
            </li>
           </ul>
           <div class="tab-content">
            <div class="scroll-table" style="display: block;">
             <h3>
              标准消耗计划
             </h3>
             <div>
              <p>
               对于标准消耗计划中的每个应用实例，Azure Spring Apps 会对以 vCPU 和 gibibyte (GiB) 度量的请求和资源分配收费。应用程序根据请求和事件按需缩放。应用实例在运行时按活动使用量计费。当没有要处理的请求或事件时，可以将应用程序配置为缩放到零实例。当应用程序缩放为零时，不收取使用费。
              </p>
              <p>
               可以选择在标准消耗计划中配置应用程序，使其具有始终运行于空闲模式的最小应用实例数。当一个应用程序缩减到其最小数量的应用实例时，如果一个应用实例处于非活动状态，则会按照降低的空闲费率收取使用费。当一个应用实例启动、处理请求或其 vCPU 或带宽使用量超过活动计费阈值
               <sup>
                1
               </sup>
               时，该应用实例进入活动模式并按照活跃费率收取费用。每月前 50 个 vCPU 小时、100 个内存 GB 小时和 200 万次请求是免费的，并在 Azure 容器应用环境中共享。
              </p>
              <div class="tags-date">
               <div class="ms-date">
                <sup>
                 1
                </sup>
                当 vCPU 使用率高于 0.01 个内核或接收到的数据高于每秒 1000 字节时，应用实例处于活动状态。
               </div>
              </div>
             </div>
             <h3>
              标准消耗计划定价(预览版)
             </h3>
             <table cellpadding="0" cellspacing="0" id="springcloud-standard-north3" width="100%">
              <tbody>
               <tr>
                <th align="left" style="width: 263px;">
                 <strong>
                 </strong>
                </th>
                <th align="left">
                 <strong>
                  标准消耗
                 </strong>
                </th>
               </tr>
               <tr>
                <td>
                 vCPU 活动使用率/秒
                </td>
                <td>
                 ￥1.01109
                </td>
               </tr>
               <tr>
                <td>
                 vCPU 空闲使用率/秒
                </td>
                <td>
                 ￥0.126384
                </td>
               </tr>
               <tr>
                <td>
                 内存活动使用量/秒
                </td>
                <td>
                 ￥0.126384
                </td>
               </tr>
               <tr>
                <td>
                 内存空闲使用量/秒
                </td>
                <td>
                 ￥0.126384
                </td>
               </tr>
               <tr>
                <td>
                 请求数/百万
                </td>
                <td>
                 ￥2.9256
                </td>
               </tr>
               <tr>
                <td>
                 <sup>
                  ^
                 </sup>
                 每月免费授权
                </td>
                <td>
                 50 vCPU 小时
                 <br/>
                 100 内存 GB 小时
                 <br/>
                 200 万个请求
                </td>
               </tr>
              </tbody>
             </table>
             <div class="tags-date">
              <div class="ms-date">
               <sup>
                ^
               </sup>
               每月免费授予与 Azure 容器应用共享。
              </div>
             </div>
            </div>
            <div class="scroll-table" style="display: block;">
             <h3>
              Eureka 和 Spring Cloud Config 服务器定价(预览版)
             </h3>
             <table cellpadding="0" cellspacing="0" id="springcloud-Eureka-north3" width="100%">
              <tbody>
               <tr>
                <th align="left" style="width: 263px;">
                 <strong>
                  计量
                 </strong>
                </th>
                <th align="left">
                 <strong>
                  价格
                 </strong>
                </th>
               </tr>
               <tr>
                <td>
                 Eureka
                </td>
                <td>
                 ￥0.954 每小时
                </td>
               </tr>
               <tr>
                <td>
                 Spring Cloud Config 服务器
                </td>
                <td>
                 ￥0.954 每小时
                </td>
               </tr>
              </tbody>
             </table>
            </div>
            <div class="tab-panel" id="tabContent2">
             <div class="scroll-table" style="display: block;">
              <h3>
               Azure Spring Apps定价
              </h3>
              <table cellpadding="0" cellspacing="0" id="spring-cloud-premium-region2" width="100%">
               <tbody>
                <tr>
                 <th align="left" style="width: 263px;">
                  <strong>
                  </strong>
                 </th>
                 <th align="left">
                  <strong>
                   基本 - 针对检查和单独的开发/测试
                  </strong>
                 </th>
                 <th align="left">
                  <strong>
                   标准 - 适用于常规用途生产工作负载
                  </strong>
                 </th>
                </tr>
                <tr>
                 <td>
                  基本价格
                 </td>
                 <td>
                  ￥1.968/小时
                 </td>
                 <td>
                  ￥5.16/小时
                 </td>
                </tr>
                <tr>
                 <td>
                  包含 vCPU 和内存
                 </td>
                 <td>
                  2 vCPU，4 GB
                 </td>
                 <td>
                  6 vCPU，12 GB
                 </td>
                </tr>
                <tr>
                 <td>
                  超额 vCPU 价格
                 </td>
                 <td>
                  ￥0.521/vCPU/小时
                 </td>
                 <td>
                  ￥0.55/vCPU/小时
                 </td>
                </tr>
                <tr>
                 <td>
                  超额内存价格
                 </td>
                 <td>
                  ￥0.0587/GB/小时
                 </td>
                 <td>
                  ￥0.0623/GB/小时
                 </td>
                </tr>
                <tr>
                 <td>
                  *每月免费授权
                 </td>
                 <td>
                  50 vCPU 小时
                  <br/>
                  100内存GB小时
                 </td>
                 <td>
                  50 vCPU 小时
                  <br/>
                  100内存GB小时
                 </td>
                </tr>
                <tr>
                 <td>
                  最大应用实例大小
                 </td>
                 <td>
                  1 vCPU，2 GB
                 </td>
                 <td>
                  4 vCPU，8 GB
                 </td>
                </tr>
                <tr>
                 <td>
                  最大应用实例数
                 </td>
                 <td>
                  25
                 </td>
                 <td>
                  500
                 </td>
                </tr>
                <tr>
                 <td>
                  SLA
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  高可用性 Azure Spring Apps运行时
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  自定义域/SSL
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  VNET 集成
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  蓝/绿部署
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
               </tbody>
              </table>
              <div class="tags-date">
               <div class="ms-date">
                <sup>
                 *
                </sup>
                每月免费授权可在基本和标准层之间共享。
               </div>
              </div>
             </div>
             <div class="scroll-table" style="display: block;">
              <h3>
               完整详细信息
              </h3>
              <table cellpadding="0" cellspacing="0" width="100%">
               <tbody>
                <tr>
                 <th align="left" style="width: 263px;">
                  <strong>
                  </strong>
                 </th>
                 <th align="left">
                  <strong>
                   基本 - 针对检查和单独的开发/测试
                  </strong>
                 </th>
                 <th align="left">
                  <strong>
                   标准 - 适用于常规用途生产工作负载
                  </strong>
                 </th>
                </tr>
                <tr>
                 <td>
                  <strong>
                   限制
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  最大应用实例大小
                 </td>
                 <td>
                  1 vCPU，2 GB
                 </td>
                 <td>
                  4 vCPU，8 GB
                 </td>
                </tr>
                <tr>
                 <td>
                  最大应用实例数
                 </td>
                 <td>
                  25
                 </td>
                 <td>
                  500
                 </td>
                </tr>
                <tr>
                 <td>
                  高可用性 Azure Spring Apps运行时
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  SLA
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  持久存储
                 </td>
                 <td>
                  每个应用 1GB，最多 10 个应用
                 </td>
                 <td>
                  每个应用 50GB，最多 10 个应用
                 </td>
                </tr>
                <tr>
                 <td>
                  <strong>
                   应用管理
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  从源进行部署
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  从 *Jar 进行部署
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  手动横向或纵向扩缩
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  启动/停止/重启/删除应用程序
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  滚动更新
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  <strong>
                   Azure Spring Apps运行时
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  Git 中的外部化配置
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  服务注册和发现
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  蓝/绿部署
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  <strong>
                   监控和诊断
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  日志流
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  Azure Spring Apps 诊断
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  Azure Monitor 中的指标和日志
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  分布式跟踪
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  <strong>
                   联网
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  VNET 集成
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  SSL 终止
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  自定义域
                 </td>
                 <td>
                  -
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  <strong>
                   安全性
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  Azure 资源的托管标识
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  静态加密
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  <strong>
                   部署工具
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  Maven 插件
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  IntelliJ 插件
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  CI/CD 集成
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  与 Azure 服务进行服务绑定
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  <strong>
                   编程语言和框架
                  </strong>
                 </td>
                 <td>
                 </td>
                 <td>
                 </td>
                </tr>
                <tr>
                 <td>
                  .NET Core/Steeltoe
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
                <tr>
                 <td>
                  Java/Spring
                 </td>
                 <td>
                  √
                 </td>
                 <td>
                  √
                 </td>
                </tr>
               </tbody>
              </table>
              <div class="tags-date">
               <div class="ms-date">
                <sup>
                 *
                </sup>
                Spring Boot uber JAR
               </div>
              </div>
             </div>
            </div>
            <!-- END: Table1-Content-->
           </div>
          </div>
          <!-- END: TAB-CONTENT-3 -->
         </div>
         <!-- END: TAB-CONTAINER-1 -->
        </div>
        <!-- END: TAB-CONTROL -->
        <!-- 修改常见问题 -->
        <div class="pricing-page-section">
         <div class="more-detail">
          <h2>
           常见问题
          </h2>
          <em>
           全部展开
          </em>
          <ul>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question14">
              服务是否按整小时增量收费？
             </a>
             <section>
              <p>
               Azure Spring Apps 按每秒计费，每小时计量一次，不足一小时的按产生用量的秒数计算。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question15">
              在我的帐户中，会在什么级别跟踪“vCPU 和内存组持续时间”及超额用量？
             </a>
             <section>
              <p>
               在服务实例级别跟踪 Azure Spring Apps 资源和超额用量。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question15">
              若要开始使用 Azure Spring Apps，客户需要为所选层支付至少一个“vCPU
													和内存组持续时间”的费用。每层的“vCPU 和内存组持续时间”包含了哪些资源？
             </a>
             <section>
              <p>
               “基本 vCPU 和内存组持续时间”包括 4 GB 内存和 2 个 vCPU。“标准 vCPU 和内存组持续时间”包括 12 GB
														内存和 6 个 vCPU。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question15">
              在我的订阅中，我想使用 Azure Spring Apps 托管和运行多个 Spring
													Boot 应用程序。我是否基于所选层仅为一个“vCPU 和内存组持续时间”付费？
             </a>
             <section>
              <p>
               是的，你需要根据定价层（基本或标准）为每个服务实例支付一个“vCPU 和内存组持续时间”。如果使用了额外的内存和 vCPU，将根据
														Azure Spring Apps 的定价层，按你订阅中“超额内存持续时间”和“超额 vCPU 持续时间”的总数进行计费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question15">
              我是否需要为处于停止状态的应用付费？
             </a>
             <section>
              <p>
               是的，“vCPU 和内存组持续时间”列出的费率适用于处于停止状态的应用。如需完全停止计费，请删除或
               <a aria-label="none" data-linktype="external" href="https://docs.azure.cn/zh-cn/spring-apps/how-to-start-stop-service?tabs=azure-portal">
                停止服务实例
               </a>
               。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question15">
              应该如何在基本层和标准层之间作出选择？
             </a>
             <section>
              <p>
               基本层适用于单独的开发/测试，不附带 SLA。它适用于希望探索 Azure Spring Apps 的人。
              </p>
              <p>
               标准层适用于常规用途生产工作负载，并附带 SLA。
              </p>
             </section>
            </div>
           </li>
          </ul>
         </div>
        </div>
        <!-- <div class="pricing-page-section">
								<h2>支持和服务级别协议</h2>
								<p>若要了解有关我们的服务器级别协议的详细信息，请访问<a href="../../../../support/sla/storage/index.html"
										id="pricing_storage_sla">服务级别协议</a>页。</p>
							</div> -->
        <!--BEGIN: Support and service code chunk-->
        <!--END: Support and service code chunk-->
       </div>
      </div>
     </div>
    </div>
   </div>
   <!-- END: Documentation Content -->
   <!-- BEGIN: Footer -->
   <div class="public_footerpage">
   </div>
   <!--END: Common sidebar-->
   <link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
   <script type="text/javascript">
    function getAntiForgeryToken() {
				var token =
					'<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
				token = $(token).val();
				return token;
			}

			function setLocaleCookie(localeVal) {
				var Days = 365 * 10; // Ten year expiration
				var exp = new Date();

				var hostName = window.location.hostname;
				var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

				exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
				document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
					.toGMTString();
			}

			setLocaleCookie(window.currentLocale);
   </script>
   <script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
			var TECHNICAL_STORAGE = '/blob/tech-content/';
			var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
			var EnablePricingSync = 'false';
   </script>
   <!-- BEGIN: Minified RequireJs -->
   <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
   </script>
   <script src="../../../../Static/Scripts/require.js" type="text/javascript">
   </script>
   <script src=" /Static/Scripts/pricing-page-detail.js" type="text/javascript">
   </script>
   <script src="../../../Static/Scripts/2c76879c65af503b3c0d969c85243b9f28fdb5c1.js" type="text/javascript">
   </script>
   <!-- <script src='../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'> -->
   <!-- END: Minified RequireJs -->
   <!-- begin JSLL -->
   <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
   </script>
   <script type="text/javascript">
    (function () {
				var config = {
					cookiesToCollect: ["_mkto_trk"],
					syncMuid: true,
					ix: {
						a: true,
						g: true
					},
					coreData: {
						appId: 'AzureCN',
						market: 'zh-cn',
					}
				};
				awa.init(config);
			})();
   </script>
   <!-- end JSLL -->
   <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
   </script>
   <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
   </script>
   <script src="/common/useCommon.js" type="text/javascript">
   </script>
  </div>
 </body>
</html>
