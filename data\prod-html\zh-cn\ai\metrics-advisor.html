<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta content="Azure, 微软云, Azure Service Fabric, 定价, 费用" name="keywords" />
    <meta
        content="了解Azure Service Fabric 的价格详情。Service Fabric 可用于任何标准大小的 Windows Server 虚拟机。仅针对用户选择的计算实例以及用于创建 Service Fabric 群集的存储和网络资源收费，对 Service Fabric 服务本身不收取任何费用。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户。"
        name="description" />
    <title>
        Metrics Advisor - Azure云计算
    </title>
    <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/service-fabric/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }

        .remove_list_style {
            list-style: none;
            overflow: hidden;
            background-color: #f4f5f6;
        }

        .title_list {
            display: inline-block;
            float: left;
            width: 50%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <div class="hide-info" style="display:none;">
        <div class="bg-box">
            <div class="cover-bg">
            </div>
        </div>
        <div class="msg-box">
            <div class="pricing-unavailable-message">
                所选区域不可用
            </div>
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/17/2020" ms.service="metrics-advisor" wacn.date="9/17/2020">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="/Images/marketing-resource/documents/MetricsAdvis.svg" />
                                    <h2>
                                        Azure 指标顾问
                                    </h2>
                                    <h4>
                                        一项主动监测指标和诊断问题的AI数据分析服务
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        <div class="pricing-page-section">
                            <h3>
                                监视指标和诊断问题的 AI 服务
                            </h3>
                            <p>
                                Azure 指标顾问是一项 Azure 应用的 AI 服务，可主动监视指标和诊断问题，使用户能够更快、更准确地解决问题，且无需任何机器学习专业知识。Azure
                                指标顾问以异常检测器为基础进行构建，可提供多维分析、根本原因诊断和警报、模型自定义和优化、流监视，以及简化的时序数据引入。可将其用于预测性维护、AI
                                操作和业务指标监视。
                            </p>
                            <br />
                            <h2>
                                定价详细信息
                            </h2>
                            <!-- BEGIN: TAB-CONTROL -->
                            <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
                                <div class="tab-container-container">
                                    <div class="tab-container-box">
                                        <div class="tab-container">
                                            <div class="dropdown-container software-kind-container"
                                                style="display:none;">
                                                <label>
                                                    OS/软件:
                                                </label>
                                                <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                    <span class="selected-item">
                                                        Azure 指标顾问
                                                    </span>
                                                    <i class="icon">
                                                    </i>
                                                    <ol class="tab-items">
                                                        <li class="active">
                                                            <a data-href="#tabContent1" href="javascript:void(0)"
                                                                id="home_metrics-advisor">
                                                                Azure 指标顾问
                                                            </a>
                                                        </li>
                                                    </ol>
                                                </div>
                                                <select class="dropdown-select software-box hidden-lg hidden-md"
                                                    id="software-box">
                                                    <option data-href="#tabContent1" selected="selected"
                                                        value="metrics-advisor">
                                                        Azure 指标顾问
                                                    </option>
                                                </select>
                                            </div>
                                            <div class="dropdown-container region-container">
                                                <label>
                                                    地区:
                                                </label>
                                                <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                                                    <span class="selected-item">
                                                        中国东部 2
                                                    </span>
                                                    <i class="icon">
                                                    </i>
                                                    <ol class="tab-items">
                                                        <!-- id 对应 soft-category 的 region -->
                                                        <li class="active">
                                                            <a data-href="#east-china2" href="javascript:void(0)"
                                                                id="east-china2">
                                                                中国东部 2
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a data-href="#north-china2" href="javascript:void(0)"
                                                                id="north-china2">
                                                                中国北部 2
                                                            </a>
                                                        </li>
                                                        <!--                                                    <li><a href="javascript:void(0)" data-href="#east-china"-->
                                                        <!--                                                           id="east-china">中国东部</a></li>-->
                                                        <!--                                                    <li><a href="javascript:void(0)" data-href="#north-china"-->
                                                        <!--                                                           id="north-china">中国北部</a></li>-->
                                                    </ol>
                                                </div>
                                                <select class="dropdown-select region-box hidden-lg hidden-md"
                                                    id="region-box">
                                                    <option data-href="#east-china2" selected="selected"
                                                        value="east-china2">
                                                        中国东部 2
                                                    </option>
                                                    <option data-href="#north-china2" value="north-china2">
                                                        中国北部 2
                                                    </option>
                                                    <!--                                                <option data-href="#east-china" value="east-china">中国东部</option>-->
                                                    <!--                                                <option data-href="#north-china" value="north-china">中国北部</option>-->
                                                </select>
                                            </div>
                                            <div class="clearfix">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- BEGIN: TAB-CONTAINER-1 -->
                                <div class="tab-content">
                                    <!-- BEGIN: TAB-CONTENT-1 -->
                                    <div class="tab-panel" id="tabContent1">
                                        <!-- BEGIN: Tab level 2 navigator 2 -->
                                        <!-- BEGIN: Tab level 2 content 3 -->

                                        <div class="tab-content">
                                            <!-- BEGIN: TAB-CONTAINER-1 -->
                                            <div class="tab-panel" id="tabContent1">
                                                <!-- BEGIN: Table1-Content-->
                                                <h2>
                                                    Azure 指标顾问
                                                </h2>
                                                <!-- <table cellpadding="0" cellspacing="0" width="100%" id="azure_firewall_standard"> -->
                                                <table cellpadding="0" cellspacing="0" id="metrics_advisor_Desktop1"
                                                    width="100%">
                                                    <tr>
                                                        <th align="left">
                                                            <strong>
                                                                实例
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                功能
                                                            </strong>
                                                        </th>
                                                        <th align="left">
                                                            <strong>
                                                                每个时序的价格
                                                            </strong>
                                                        </th>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            标准
                                                        </td>
                                                        <td>
                                                            标准数据监视和异常情况检测
                                                        </td>
                                                        <td>
                                                            <ul>
                                                                <li>
                                                                    0-25 个时序 - 免费
                                                                </li>
                                                                <li>
                                                                    25-1,000 个时序 -￥7.632
                                                                </li>
                                                                <li>
                                                                    1,000-5,000 个时序 - ￥5.088
                                                                </li>
                                                                <li>
                                                                    5,000-20,000 个时序 - ￥2.543
                                                                </li>
                                                                <li>
                                                                    20,000-50,000 个时序 - ￥1.017
                                                                </li>
                                                                <li>
                                                                    50,000+ 个时序 - ￥0.509
                                                                </li>
                                                            </ul>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
    </script>
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="cO-ztjsaj4AipEd8qChMMWP4d1I0Zf9H9N-JVkDy1ZYltjKc0raP1R4b6ffJjWfcuFPkijs_T5tHY8B40px7VeRBQuNX3FsUo29Oy9k63dM1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="/Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
    </script>
    <!--            <script src='../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>