#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试debug工具的基本功能
验证debug脚本是否能正常工作

使用方法:
    python test_debug_tools.py
"""

import sys
import subprocess
from pathlib import Path

def run_command(cmd, description):
    """运行命令并返回结果"""
    print(f"\n🧪 测试: {description}")
    print(f"命令: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ 命令执行成功")
            if result.stdout:
                # 只显示前10行输出
                lines = result.stdout.split('\n')[:10]
                for line in lines:
                    if line.strip():
                        print(f"   {line}")
                if len(result.stdout.split('\n')) > 10:
                    print("   ... (输出已截断)")
        else:
            print("❌ 命令执行失败")
            if result.stderr:
                print(f"错误: {result.stderr[:200]}...")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 命令执行超时")
        return False
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def test_debug_tools():
    """测试debug工具的基本功能"""
    print("🔧 Debug工具功能测试")
    print("=" * 60)
    
    # 检查Python环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {Path.cwd()}")
    
    # 测试用例
    test_cases = [
        {
            "cmd": [sys.executable, "debug_batch_processing.py", "--help"],
            "desc": "批量处理调试器帮助信息"
        },
        {
            "cmd": [sys.executable, "debug_single_product.py", "--help"],
            "desc": "单产品调试器帮助信息"
        },
        {
            "cmd": [sys.executable, "debug_batch_processing.py", "list-categories"],
            "desc": "列出产品类别"
        },
        {
            "cmd": [sys.executable, "debug_single_product.py", "list-products", "database"],
            "desc": "列出database类别的产品"
        },
        {
            "cmd": [sys.executable, "debug_batch_processing.py", "check-files", "database"],
            "desc": "检查database类别的HTML文件"
        }
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for test_case in test_cases:
        success = run_command(test_case["cmd"], test_case["desc"])
        if success:
            success_count += 1
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_count} 通过")
    
    if success_count == total_count:
        print("✅ 所有测试通过！debug工具可以正常使用")
        print("\n💡 接下来可以尝试:")
        print("   python debug_batch_processing.py test-category database --dry-run")
        print("   python debug_single_product.py test mysql --dry-run")
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        print("\n🔧 可能的解决方案:")
        print("   1. 确保在项目根目录执行")
        print("   2. 检查Python依赖是否安装完整")
        print("   3. 确保data/configs目录存在且包含配置文件")
    
    return success_count == total_count

def check_environment():
    """检查环境配置"""
    print("\n🔍 环境检查:")
    print("-" * 30)
    
    # 检查关键文件和目录
    critical_paths = [
        "src/",
        "src/batch/",
        "src/core/",
        "data/configs/",
        "data/configs/products-index.json",
        "data/configs/categories.json"
    ]
    
    all_exist = True
    for path in critical_paths:
        path_obj = Path(path)
        if path_obj.exists():
            print(f"✅ {path}")
        else:
            print(f"❌ {path} (不存在)")
            all_exist = False
    
    if not all_exist:
        print("\n⚠️  关键文件缺失，debug工具可能无法正常工作")
    
    return all_exist

def main():
    """主函数"""
    print("🚀 开始测试debug工具...")
    
    # 环境检查
    env_ok = check_environment()
    
    if not env_ok:
        print("\n❌ 环境检查失败，请先修复环境问题")
        return 1
    
    # 功能测试
    test_ok = test_debug_tools()
    
    if test_ok:
        print("\n🎉 debug工具测试完成，一切正常！")
        return 0
    else:
        print("\n❌ debug工具测试失败，请检查问题")
        return 1

if __name__ == '__main__':
    sys.exit(main())
