<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 计划程序, 价格" name="keywords"/>
  <meta content="了解 Azure 计划程序（Scheduler）价格详情。利用 Azure 计划程序（Scheduler），可在云中创建在 Azure 内部和外部可靠调用服务的作业，并可按需或按定复计划运行这些作业或者为将来日期指定作业。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   计划程序 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/scheduler/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="scheduler" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/scheduler.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/<EMAIL>"/>
          <h2>
           计划程序
           <span>
            Scheduler
           </span>
          </h2>
          <h4>
           根据简单或复杂的定期计划运行作业
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <p>
         Azure 计划程序允许你按任意计划调用操作，如调用 HTTP/S 终结点或将消息发布到存储队列。利用计划程序，可在云中创建在 Azure 内部和外部可靠调用服务的作业，并可按需或按定期计划运行这些作业或者为将来日期指定作业。
        </p>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector tab-control-selector">
        <div class="tab-control-container tab-active">
         <h2>
          定价详细信息
         </h2>
         <div class="tags-date">
          <div class="ms-date">
           *以下价格均为含税价格。
          </div>
          <br/>
          <div class="ms-date">
           *每月价格估算基于每个月 744 小时的使用量。
          </div>
         </div>
         <table cellpadding="0" cellspacing="0" width="100%">
          <tr>
           <th align="left">
            <strong>
            </strong>
           </th>
           <th align="left">
            <strong>
             免费版
            </strong>
           </th>
           <th align="left">
            <strong>
             标准版
            </strong>
           </th>
           <th align="left">
            <strong>
             高级版 P10
            </strong>
           </th>
           <th align="left">
            <strong>
             高级版 P20
            </strong>
           </th>
          </tr>
          <tr>
           <td>
            每月价格
            <sup>
             1
            </sup>
           </td>
           <td>
            免费
           </td>
           <td>
            ￥96.81/单元
           </td>
           <td>
            ￥938.15/单元
           </td>
           <td>
            ￥10,300/单元
           </td>
          </tr>
          <tr>
           <td>
            可执行作业数
            <sup>
             2
            </sup>
           </td>
           <td>
            3,600
           </td>
           <td>
            无限制
           </td>
           <td>
            无限制
           </td>
           <td>
            无限制
           </td>
          </tr>
          <tr>
           <td>
            最高执行频率
           </td>
           <td>
            每小时
           </td>
           <td>
            每分钟
           </td>
           <td>
            每分钟
           </td>
           <td>
            每分钟
           </td>
          </tr>
          <tr>
           <td>
            作业集合数
           </td>
           <td>
            1
           </td>
           <td>
            10/单元
           </td>
           <td>
            10k/单元
           </td>
           <td>
            5k/单元
           </td>
          </tr>
          <tr>
           <td>
            每集合作业数
           </td>
           <td>
            5
           </td>
           <td>
            50
           </td>
           <td>
            50
           </td>
           <td>
            1,000
           </td>
          </tr>
          <tr>
           <td>
            缩放
           </td>
           <td>
            N/A
           </td>
           <td>
            最多10个单元
           </td>
           <td>
            1 个高级单元
            <br/>
            <a href="https://support.azure.cn/zh-cn/support/contact" id="scheduler_price_contactus01">
             联系我们
            </a>
            了解更多信息
           </td>
           <td>
            1 个高级单元
            <br/>
            <a href="https://support.azure.cn/zh-cn/support/contact" id="scheduler_price_contactus02">
             联系我们
            </a>
            了解更多信息
           </td>
          </tr>
          <tr>
           <td>
            出站身份验证
           </td>
           <td>
            --
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
           <td>
            √
           </td>
          </tr>
         </table>
         <p>
          <sup>
           1
          </sup>
          费用将根据小时数按比例分摊。标准单元将在每创建 10 个作业集合（或其中一部分）后进行计费，并将按小时分摊。同理，高级单元将在每创建 10,000 个作业集合（或其中一部分）后进行计费，同样按小时分摊。所有地域运行的作业将汇总后计费。
         </p>
         <p>
          <sup>
           2
          </sup>
          计划程序免费层服务中作业的执行次数将每 30 天计算一次。所有服务层都受制于最高执行频率和可执行作业总数的限制。在 30 天计算期限内，作业的执行数量实际会被限制为：每个标准单元 2160 万次执行，每个高级单元 216 亿次执行。上述表格中“无限制”一词是指在相应的最大执行频率范围内，每个单元可执行的作业总数是无限制的。
         </p>
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="scheduler_price_question1">
             如何对计划程序计费？
            </a>
            <section>
             <p>
              在存在一个或多个活跃作业集的情况下，计划程序将按小时分摊计费。一个标准单元按照所创建的每 10 个标准作业集合（或其中一部分），根据小时数按比例计费。同样，一个高级单元按照所创建的每 10,000 个高级作业集合（或其中一部分），根据小时数按比例计费。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="scheduler_price_question2">
             是否在订阅级别应用计划程序配额？
            </a>
            <section>
             <p>
              计划程序的配额将在订阅级别应用。此外，请注意，给定作业集合中的所有作业都将绑定到此作业集合的区域。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="scheduler_price_question3">
             作业执行日志将保留多长时间？
            </a>
            <section>
             <p>
              执行日志包含前 60 天的作业执行历史记录。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="scheduler_price_question4">
             我能否将我的计划程序服务从免费版升级到标准版？
            </a>
            <section>
             <p>
              可以，通过更新层级特性可以支持对现有作业集合的升级。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="scheduler_price_question6">
             计划程序的高可用性支持是如何实现的？
            </a>
            <section>
             <p>
              计划程序通过跨同一地域的两个区域进行配对支持高可用性。美国中南部与美国中北部配对，欧洲北部与欧洲西部配对，而亚太东部与亚太东南部配对。提交至给定地域（请参考下面的上市地域）的作业可以在配对的任一区域运行。执行日志会显示给定作业运行的特定区域。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a id="scheduler_price_question5">
             计划程序是否是独立于移动服务中的计划作业功能之外的服务？
            </a>
            <section>
             <p>
              可以。虽然这二者都可以提供计划功能，但它们是独立的。移动服务中的计划作业是一个此类服务的功能。计划程序是具有更广泛的使用场合的独立计划程序。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <!-- <div class="pricing-page-section">
                <h2>上市地区</h2>
                <p>计划程序在以下区域中提供：</p>
                <table cellpadding="0" cellspacing="0" class="table-col6">
                    <tr>
                        <th align="left"><strong>地域</strong></th>
                        <th align="left"><strong>区域</strong></th>
                    </tr>
                    <tr>
                        <td>中国大陆</td>
                        <td>中国东部数据中心 , 中国北部数据中心</td>
                    </tr>
                </table> -->
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="scheduler-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         我们保证在至少 99.9% 的情况下，所有计划调用操作会在按计划执行时间的 30 分钟内启动。可用性按每月计费结算。若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/scheduler/index.html" id="pricing_scheduler_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--

            <h2>支持和服务级别协议</h2>
            <p>Azure 支持功能：</p>
            <p>我们免费向用户提供以下支持服务：</p>
            <table cellpadding="0" cellspacing="0" class="table-col6">
                <tr>
                    <th align="left">&nbsp;</th>
                    <th align="left"><strong>是否支持</strong></th>
                </tr>
                <tr>
                    <td>计费和订阅管理</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>服务仪表板</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>Web事件提交</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>中断/修复不受限制</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>电话支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
                <tr>
                    <td>ICP备案支持</td>
                    <td><i class="icon icon-tick"></i></td>
                </tr>
            </table>
            <p>您可以<a href="/support/support-ticket-form/?l=zh-cn" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
            <h2>服务热线：</h2>
            <ul>
                <li>400-089-0365</li>
                <li>010-84563652</li>
            </ul>
            <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/zh-cn/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
            <p>更多支持信息，请访问<a href="/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>
              
      -->
       <!--END: Support and service code chunk-->
       <!--BEGIN: Support and service code chunk-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="y1AOkq1g749q5L-onQg9WA74bM8ILuhk222izKJ48tj6G9Dl_3RTpyqTMD7A6DrglYVBCAkOGQp0t2P3TLPoGu4BvUwFslfwSwUdfERAe1c1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
