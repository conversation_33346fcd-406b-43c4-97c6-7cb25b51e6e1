<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, 物联网, Azure IoT 中心, Azure IoT Hub, 监控物联网资产, 双向通信, 价格" name="keywords"/>
  <meta content="了解 Azure IoT 中心（Azure IoT Hub）价格详情。使用 Azure IoT 中心连接、监视并控制数百万物联网资产。Azure IoT 中心提供免费版、S1 、S2、S3 版。Azure IoT 中心免费版可每天传输最多 8,000 条消息。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。" name="description"/>
  <title>
   Azure IoT解决方案服务定价 - Azure云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/iot-hub/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="10/19/2020" ms.service="iot-hub" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        .svg {
                            width: 50px;
                            float: left;
                            margin-right: 10px;
                        }

                        .sub_list {
                            overflow: hidden;
                            background: #f4f5f6;
                            padding: 20px 0;
                        }

                        .sub_list > li {
                            display: block;
                            list-style: none;
                            float: left;
                            width: 50%;
                        }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <tags ms.date="09/30/2015" ms.service="iot-hub" wacn.date="11/27/2015">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/azure-Iot-hub.jpg','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/azure-iot-hub80.png"/>
          <h2>
           Azure IoT 中心
           <span>
            Azure IoT Hub
           </span>
          </h2>
          <h4>
           连接、监视并控制数十亿 IoT 资产
          </h4>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <div class="pricing-page-section">
         <p>
          连接、监视并控制在各种操作系统和协议上运行的数百万 IoT 资产，通过 IoT 中心快速启动物联网项目。使用每设备身份验证，通过合适的凭据与设备通信，提升 IoT 解决方案的安全性。借助这些资产安全地建立可靠的双向通信（即使是间歇性连接），以便分析传入的遥测、同步设备管理工作流，并根据需要发送命令和通知。
         </p>
        </div>
        <div class="pricing-page-section">
         <h2>
          定价详细信息
         </h2>
        </div>
        <!-- BEGIN: TAB-CONTROL -->
        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
         <div class="tab-container-container">
          <div class="tab-container-box">
           <div class="tab-container">
            <div class="dropdown-container software-kind-container" style="display:none;">
             <label>
              OS/软件:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               Azure IoT 中心
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
               <li class="active">
                <a data-href="#tabContent1" href="javascript:void(0)" id="Azure IoT">
                 Azure IoT 中心
                </a>
               </li>
              </ol>
             </div>
             <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
              <option data-href="#tabContent1" selected="selected" value="Azure IoT">
               Azure IoT 中心
              </option>
             </select>
            </div>
            <div class="dropdown-container region-container">
             <label>
              地区:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               中国东部 3
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
               <!-- id 对应 soft-category 的 region -->
               <li class="active">
                <a data-href="#east-china3" href="javascript:void(0)" id="east-china3">
                 中国东部 3
                </a>
               </li>
               <li>
                <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                 中国北部 3
                </a>
               </li>
               <li>
                <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                 中国东部 2
                </a>
               </li>
               <li>
                <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                 中国北部 2
                </a>
               </li>
               <li>
                <a data-href="#east-china" href="javascript:void(0)" id="east-china">
                 中国东部
                </a>
               </li>
               <li>
                <a data-href="#north-china" href="javascript:void(0)" id="north-china">
                 中国北部
                </a>
               </li>
              </ol>
             </div>
             <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
                <option data-href="#east-china3" selected="selected" value="east-china3">
                    中国东部 3
                   </option>
              <option data-href="#north-china3" value="north-china3">
               中国北部 3
              </option>
              <option data-href="#east-china2" value="east-china2">
               中国东部 2
              </option>
              <option data-href="#north-china2" value="north-china2">
               中国北部 2
              </option>
              <option data-href="#east-china" value="east-china">
               中国东部
              </option>
              <option data-href="#north-china" value="north-china">
               中国北部
              </option>
             </select>
            </div>
            <div class="clearfix">
            </div>
           </div>
          </div>
         </div>
         <!-- BEGIN: TAB-CONTAINER-1 -->
         <div class="tab-content">
          <!-- BEGIN: TAB-CONTENT-1 -->
          <div class="tab-content" id="tabContent1">
           <h3>
            Iot 中心
           </h3>
           <p>
            连接 IoT 设备，构建 IoT 解决方案，将数据转换为商业智能。
           </p>
           <h4>
            基本级别
            <sup style="color:#e66102; font-weight: bold">
             new
            </sup>
           </h4>
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="azure-iot-basic-north3" width="100%">
            <tr>
             <th align="left">
              <strong>
               版本类型
              </strong>
             </th>
             <th align="left">
              <strong>
               价格/单位/月
              </strong>
             </th>
             <th align="left">
              <strong>
               消费总数/天/单位
              </strong>
             </th>
             <th align="left">
              <strong>
               消费计算大小
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              B1
             </td>
             <td>
              ￥ 101.76
             </td>
             <td>
              400,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              B2
             </td>
             <td>
              ￥ 508.80
             </td>
             <td>
              6,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              B3
             </td>
             <td>
              ￥ 5,088.00
             </td>
             <td>
              300,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="azure-iot-basic-elsearea" width="100%">
            <tr>
             <th align="left">
              <strong>
               版本类型
              </strong>
             </th>
             <th align="left">
              <strong>
               价格/单位/月
              </strong>
             </th>
             <th align="left">
              <strong>
               消费总数/天/单位
              </strong>
             </th>
             <th align="left">
              <strong>
               消费计算大小
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              B1
             </td>
             <td>
              ￥ 101.76
             </td>
             <td>
              400,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              B2
             </td>
             <td>
              ￥ 508.80
             </td>
             <td>
              6,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              B3
             </td>
             <td>
              ￥ 5,088.00
             </td>
             <td>
              300,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
           </table>
           <h4>
            标准级别
           </h4>
           <div class="tags-date">
            <div class="ms-date">
             *以下价格均为含税价格。
            </div>
            <br/>
            <div class="ms-date">
             *每月价格估算基于每个月 744 小时的使用量。
            </div>
           </div>
           <table cellpadding="0" cellspacing="0" id="azure-iot-standard-north3" width="100%">
            <tr>
             <th align="left">
              <strong>
               版本类型
              </strong>
             </th>
             <th align="left">
              <strong>
               价格/单位/月
              </strong>
             </th>
             <th align="left">
              <strong>
               消费总数/天/单位
              </strong>
             </th>
             <th align="left">
              <strong>
               消费计算大小
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              免费
             </td>
             <td>
              免费
             </td>
             <td>
              8,000
             </td>
             <td>
              0.5KB
             </td>
            </tr>
            <tr>
             <td>
              S1
             </td>
             <td>
              ￥ 254.40
             </td>
             <td>
              400,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              S2
             </td>
             <td>
              ￥ 2,544.00
             </td>
             <td>
              6,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              S3
             </td>
             <td>
              ￥ 25,440.00
             </td>
             <td>
              300,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
           </table>
           <table cellpadding="0" cellspacing="0" id="azure-iot-standard-elsearea" width="100%">
            <tr>
             <th align="left">
              <strong>
               版本类型
              </strong>
             </th>
             <th align="left">
              <strong>
               价格/单位/月
              </strong>
             </th>
             <th align="left">
              <strong>
               消费总数/天/单位
              </strong>
             </th>
             <th align="left">
              <strong>
               消费计算大小
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              免费
             </td>
             <td>
              免费
             </td>
             <td>
              8,000
             </td>
             <td>
              0.5KB
             </td>
            </tr>
            <tr>
             <td>
              S1
             </td>
             <td>
              ￥ 254.40
             </td>
             <td>
              400,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              S2
             </td>
             <td>
              ￥ 2,544.00
             </td>
             <td>
              6,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
            <tr>
             <td>
              S3
             </td>
             <td>
              ￥ 25,440.00
             </td>
             <td>
              300,000,000
             </td>
             <td>
              4KB
             </td>
            </tr>
           </table>
           <p>
            如需详细了解基本层和标准层支持的功能，请参阅
            <a aria-label="iot-contact-page" href="https://docs.azure.cn/zh-cn/iot-hub/iot-hub-scaling">
             如何选择适合的 IoT 中心层
            </a>
            。
           </p>
           <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
             <th align="left">
              <strong>
               功能
              </strong>
             </th>
             <th align="left">
              <strong>
               基本
              </strong>
             </th>
             <th align="left">
              <strong>
               标准
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              设备到云遥测
             </td>
             <td>
              √
             </td>
             <td>
              √
             </td>
            </tr>
            <tr>
             <td>
              每设备标识
             </td>
             <td>
              √
             </td>
             <td>
              √
             </td>
            </tr>
            <tr>
             <td>
              消息路由、事件网格集成
             </td>
             <td>
              √
             </td>
             <td>
              √
             </td>
            </tr>
            <tr>
             <td>
              HTTP、AMQP、MQTT 协议
             </td>
             <td>
              √
             </td>
             <td>
              √
             </td>
            </tr>
            <tr>
             <td>
              DPS 支持
             </td>
             <td>
              √
             </td>
             <td>
              √
             </td>
            </tr>
            <tr>
             <td>
              监视和诊断
             </td>
             <td>
             </td>
             <td>
              √
             </td>
            </tr>
            <tr>
             <td>
              云到设备消息传递
             </td>
             <td>
             </td>
             <td>
              √
             </td>
            </tr>
            <tr>
             <td>
              设备管理、设备孪生
             </td>
             <td>
             </td>
             <td>
              √
             </td>
            </tr>
           </table>
           <h3>
            Azure IoT 中心设备预配服务
           </h3>
           <p>
            对合适的 IoT 中心启用无需人为干预的零接触预配，客户可以采用安全且可缩放的方式预配数百万台设备。
           </p>
           <p>
            使用 Azure IoT 中心设备预配服务，无需人为干预即可零接触预配至合适的 IoT 中心，客户可以采用安全且可缩放的方式预配数百万台设备。它还可以启用设备生命周期支持持和重要功能，可与 IoT 中心设备管理配合使用，帮助客户处理 IoT 设备生命周期的各个阶段。
           </p>
           <table cellpadding="0" cellspacing="0" width="100%">
            <tr>
             <th align="left">
              <strong>
               层
              </strong>
             </th>
             <th align="left">
              <strong>
               价格
              </strong>
             </th>
            </tr>
            <tr>
             <td>
              S1
             </td>
             <td>
              每 1,000 次操作 ￥0.636
             </td>
            </tr>
           </table>
          </div>
          <!-- END: TAB-CONTENT-3 -->
         </div>
         <!-- END: TAB-CONTAINER-1 -->
        </div>
        <!-- END: TAB-CONTROL -->
        <div class="pricing-page-section">
         <div class="more-detail">
          <h2>
           常见问题
          </h2>
          <em>
           全部展开
          </em>
          <ul>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q1">
              Azure IoT 中心免费版中包括哪些内容？
             </a>
             <section>
              <p>
               Azure IoT 中心免费版旨在促进概念证明项目。可让你每天共传输多达 8,000 条消息，并且注册多达 500 个设备 ID。设备 ID 限制现仅存在于免费版。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q2">
              如何增加或减少购买的 IoT 中心单位数？
             </a>
             <section>
              <p>
               每个层都有每日消息数限制，超出后就会被限制。请参阅
               <a href="https://docs.azure.cn/zh-cn/iot-hub/iot-hub-devguide-quotas-throttling">
                限制文档
               </a>
               了解详细信息。如需更改，可在 IoT 中心内调整单位。也可在层内更改单位类型（例如从 S1 变为 S2）或升级至更高的层（例如从 B1 升级至 S2）。如果希望使用超过 200 个单位的 IoT 中心 S1 S2 SKU 和 IoT 中心 B1 B2 SKU 或者超过 10 个单位的 IoT 中心 B3 或 S3，请联系
               <a href="https://support.azure.cn/zh-cn/support/contact">
                Azure 支持部门
               </a>
               。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q3">
              我可通过 IoT 中心发送的最大消息大小是多少？
             </a>
             <section>
              <p>
               从设备发送到云的消息的最大消息大小为 256 KB。在付费版（即 S1 版和 S2 版）中，这些消息按 4 KB 的块计量。例如，如果设备通过 S1 版或 S2 版发送一条 16 KB 大小的消息，这条消息将按 4 条消息计费。已连接设备通过免费版发送的消息按 0.5 KB 的块计量。例如，如果设备通过 IoT 中心免费版发送一条 16 KB 大小的消息，这条消息将按 32 条消息计费。
              </p>
              <p>
               对于付费 SKU （即 S1 版和 S2 版），从云发送到设备的最大消息大小为 64 KB，按 4 KB 的块计量。例如，一条通过 S1 版或 S2 版发送的 8 KB 大小的消息将按 2 条消息计费。使用免费版发送的消息以 0.5 KB 的块计量。例如，一条通过 IoT 中心免费版发送的 8 KB 大小的消息将按 16 条消息计费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q4">
              是否会对与文件上传相关的消息收费？
             </a>
             <section>
              <p>
               仅启动新上传并提供完成上传通知的消息才计入每日消息分配。客户必须提供其自己的存储帐户并为存储付费。不会针对文件上传单独收费。例如，在典型文件上传方案中，仅有两条消息，一条消息指示已启动文件上传，另一条消息指示已完成文件上传。在收到上传失败通知时，客户可选择在状态说明中发送附加数据。有关文件上传功能的其他详细信息，请参阅 IoT 中心文档。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q5">
              我可通过文件上传功能发送的最大文件大小是多少？
             </a>
             <section>
              <p>
               IoT 中心的文件上传功能遵循 Azure 存储已经实施的上传限制。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q6">
              IoT 中心环境中的设备孪生和查询是什么？
             </a>
             <section>
              <p>
               设备对包含设备状态信息（元数据、配置和条件），并存储为 JSON 文档。对可由云和设备应用程序修改，且支持大量查询语言。设备孪生是 IoT 中心设备管理的原始基础，可通过读取、写入和查询等多个操作进行访问。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q7">
              成对读取、写入和查询如何计费？
             </a>
             <section>
              <p>
               成对读取、写入和查询仅在标准层中可用，按 0.5KB 的块计费，且分别考虑返回的对大小、更新大小和查询结果大小。例如，读取 4 KB 的对按 8 条消息计费，更新 1 KB 有效负载的对按 2 条消息计费，查询 10 KB 结果的对按 20 条消息计费。所有其他消息针对付费层（S1、S2、S3）以 4 KB 的块计费，针对免费层以 0.5 KB 计费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q8">
              直接方法如何计费？
             </a>
             <section>
              <p>
               直接方法是由云发起的请求响应通信，且仅在标准层中可用。云从设备接收即时响应。请求和响应两者最多均为 8KB，且针对付费层（如 S1、S2 和 S3）以 4KB 的块进行计量，因此如果通过 S1、S2 或 S3 层本发送了 8KB 请求，该请求将按 2 条消息计费。使用免费层发送的请求以 0.5KB 的块计量。例如，一条通过 IoT 中心免费层发送的 8KB 大小的消息将按 16 条消息计费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q9">
              长时运行的作业如何计费？
             </a>
             <section>
              <p>
               作业允许在大量设备上启动或安排成对写入和直接方法的执行。作业操作不计费，但产生的成对写入和方法调用要如上述计费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q10">
              Azure IoT 中心服务的计费方式是什么？
             </a>
             <section>
              <p>
               以天为单位测量 IoT 中心单位的使用量，并每月生成帐单。根据本月内消费的 IoT 中心单位数对客户进行计费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="ih-que-q11">
              如果我在月中购买额外的 IoT 中心单位，是否按比例计费？
             </a>
             <section>
              <p>
               你可以随时选择增加购买的 IoT 中心单位数。如果在月中注册服务，则本月费用将根据本月剩余天数按比例进行计算。（对于 IoT 中心服务，一个月定义为 31 天）。如果在月中增加 IoT 中心单位数，则本月费用将根据本月每天可用的单位数进行计算。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="vm-que-q12">
              如果业务需要更改，能否减少单位数？
             </a>
             <section>
              <p>
               是的，可以随时从“设置”页面减少单位数量。更改将于次日生效，在月底反映在帐单上。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="vm-que-q13">
              我可以从免费版切换到付费版吗？
             </a>
             <section>
              <p>
               否，你不能从免费版切换到付费版。免费版仅用于测试概念证明解决方案。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="vm-que-q14">
              如何取消 IoT 中心服务订阅？
             </a>
             <section>
              <p>
               可从
               <a href="https://portal.azure.cn/">
                Azure 管理门户
               </a>
               中取消 IoT 中心订阅。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="vm-que-q15">
              IoT 中心中的设备管理费用是多少？
             </a>
             <section>
              <p>
               设备管理功能是 IoT 中心的一部分。设备管理消息的测量方式与 IoT 中心中的其他遥测消息类似。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="iot-dps-billing">
              IoT 中心设备预配服务如何计费？
             </a>
             <section>
              <p>
               Azure IoT 中心设备预配服务按操作数计费。操作包括设备注册和重新注册，还包括服务更改（例如添加注册列表条目、更新注册列表条目和获取/查询操作）。给定的注册条目上，每六个月处于不活动状态将引发“保持活动状态”操作。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="vm-que-q16">
              可以在任何标准 IoT 中心版之间切换吗？
             </a>
             <section>
              <p>
               是的，你必须采取措施从一个付费版本显式切换到另一版本。设备 ID 和所有消息均传递到新版本中。如果客户想要从较高级别切换到较低级别（例如，从 S2 切换到 S1），并且他们使用的消息数超出了 S1 允许的每日消息数，则当天将按 S2 对该客户收费，并从下一天起按 S1 费率收费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="vm-que-q17">
              可以在任何基本 IoT 中心版之间切换吗？
             </a>
             <section>
              <p>
               是的，你必须采取措施从一个付费版本显式切换到另一版本。设备 ID 和所有消息均传递到新版本中。如果客户想要从较高级别切换到较低级别（例如，从 B2 切换到 B1），并且他们使用的消息数超出了 B1 允许的每日消息数，则当天将按 B2 对该客户收费，并从下一天起按 B1 费率收费。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="iot-switch-basic-to -tandard">
              可以在 IoT 中心基本层与标准层之间相互切换吗？
             </a>
             <section>
              <p>
               可以。可通过 Azure 门户从基本层升级为标准层，但不能从标准层降级为基本层。若要从标准层移至基本层，必须创建一个新的基本层 IoT 中心，并需向该 IoT 中心重新注册设备。
              </p>
             </section>
            </div>
           </li>
          </ul>
         </div>
        </div>
        <div class="pricing-page-section">
         <h2>
          支持和服务级别协议
         </h2>
         <p>
          如有任何疑问或需要帮助，请访问
          <a href="https://support.azure.cn/zh-cn/support/contact" id="iot-contact-page">
           Azure 支持
          </a>
          选择自助服务或者其他任何方式联系我们获得支持。
         </p>
         <p>
          对于 IoT 中心，我们保证在 99.9% 的时间里，已部署的 IoT 中心能够向已注册的设备发送信息并从中接收信息，并且该服务能够在 IoT 中心上执行创建、读取、更新和删除操作。
         </p>
         <p>
          IoT 中心免费版不提供服务级别协议。
         </p>
         <p>
          若要了解有关我们的服务器级别协议的详细信息，请访问
          <a href="../../../support/sla/iot-hub/index.html" id="pricing_iot-hub_sla">
           服务级别协议
          </a>
          页。
         </p>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
  </script>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token =
            '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
            .toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
  </script>
  <!--<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'>-->
  <!--</script>-->
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
