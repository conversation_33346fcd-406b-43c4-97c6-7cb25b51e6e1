<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="Azure, Service Bus, message transfer, transfer connection" name="keywords"/>
    <meta content="Learn more about the pricing details of Azure Service Bus (Search Bus). Azure Service Bus (Search Bus) is a message transfer infrastructure located among various applications, permitting the applications to exchange messages, so as to expand the scale and improve recovery capabilities. A 1RMB Trial gets you RMB1,500 in service credits. You can also make a direct purchase and become a Pay-in-Advance Azure customer to enjoy a Service Level Agreement of up to 99.99%."
          name="description"/>
    <title>
        Service Bus Pricing Details - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/service-bus/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-service-bus" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/service_bus01.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/media/images/production/service-bus-icon01.png"/>
                                <h2>
                                    Service Bus
                                </h2>
                                <h4>
                                    Connect across private and public cloud environments
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Azure Service Bus is a messaging infrastructure that sits between applications allowing them to exchange messages for
                            improved scale and resiliency.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <div class="technical-azure-selector tab-control-selector">
                        <div class="tab-control-container tab-active">
                            <h2>
                                Pricing Details
                            </h2>
                            <p>
                                Service Bus comes in Basic and Standard tiers, and premium tiers. Here’s how they compare:
                            </p>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Features
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Basic
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Standard
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                            Premium
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Queues
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Scheduled messages
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Topics
                                    </td>
                                    <td>
                                        -
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Transactions
                                    </td>
                                    <td>
                                        -
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        De-duplication
                                    </td>
                                    <td>
                                        -
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Sessions
                                    </td>
                                    <td>
                                        -
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        ForwardTo/SendVia
                                    </td>
                                    <td>
                                        -
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Message size
                                    </td>
                                    <td>
                                        256 KB
                                    </td>
                                    <td>
                                        256 KB
                                    </td>
                                    <td>
                                        100 MB
                                    </td>
                                </tr>
                                <!-- <tr>
                                                  <td>中继</td>
                                                  <td><i class="icon icon-tick"></i></td>
                                                  <td><i class="icon icon-tick"></i></td>
                                              </tr> -->
                                <!-- <tr>
                                                  <td>事件中心<sup> 1</sup></td>
                                                  <td><i class="icon icon-tick"></i></td>
                                                  <td><i class="icon icon-tick"></i></td>
                                              </tr> -->
                                <tr>
                                    <td>
                                        Brokered connections included
                                    </td>
                                    <td>
                                        100
                                    </td>
                                    <td>
                                        1,000
                                        <sup>
                                            1
                                        </sup>
                                    </td>
                                    <td>
                                        1,000 per MU
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Brokered connections (overage allowed)
                                    </td>
                                    <td>
                                        -
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                        (billable)
                                    </td>
                                    <td>
                                        Up to 1,000 per MU
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Resource isolation
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Availability Zones (AZ) support
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                    <td>
                                        <i class="icon icon-tick">
                                        </i>
                                    </td>
                                </tr>
                            </table>
                            <!-- <p><sup>1</sup> 在服务使用过程中，事件中心的使用将单独计费，请访问<a id="service_bus_transfer_price-page-event-center" href="/en-us/pricing/details/event-hubs/" target="_blank">事件中心价格页面</a>了解相关费率。</p> -->
                            <div class="tags-date">
                                <div class="ms-date">
                                    Service Bus premium runs in dedicated resources to provide higher throughput and more consistent
                                    performance.
                                </div>
                                <div class="ms-date">
                                    <sup>
                                        1
                                    </sup>
                                    1,000 brokered connections are included with the standard messaging tier (via the base
                                    charge) and can be shared across all queues, topics, subscriptions, and event hubs within the associated Azure
                                    subscription.
                                </div>
                            </div>
                            <h2>
                                Message Transfer Operations
                            </h2>
                            <p>
                                An operation is any API call to the Service Bus service.
                            </p>
                            <p>
                                Starting May 2, 2018, customers will be billed at an hourly rate for Service Bus standard base units. This replaces
                                the current daily and monthly rate billing, making it more consistent with other Azure services. Your customers will
                                pay only for the number of hours they use Service Bus rather than for a whole day or month.
                            </p>
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    *Monthly pricing estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Basic
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Operations
                                    </td>
                                    <td>
                                        ￥0.31 per million operations
                                    </td>
                                </tr>
                            </table>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Standard
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Basic charge
                                        <sup>
                                            1
                                            <sup>
                                            </sup>
                                        </sup>
                                    </td>
                                    <td>
                                        ￥0.0851/hour (about￥63.3144/month)
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        First 13M operations/month
                                    </td>
                                    <td>
                                        Included
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Next 88M operations (13M - 100M operations)/month
                                    </td>
                                    <td>
                                        ￥5.21 per million operations
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Next 2,400M operations (100M - 2,500M operations)/month
                                    </td>
                                    <td>
                                        ￥3.20 per million operations
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Over 2,500M operations/month
                                    </td>
                                    <td>
                                        ￥1.27 per million operations
                                    </td>
                                </tr>
                            </table>
                            <p>
                                <sup>
                                    1
                                </sup>
                                Allocated in proportion every hour, 744 hours every month.
                            </p>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Premuim
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Hourly
                                    </td>
                                    <td>
                                        ￥9.4382/hour (about￥7,022.0208/month)
                                    </td>
                                </tr>
                            </table>
                            <h2>
                                Brokered connections
                            </h2>
                            <p>
                                Number of AMQP connections or HTTP calls to Service Bus.
                            </p>
                            <!--<p>从2016年4月1日起，中转连接的价格会下调 28.4%，以下是下调后的新价格：</p>-->
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    *Monthly pricing estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Standard Tier
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        First 1k/month
                                    </td>
                                    <td>
                                        Included
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Next 99K (1K – 100K)/month
                                    </td>
                                    <td>
                                        ￥0.18 per connection/month
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Next 400K (100K – 500K)/month
                                    </td>
                                    <td>
                                        ￥0.15 per connection/month
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Over 500K/month
                                    </td>
                                    <td>
                                        ￥0.10 per connection/month
                                    </td>
                                </tr>
                            </table>
                            <p>
                                This will be charged by the peak amount of concurrent connections, and the expenses are allocated in proportion every hour, 744 hours
                                every month.
                            </p>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            PREMIUM TIER
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Brokered connections are not charged in the premium tier.
                                    </td>
                                </tr>
                            </table>
                            <h2>
                                Hybrid connections
                            </h2>
                            <p>
                                Mixed connection is charged per listener, including 5 GB/month of data transmission. In the event of excess of this
                                limit, the extra parts will also be charged.
                            </p>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Hybrid Connection Pricing
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Connection charge (including 5 GB data/month)
                                        <sup>
                                            1
                                        </sup>
                                    </td>
                                    <td>
                                        ￥76.087/listener per month(￥0.102/listener per hour)
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Data transfer overage (data exceeding the included 5 GB/month)
                                    </td>
                                    <td>
                                        ￥13.398/GB
                                    </td>
                                </tr>
                            </table>
                            <div class="tags-date">
                                <div class="ms-date">
                                    <sup>
                                        1
                                    </sup>
                                    The data transfer limit of 5 GB covers total data transfer across all listener
                                    units.
                                </div>
                            </div>
                            <h2>
                                Relay
                            </h2>
                            <!--<p>中继仅在标准级别中可用，按消息量和中继小时数收费。</p>-->
                            <!--<p>从2016年4月1日起，中继的价格会下调 25.6%，以下是下调后的新价格：</p>-->
                            <div class="tags-date">
                                <div class="ms-date">
                                    *The following prices are tax-inclusive.
                                </div>
                                <br/>
                                <div class="ms-date">
                                    *Monthly pricing estimates are based on 744 hours of usage per month.
                                </div>
                            </div>
                            <table cellpadding="0" cellspacing="0" width="100%">
                                <tr>
                                    <th align="left">
                                        <strong>
                                            Relay Pricing
                                        </strong>
                                    </th>
                                    <th align="left">
                                        <strong>
                                        </strong>
                                    </th>
                                </tr>
                                <tr>
                                    <td>
                                        Relay hours
                                    </td>
                                    <td>
                                        ￥0.52 for every 100 relay hours
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        Messages
                                    </td>
                                    <td>
                                        ￥0.05 for every 10,000 messages
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <!-- END: TAB-CONTROL -->
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand All
                            </em>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_calculate_relay">
                                            How is the number of relay messages calculated?
                                        </a>
                                        <section>
                                            <p>
                                                The relay counts each message sent to the relay, and each message sent by the relay, as billable. A billable
                                                message is a data frame of at most 64 KB.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_calculate_topic">
                                            How is the operations meter calculated for queues and topics?
                                        </a>
                                        <section>
                                            <p>
                                                For brokered entities (queues and topics or subscriptions), an operation is any API interaction with Service Bus
                                                service on any protocol.
                                            </p>
                                            <p>
                                                A send, receive, delete for a message that's less than or equal to 64 KB in size is considered as one billable
                                                operation. If the message is greater than 64 KB in size, the number of billable operations is calculated
                                                according to the message size in multiples of 64 KB. For example, an 8-KB message sent to the Service Bus will
                                                be billed as one operation, but a 96-KB message sent to the Service Bus will be billed as two operations.
                                                Reading the 8-KB message with a lock and then completing or explicitly abandoning the message will be billed as
                                                two operations. Renewing the lock on a message also incurs an operation.
                                            </p>
                                            <p>
                                                Multiple deliveries of the same message (for example, message fan out to multiple subscribers or message
                                                retrieval after abandon, deferral, or dead lettering) will be counted as independent operations. For example, in
                                                the case of a topic with three subscriptions, a single 64 KB message sent and subsequently received will
                                                generate four billable operations, one "in" plus three "out," assuming all messages are delivered to all
                                                subscriptions and deleted during the read.
                                            </p>
                                            <p>
                                                You can receive messages by using PeekLock’s ReceiveMode, then deleting the message through a complete call,
                                                which will generate two individual operations. Relocking the message will also generate one operation.
                                            </p>
                                            <p>
                                                Additionally creating, reading (listing), updating, and deleting a queue, topic, or subscription will each incur
                                                an operation charge.
                                            </p>
                                            <p>
                                                Operations are API calls made against queue, topic, or subscription service endpoints. This includes management,
                                                send/receive, and session state operations.
                                            </p>
                                            <table border="0" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <th align="left">
                                                        <strong>
                                                            Operation Types
                                                        </strong>
                                                    </th>
                                                    <th align="left">
                                                        <strong>
                                                            Description
                                                        </strong>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        Management
                                                    </td>
                                                    <td>
                                                        Create, read, update, delete against queues, topics, or subscriptions
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        Messaging
                                                    </td>
                                                    <td>
                                                        Sending and receiving messages with queues, topics, or subscriptions
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        Session state
                                                    </td>
                                                    <td>
                                                        Getting or setting session state on a queue, topic, or subscription
                                                    </td>
                                                </tr>
                                            </table>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_calculate_hour">
                                            How are relay hours calculated?
                                        </a>
                                        <section>
                                            <p>
                                                Relay hours are billed for the cumulative amount of time during which each Service Bus Relay is "open". A
                                                relay is implicitly instantiated and opened at a given Service Bus address (service namespace URL) when a
                                                relay-enabled WCF service, or "relay listener," first connects to that address. It's closed only when the
                                                last listener disconnects from its address. Therefore, for billing purposes a relay is considered "open"
                                                from the time the first relay listener connects, to the time the last relay listener disconnects from the
                                                Service Bus address of that relay.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_bill_transfer">
                                            What is a brokered connection and how do I get charged for them?
                                        </a>
                                        <section>
                                            <p>
                                                A brokered connection is defined as one of the following:
                                            </p>
                                            <ol>
                                                <li>
                                                    An AMQP connection from the client into a Service Bus topic, subscription, queue, or event hub.
                                                </li>
                                                <li>
                                                    An HTTP call to receiving a message from a Service Bus topic or queues that has a receive timeout value
                                                    greater than zero.
                                                    <br/>
                                                    Charges for the peak number of concurrent brokered connections that exceed the
                                                    included quantity (1,000 in the standard tier). Peaks are measured on an hourly basis, prorated by dividing
                                                    by 744 hours in a month, and added up over the monthly billing period. The included quantity (1,000 brokered
                                                    connections per month) is applied at the end of the billing period against the sum of the prorated hourly
                                                    peaks.7
                                                    <br/>
                                                    Examples
                                                    <br/>
                                                    <ol>
                                                        <li>
                                                            10,000 clients connect via a single AMQP connection each, and receive commands from a Service Bus
                                                            topic and send events to queues. If all clients connect for 12 hours every day, you will see the
                                                            following connection charges (in addition to any other Service Bus charges)—10,000 connections * 12
                                                            hours * 31 days / 744 = 5,000 brokered connections. After the monthly allowance of 1,000 brokered
                                                            connections, you would be charged for 4,000 brokered connections at a rate of RMB0.18 for every
                                                            brokered connections, total￥720.
                                                        </li>
                                                        <li>
                                                            10,000 clients receive messages from a Service Bus queue via HTTP, specifying a non-zero timeout. If
                                                            all devices connect for 12 hours every day, you will see the following connection charges (in
                                                            addition to any other Service Bus charges): 10,000 HTTP receive connections * 12 hours per day * 31
                                                            days / 744 hours = 5,000 brokered connections.
                                                        </li>
                                                    </ol>
                                                </li>
                                            </ol>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_transfer_queue">
                                            Do brokered connection charges apply to queues and topics/subscriptions?
                                        </a>
                                        <section>
                                            <p>
                                                Yes, they do. There are no connection charges for sending events using HTTP, regardless of the number of sending
                                                systems or devices. Receiving events with HTTP using a timeout greater than zero, sometimes called "long
                                                polling," generate brokered connection charges. AMQP connections generate brokered connection charges regardless
                                                of whether the connections are being used to send or receive. Note that 100 brokered connections are allowed at
                                                no charge in a basic namespace (this is also the maximum number of brokered connections allowed for the Azure
                                                subscription). The first 1,000 brokered connections across any and all standard namespaces in an Azure
                                                subscription are included at no extra charge (beyond the base charge). Since these allowances are enough to
                                                cover many service-to-service messaging scenarios, brokered connection charges usually only become relevant if
                                                you plan to use AMQP or HTTP long-polling with a large number of clients, for example, to achieve more efficient
                                                event streaming, or enable bi-directional communication with thousands or millions of devices or app instances.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_standard_charge">
                                            Will I be charged multiple base charges if I have multiple standard namespaces in my
                                            Azure subscription?
                                        </a>
                                        <section>
                                            <p>
                                                No, the standard base charge is billed only once per month per Azure subscription. This means that after you
                                                create a single standard tier Service Bus namespace, you will be able to create as many additional standard tier
                                                namespaces as you like under that same Azure subscription without incurring additional base charges.
                                            </p>
                                            <p>
                                                For other FAQs about Service Bus billing, please refer to
                                                <a href="http://msdn.microsoft.com/library/azure/hh667438.aspx" id="service-bus-msdn">
                                                    MSDN articles
                                                </a>
                                                .
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <div>
                                        <i class="icon icon-plus">
                                        </i>
                                        <a id="service_bus_premium_dirferent">
                                            How is the premium tier different from the others?
                                        </a>
                                        <section>
                                            <p>
                                                The premium tier of Service Bus messaging provides all the messaging features of Azure Service Bus queues and
                                                topics with predictable, repeatable performance, higher throughput, and improved availability. The premium tier
                                                uses a dedicated resource allocation model to provide workload isolation and consistent performance. Because the
                                                compute and memory resources in the premium tier are dedicated, there are no per-message transaction charges as
                                                in other tiers. All transactions are included in the message unit allocation.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <div>
                                        <i class="icon icon-plus">
                                        </i>
                                        <a id="service_bus_what_is_messaging_unit">
                                            What is a messaging unit?
                                        </a>
                                        <section>
                                            <p>
                                                A messaging unit is a set of dedicated resources exclusively reserved for premium namespaces. This resource set can
                                                deliver a consistent and repeatable performance of messaging workloads. Each premium namespace can have 1, 2, or 4
                                                messaging units and the resource allocation grows linearly—2 messaging units will consist of twice as many resources
                                                allocated as 1 messaging unit.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_billing_work_for_premiun">
                                            How does billing work for the premium tier?
                                        </a>
                                        <section>
                                            <p>
                                                The premium tier of Service Bus messaging is a flat daily rate per messaging unit purchased. Namespaces
                                                created as premium can have 1, 2, or 4 messaging units which will each accrue the given number of messaging
                                                unit daily rate charges. Premium namespaces can have the number of purchased messaging units changed at any
                                                time, but the daily rate is based on the maximum number of message units assigned to the namespace at any
                                                time.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a id="service_bus_change_from_premium">
                                            Can I upgrade or downgrade between premium and other tiers?
                                        </a>
                                        <section>
                                            <p>
                                                Yes, it's technically possible to upgrade and downgrade between premium and other tiers. For guidelines on
                                                how to migrate your solution from standard messaging to premium messaging please read this
                                                <a href="https://blogs.msdn.microsoft.com/servicebus/2016/07/28/tips-on-migrating-existing-solutions-to-premium-messaging/">
                                                    blog
                                                    post
                                                </a>
                                                .
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <div>
                                        <i class="icon icon-plus">
                                        </i>
                                        <a id="service_bus_standard_mixed">
                                            What is a hybrid connection?
                                        </a>
                                        <section>
                                            <p>
                                                A hybrid connection allows you to establish bi-directional, binary stream communication between two networked
                                                applications, one or both parties can reside behind NATs or Firewalls. The listener that accepts this
                                                relayed connection and the sender that initiates the connection can both be implemented on any platform, and
                                                in any language, that has a basic WebSocket capability, including the WebSocket API in most web browsers.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <div>
                                        <i class="icon icon-plus">
                                        </i>
                                        <a id="service_bus_standard_charging">
                                            How are mixed connections billed?
                                        </a>
                                        <section>
                                            <p>
                                                When you create your first hybrid connection listener you will be charged at a per listener unit rate. The
                                                same rate applies to each individual listener that you decide to create. 5 GB of free data transfer per
                                                month is included with the service. You can use the 5 GB of free data transfer across all your listener
                                                units. You will be charged for data transfer overage if your aggregate data transfer across all listener
                                                units is more than 5 GB.
                                            </p>
                                            <p>
                                                <strong>
                                                    Pricing example 1:
                                                </strong>
                                                If you have a single listener, such as an instance of the hybrid
                                                connections manager installed and continuously running for the entire month, and you send 3 GB of data
                                                across the connection during the course of the month, your total charge will be￥76.09.
                                            </p>
                                            <p>
                                                <strong>
                                                    Pricing example 2:
                                                </strong>
                                                If you have a single listener, such as an instance of the hybrid
                                                connections manager installed and continuously running for the entire month, and you send 10 GB of data
                                                across the connection during the course of the month, your total charge will be￥143.077. That's based
                                                on￥76.087 for the connection and first 5 GB plus￥66.99 for the additional 5 GB of data.
                                            </p>
                                            <p>
                                                <strong>
                                                    Pricing example 3:
                                                </strong>
                                                If you have two instances, A and B, of the hybrid connections manager
                                                installed and continuously running for the entire month, and you send 3 GB of data across connection A, and
                                                6 GB across connection B, for a total of 9 GB of data, your total charge will be￥205.766. That's based
                                                on￥76.087 for connection A plus￥76.087 for connection B plus￥53.592 for the additional 4 GB of data overage.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <div>
                                        <i class="icon icon-plus">
                                        </i>
                                        <a id="service_bus_standard_whether-accounting">
                                            Is there any data transfer charge to
                                            make a connection to the hybrid connection listener?
                                        </a>
                                        <section>
                                            <p>
                                                We will charge 64 KB for each connection to your listener. This will be deducted from the 5 GB free we offer
                                                each month with listener units. The listener unit charge is calculated per hour in increments of 5 minutes.
                                                You will not be charged for multiple opens and closes for dev/test purposes.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <div>
                                        <i class="icon icon-plus">
                                        </i>
                                        <a id="service_bus_standard_how">
                                            How long will a hybrid connection listener remain
                                            open if there is no data transfer? What is the charge to keep the connection open?
                                        </a>
                                        <section>
                                            <p>
                                                If you open a connection and do not transfer any data, we will transfer 1 KB each minute on your behalf to
                                                keep the connection alive. We do this so the network doesn’t auto-close the connection every few minutes.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- <div class="pricing-page-section">
                             <h2>上市地区</h2>
                             <p>服务总线在以下区域中提供：</p>
                             <table cellpadding="0" cellspacing="0" class="table-col6">
                                 <tr>
                                     <th align="left"><strong>地域</strong></th>
                                     <th align="left"><strong>区域</strong></th>
                                 </tr>
                                 <tr>
                                     <td>中国大陆</td>
                                     <td>中国东部数据中心 , 中国北部数据中心</td>
                                 </tr>
                             </table> -->
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a href="https://support.azure.cn/en-us/support/contact" id="messaging-contact-page">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            For Service Bus Relay, we guarantee that at least 99.9% time, applications configured correctly can establish connections with the deployed
                            relay.
                        </p>
                        <p>
                            For the Service Bus queues and themes, we guarantee that at least 99.9% of the time, applications configured correctly
                            can send or receive messages or perform other operations on the deployed queues or themes.
                        </p>
                        <!-- <p>对于服务总线的“基本”和“标准”通知中心级别，我们保证至少在 99.9% 的时间里，正确配置的应用程序能够通过在基本或标准通知中心层上部署的通知中心，发送通知或执行注册管理操作。</p> -->
                        <p>
                            To learn more about the details of our Service Level Agreement, please visit the
                            <a href="/en-us/support/sla/messaging/index.html" id="pricing_messaging_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!--BEGIN: Support and service code chunk-->
                    <!--

                         <h2>Support &amp; SLA</h2>
                         <p>Azure 支持功能：</p>
                         <p>我们免费向用户提供以下支持服务：</p>
                         <table cellpadding="0" cellspacing="0" class="table-col6">
                             <tr>
                                 <th align="left">&nbsp;</th>
                                 <th align="left"><strong>是否支持</strong></th>
                             </tr>
                             <tr>
                                 <td>计费和订阅管理</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>服务仪表板</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>Web事件提交</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>中断/修复不受限制</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>电话支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                             <tr>
                                 <td>ICP备案支持</td>
                                 <td><i class="icon icon-tick"></i></td>
                             </tr>
                         </table>
                         <p>您可以<a href="/en-us/support/support-ticket-form/?l=en-us" id="vm-sla-sup">在线申请支持</a>，也可以通过服务热线联系我们。</p>
                         <h2>服务热线：</h2>
                         <ul>
                             <li>400-089-0365</li>
                             <li>010-84563652</li>
                         </ul>
                         <p>社区帮助：<a href="https://social.msdn.microsoft.com/Forums/en-us/home?forum=windowsazurezhchs" id="pricing_vm_help">访问MSDN</a></p>
                         <p>更多支持信息，请访问<a href="/en-us/support/plans/" id="stor-sla-info">Azure 支持计划</a></p>

                   -->
                    <!--END: Support and service code chunk-->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="pAphNFH2oInmtb46N01prQ3mWT0CGzkyzn40iDLdOdTSxvKHxBaAzcLvc3p2An9YWhKh2kre6bBV9CXrLROb951CgcLZDr5s4-VV--sKW8k1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
