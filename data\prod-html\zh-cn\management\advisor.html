

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0">
    <meta name="keywords" content="Azure 顾问价格" />
    <meta name="description" content="Azure 顾问是一个个性化的推荐引擎，为优化配置 Azure 资源提供最佳主动操作指南。" />

    <title>Azure 顾问价格详情_价格估算-Azure云计算</title>

    <link rel="apple-touch-icon" sizes="180x180" href="../../../Static/Favicon/apple-touch-icon.png">
    <link rel="shortcut icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="icon" href="../../../Static/Favicon/favicon.ico" type="image/x-icon">
    <link rel="manifest" href="../../../Static/Favicon/manifest.json">
    <link rel="mask-icon" href="../../../Static/Favicon/safari-pinned-tab.svg" color="#0078D4">
    <meta name="msapplication-config" content="/Static/Favicon/browserconfig.xml">
    <meta name="theme-color" content="#ffffff">
        <link rel="canonical" href="https://azure.microsoft.com/pricing/details/advisor/"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet">
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet">
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    
    <!-- END: Page Style -->

    <!-- BEGIN: Minified Page Style -->
    <link href='../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css' rel='stylesheet' />
    <!-- END: Minified Page Style -->
            
    <link rel="stylesheet" href="../../../StaticService/css/service.min.css" />
</head>
<body class="zh-cn">    
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    
    <style>
       @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
    </style>

<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage"></div>
</div>
    
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li><span></span></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                            <i class="circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select><option selected="selected">加载中...</option></select>
                        <span class="icon icon-arrow-top"></span>
                    </div>
                    
            <tags ms.service="advisor" ms.date="09/30/2015" wacn.date="11/27/2015"></tags>                      
            <!-- BEGIN: Product-Detail-TopBanner -->
            <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/images/service/product-page-banner-azure_advisor-01.png','imageHeight':'auto'}">
                <div class="common-banner-image">
                    <div class="common-banner-title">
                        <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 48 48" xml:space="preserve" width="48px" height="48px" style="float: left;margin-right: 10px;margin-top: 5px;">
                            <style type="text/css">
                                .advisor0{fill-rule:evenodd;clip-rule:evenodd;fill:#85C9D5;}
                                .advisor1{fill-rule:evenodd;clip-rule:evenodd;fill:#EDF0F4;}
                                .advisor2{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor3{fill-rule:evenodd;clip-rule:evenodd;fill:#B5C0CE;}
                                .advisor4{fill-rule:evenodd;clip-rule:evenodd;fill:#33B4DF;}
                                .advisor5{fill-rule:evenodd;clip-rule:evenodd;fill:#7B4D87;}
                                .advisor6{fill-rule:evenodd;clip-rule:evenodd;fill:#482255;}
                                .advisor7{fill-rule:evenodd;clip-rule:evenodd;fill:none;}
                                .advisor8{fill-rule:evenodd;clip-rule:evenodd;fill:#E3F3F6;}
                                .advisor9{fill-rule:evenodd;clip-rule:evenodd;fill:#A3BF3A;}
                                .advisor10{fill-rule:evenodd;clip-rule:evenodd;fill:#8BA63C;}
                                .advisor11{fill-rule:evenodd;clip-rule:evenodd;fill:#5E6D80;}
                                .advisor12{fill-rule:evenodd;clip-rule:evenodd;fill:#88909B;}
                                .advisor13{fill:#85C9D5;}
                                .advisor14{fill:#E56225;}
                                .advisor15{fill-rule:evenodd;clip-rule:evenodd;fill:#2F75BB;}
                                .advisor16{fill:#00A2D8;}
                                .advisor17{fill-rule:evenodd;clip-rule:evenodd;fill:#00A2D8;}
                                .advisor18{fill:#F79E1C;}
                                .advisor19{fill-rule:evenodd;clip-rule:evenodd;fill:#2ABAEC;}
                                .advisor20{fill:#F7B218;}
                                .advisor21{fill:#009480;}
                                .advisor22{fill-rule:evenodd;clip-rule:evenodd;fill:#009480;}
                                .advisor23{fill:#2ABAEC;}
                                .advisor24{fill:#323A45;}
                                .advisor25{fill:#88909B;}
                                .advisor26{fill:#A3BF3A;}
                                .advisor27{fill:#80A23E;}
                                .advisor28{fill:#7B4D87;}
                                .advisor29{fill:#2F75BB;}
                                .advisor30{opacity:0.15;fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor31{fill:#A5D9E2;}
                                .advisor32{fill-rule:evenodd;clip-rule:evenodd;fill:#323A45;}
                                .advisor33{fill:#FFFFFF;}
                                .advisor34{fill:#DAEFF3;}
                                .advisor35{fill:#2A65AF;}
                                .advisor36{fill:#F6ECF4;}
                                .advisor37{fill:none;}
                                .advisor38{fill-rule:evenodd;clip-rule:evenodd;fill:#F9BA19;}
                                .advisor39{fill:#F6931D;}
                                .advisor40{fill:#5E6D80;}
                                .advisor41{fill:#6B903D;}
                                .advisor42{fill-rule:evenodd;clip-rule:evenodd;}
                                .advisor43{fill:#00B8E2;}
                                .advisor44{fill:none;stroke:#85C9D5;stroke-width:0;stroke-linecap:round;stroke-linejoin:round;}
                                .advisor45{fill:#2A89CA;}
                                .advisor46{opacity:0.3;fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor47{opacity:0.3;fill:#FFFFFF;}
                                .advisor48{fill-rule:evenodd;clip-rule:evenodd;fill:#78B843;}
                                .advisor49{fill:#78B843;}
                                .advisor50{fill:#005BA3;}
                                .advisor51{fill:#9AD9E9;}
                                .advisor52{fill:#0F80B0;}
                                .advisor53{fill:#005F87;}
                                .advisor54{fill:#B7D332;}
                                .advisor55{fill:#7FB900;}
                                .advisor56{fill:#66C8E7;}
                                .advisor57{fill:#33B4DF;}
                                .advisor58{opacity:0.6;fill:#FFFFFF;}
                                .advisor59{opacity:0.4;fill:#FFFFFF;}
                                .advisor60{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor61{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor62{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor63{fill:#73BDCD;}
                                .advisor64{opacity:0.6;fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor65{fill-rule:evenodd;clip-rule:evenodd;fill:#80A23E;stroke:#FFFFFF;stroke-width:4;stroke-miterlimit:10;}
                                .advisor66{fill-rule:evenodd;clip-rule:evenodd;fill:#80A23E;}
                                .advisor67{clip-path:url(index.html);fill:none;}
                                .advisor68{fill-rule:evenodd;clip-rule:evenodd;fill:#F7B218;}
                                .advisor69{opacity:0.35;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor70{opacity:0.8;fill:#FFFFFF;}
                                .advisor71{opacity:0.35;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor72{fill-rule:evenodd;clip-rule:evenodd;fill:#DB5B26;}
                                .advisor73{fill-rule:evenodd;clip-rule:evenodd;fill:#F58B1F;}
                                .advisor74{opacity:0.35;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor75{fill-rule:evenodd;clip-rule:evenodd;fill:#404B59;}
                                .advisor76{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor77{fill-rule:evenodd;clip-rule:evenodd;fill:#D1B0D3;}
                                .advisor78{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor79{fill-rule:evenodd;clip-rule:evenodd;fill:#15B2E7;}
                                .advisor80{fill-rule:evenodd;clip-rule:evenodd;fill:#E1F3F8;}
                                .advisor81{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor82{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor83{opacity:0.3;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor84{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor85{fill-rule:evenodd;clip-rule:evenodd;fill:#7C8737;}
                                .advisor86{opacity:0.3;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor87{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor88{fill:#CCCBCB;}
                                .advisor89{opacity:0.1;fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor90{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor91{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor92{display:none;}
                                .advisor93{fill:#45B6F4;}
                                .advisor94{fill:#89919C;}
                                .advisor95{fill:#61ADD1;}
                                .advisor96{fill:#3899C6;}
                                .advisor97{fill:#F78F08;}
                                .advisor98{fill:#C1BFBC;}
                                .advisor99{fill:#B3B4B5;}
                                .advisor100{fill:#7AC3E1;}
                                .advisor101{opacity:0.12;fill:#333333;enable-background:new    ;}
                                .advisor102{fill:#7A7A7A;}
                                .advisor103{opacity:0.48;}
                                .advisor104{fill:#A0A1A2;}
                                .advisor105{fill:#A1DEF0;}
                                .advisor106{fill:#804998;}
                                .advisor107{fill:#0072C6;}
                                .advisor108{fill:#59B4D9;}
                                .advisor109{fill:#3999C6;}
                                .advisor110{fill:#B8D432;}
                                .advisor111{opacity:0.8;fill:#FFFFFF;enable-background:new    ;}
                                .advisor112{opacity:0.2;fill:#FFFFFF;enable-background:new    ;}
                                .advisor113{fill:#7FBA00;}
                                .advisor114{fill:#414141;}
                                .advisor115{fill:#0078D7;}
                                .advisor116{fill:#414142;}
                                .advisor117{fill:#D1B972;}
                                .advisor118{fill:#FEE087;}
                                .advisor119{fill:#B1CE2D;}
                                .advisor120{fill:#FCD116;}
                                .advisor121{fill:#719904;}
                                .advisor122{fill-rule:evenodd;clip-rule:evenodd;fill:#804898;}
                                .advisor123{fill-rule:evenodd;clip-rule:evenodd;fill:#6B2980;}
                                .advisor124{fill:#6B2980;}
                                .advisor125{opacity:0.3;fill:#FFFFFF;enable-background:new    ;}
                                .advisor126{opacity:0.2;fill:#1E1E1E;enable-background:new    ;}
                                .advisor127{fill:#33BCF0;}
                                .advisor128{opacity:0.2;fill:#636363;}
                                .advisor129{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor130{fill:#8B77B6;}
                                .advisor131{fill:#9B6CAC;}
                                .advisor132{fill:#874B94;}
                                .advisor133{opacity:0.4;fill:#7FBA00;enable-background:new    ;}
                                .advisor134{opacity:0.8;fill:#7FBA00;enable-background:new    ;}
                                .advisor135{opacity:0.6;fill:#7FBA00;enable-background:new    ;}
                                .advisor136{opacity:0.25;fill:#7FBA00;enable-background:new    ;}
                                .advisor137{fill:#DD5900;}
                                .advisor138{fill:#FF8C00;}
                                .advisor139{fill-rule:evenodd;clip-rule:evenodd;fill:#3E3E3E;}
                                .advisor140{fill:url(index.html);}
                                .advisor141{fill:#1870C5;}
                                .advisor142{fill:#72BFDF;}
                                .advisor143{fill:#3C3938;}
                                .advisor144{fill:#0072AC;}
                                .advisor145{fill:#0079B6;}
                                .advisor146{fill:#00A2D9;}
                                .advisor147{fill:#BFDC34;}
                                .advisor148{fill:#898989;}
                                .advisor149{fill:#EDF0F5;}
                                .advisor150{fill:#A3A4A7;}
                                .advisor151{fill:#353535;}
                                .advisor152{fill:#FF8C02;}
                                .advisor153{fill:#00AC8C;}
                                .advisor154{fill:#6EC2E9;}
                                .advisor155{fill:#442359;}
                                .advisor156{fill:#6DC0E6;}
                                .advisor157{fill:#432056;}
                                .advisor158{fill:#86CAD7;}
                                .advisor159{fill:#009E48;}
                                .advisor160{fill:#68217A;}
                                .advisor161{fill:#E5E5E5;}
                                .advisor162{opacity:0.15;fill:#FFFFFF;enable-background:new    ;}
                                .advisor163{fill:#3898C6;}
                                .advisor164{fill:#80BA42;}
                                .advisor165{fill:#B3D234;}
                                .advisor166{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor167{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor168{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor169{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor170{opacity:0.35;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor171{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor172{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor173{opacity:0.35;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor174{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#F1F1F2;}
                                .advisor175{fill-rule:evenodd;clip-rule:evenodd;fill:#008EBE;}
                                .advisor176{fill:#017F6F;}
                                .advisor177{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#F1F1F2;}
                                .advisor178{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor179{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor180{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor181{opacity:0.15;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor182{clip-path:url(index.html);fill:none;}
                                .advisor183{opacity:0.1;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor184{opacity:0.3;clip-path:url(index.html);fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
                                .advisor185{fill-rule:evenodd;clip-rule:evenodd;fill:#F9F9F9;}
                                .advisor186{opacity:0.1;fill-rule:evenodd;clip-rule:evenodd;fill:#F1F1F2;}
                                .advisor187{fill:#BAD80A;}
                                .advisor188{clip-path:url(index.html);}
                                .advisor189{fill:#00BCF2;}
                                .advisor190{clip-path:url(index.html);}
                                .advisor191{fill:#5AB4DA;}
                                .advisor192{fill:none;stroke:#5AB4DA;stroke-width:0.75;stroke-miterlimit:10;}
                                .advisor193{fill:#3898C5;}
                                .advisor194{fill:#7A7B7B;}
                                .advisor195{fill:#545555;}
                                .advisor196{fill:#D6EAF3;}
                                .advisor197{fill:#009580;}
                                .advisor198{fill:#BA141A;}
                                .advisor199{fill:#E27226;}
                                .advisor200{fill:#FF9D26;}
                                .advisor201{opacity:0.12;fill:#BA141A;}
                            </style>
                            <g>
                                <polygon class="advisor198" points="36,47 29.5,41 23,47 24,32 35,32     "/>
                                <path class="advisor100" d="M42,19c-0.1,0-0.2,0-0.3,0c0.2-1,0.3-2,0.3-3c0-8.3-6.7-15-15-15c-6.3,0-11.7,3.9-13.9,9.3
                                    c-0.8-0.2-1.7-0.3-2.6-0.3C4.7,10,0,14.7,0,20.5C0,26.1,4.4,30.7,10,31v0h32c3.3,0,6-2.7,6-6S45.3,19,42,19z"/>
                                <path class="advisor108" d="M48,25c0-3.3-2.7-6-6-6c-0.1,0-0.2,0-0.3,0c0.2-1,0.3-2,0.3-3c0-6.8-4.6-12.6-10.9-14.4
                                    c-4.1,2.2-7,6.5-7.1,11.5C23.5,13,23,13,22.5,13C16.7,13,12,17.7,12,23.5c0,2.9,1.2,5.6,3.2,7.5H42C45.3,31,48,28.3,48,25z"/>
                                <g>
                                    <circle class="advisor199" cx="30" cy="25" r="11"/>
                                    <circle class="advisor200" cx="30" cy="25" r="8.5"/>
                                </g>
                                <path class="advisor201" d="M37.8,17.2c4.3,4.3,4.3,11.3,0,15.6s-11.3,4.3-15.6,0"/>
                            </g>
                        </svg>
                        <h2>Azure 顾问<span style="color: #4d4d4d;font-family: '微软雅黑',regular;font-size: 20px;top: -6px;position: relative;display: inline;">（预览）</span><span>Azure Advisor</span></h2>
                        <h4>个性化 Azure 最佳实践推荐引擎</h4>
                    </div>
                </div>
            </div>
            <!-- END: Product-Detail-TopBanner -->
            <div class="pricing-page-section">
                
                <p>获取建议无需支付额外费用。</p>

            </div>

            <div class="pricing-page-section">
                <h2>支持和服务级别协议</h2>
                <p>如有任何疑问或需要帮助，请访问<a href="https://support.azure.cn/zh-cn/support/contact" id="advisor-contact-page">Azure 支持</a>选择自助服务或者其他任何方式联系我们获得支持。</p>
                <p>Azure 顾问功能预览期间，不提供服务级别协议。若要了解有关我们的服务器级别协议的详细信息，请访问<a href="../../../support/legal/sla/index.html" id="pricing_advisor_sla">服务级别协议</a>页。</p>
            </div>
              
            <!--BEGIN: Support and service code chunk-->
            
             
                                                                             
            <!--END: Support and service code chunk-->                                                                                                      
    
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->

<!-- BEGIN: Footer -->
<div class="public_footerpage"></div>
<!--END: Common sidebar-->


        
<link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet">
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="ir4pbelDNYbbgfSFeMMQLcnnBdJ06Ise6KXbFSLThzCVljBFhleIx3UxycxcwBj4UMIu2-HTvr4TgD4hRyS_roJ0SxQyXPlrM1s9qYzRRH01" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>

<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>

<!-- BEGIN: Minified RequireJs -->
<script src='../../../Static/Scripts/global.config.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/require.js' type='text/javascript'></script>
<script src='../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>
<!-- END: Minified RequireJs -->

<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js"></script>

<script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
</script>
<!-- end JSLL -->

<script type="text/javascript" src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../Static/Scripts/wacndatatracker.js"></script>


<script type="text/javascript" src="/common/useCommon.js"></script>

</body>
</html>