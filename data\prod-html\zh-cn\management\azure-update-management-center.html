<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport" />
    <meta content="Azure, 密钥保管库, 价格" name="keywords" />
    <meta
        content="了解 Azure 密钥保管库（Key Vault）价格详情。通过 Azure 密钥保管库（Key Vault），Azure 用户能够保护和控制云应用程序和服务使用的加密密钥和其他机密。1元试用即获1500元人民币的服务使用额度，也可直接购买成为 Azure 预付费客户，尽享最高 99.99% 的服务级别协议。"
        name="description" />
    <title>Azure 更新管理器</title>
    <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="../../../Static/Favicon/manifest.json" rel="manifest" />
    <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config" />
    <meta content="#ffffff" name="theme-color" />
    <link href="https://azure.microsoft.com/pricing/details/key-vault/" rel="canonical" />
    <!-- BEGIN: Azure UI Style -->
    <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet" />
    <link href="../../../Static/CSS/common.min.css" rel="stylesheet" />
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet" />
    <!-- END: Minified Page Style -->
    <link href="../../../StaticService/css/service.min.css" rel="stylesheet" />
</head>

<body class="zh-cn">
    <script>
        window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
    </script>
    <style>
        @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px;
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px;
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service {
            position: absolute;
            top: 0;
            width: 100%;
        }
    </style>
    <div class="acn-header-container">
        <div class="acn-header-placeholder">
        </div>
        <div class="public_headerpage">
        </div>
    </div>
    <!-- BEGIN: Documentation Content -->
    <div class="content">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="bread-crumb hidden-sm hidden-xs">
                        <ul>
                            <li>
                                <span>
                                </span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="single-page">
                <div class="row">
                    <div class="col-md-2">
                        <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                            <div class="loader">
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                                <i class="circle">
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-10 pure-content">
                        <div class="select left-navigation-select hidden-md hidden-lg">
                            <select>
                                <option selected="selected">
                                    加载中...
                                </option>
                            </select>
                            <span class="icon icon-arrow-top">
                            </span>
                        </div>
                        <tags ms.date="09/30/2015" ms.service="azure-update-management-center" wacn.date="11/27/2015">
                        </tags>
                        <!-- BEGIN: Product-Detail-TopBanner -->
                        <div class="common-banner col-top-banner"
                            data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/media/images/production/virtual_machine.png','imageHeight':'auto'}">
                            <div class="common-banner-image">
                                <div class="common-banner-title">
                                    <img src="/Images/02846-icon-service-Update-Management-Center.svg" />
                                    <h2>
                                        Azure 更新管理器
                                        <span>

                                        </span>
                                    </h2>
                                    <h4>
                                        Azure 更新管理器是一项服务，可帮助你跨 Azure、本地和其他云平台管理所有计算机(包括运行 Windows 和 Linux 的计算机)的更新。
                                    </h4>
                                </div>
                            </div>
                        </div>
                        <!-- END: Product-Detail-TopBanner -->
                        
                        <!-- BEGIN: TAB-CONTROL -->
                        <div class="technical-azure-selector tab-control-selector">
                            <div class="tab-control-container tab-active">
                                <h2>
                                    Azure 更新管理器
                                </h2>
                                <p>对于已启用 Arc 的服务器，Azure 更新管理器将根据 ￥1.03/服务器/天的价格每天按比例计费，此价格相当于 ￥31.8/服务器/月(假设连接使用 31 天)。由
                                    Azure Arc 启用的计算机只有在连接到 Arc 并由 Azure 更新管理器管理时才计费。
                                    <a href="https://learn.microsoft.com/zh-cn/azure/update-manager/update-manager-faq#when-is-an-arc-enabled-server-considered-managed-by-azure-update-manager"
                                        target="_blank">详细了解计算机何时由 Azure 更新管理器管理。</a>
                                </p>
                                <p>
                                    单击此处详细了解 <a target="_blank"
                                        href="https://learn.microsoft.com/zh-cn/azure/update-manager/update-manager-faq#pricing">Azure
                                        更新管理器常见问题解答 | Microsoft Learn</a>。</p>

                                <br /><br />
                                <table cellpadding="0" cellspacing="0" class="table-col6" width="100%">
                                    <tr>
                                        <th align="left">
                                            <strong>
                                                服务
                                            </strong>
                                        </th>
                                        <th align="left">
                                            <strong>
                                                Azure
                                            </strong>
                                        </th>
                                        <th align="left">
                                            <strong>
                                                已启用Arc
                                            </strong>
                                        </th>
                                    </tr>
                                    <tr>
                                        <td>
                                            Azure 更新管理器
                                        </td>
                                        <td>
                                            免费
                                        </td>
                                        <td>
                                            ￥31.8/服务器/月
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <!-- END: TAB-CONTROL -->
                        <div class="pricing-page-section">
                            <div class="more-detail">
                                <h2>
                                    常见问题
                                </h2>
                                <em>
                                    全部展开
                                </em>
                                <ul>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="key-vault-question-stored">
                                                如何为已启用 Arc 的服务器计算 Azure 更新管理器价格?
                                            </a>
                                            <section>
                                                <p>对于已启用 Arc 的服务器，Azure 更新管理器费用为 $31.8/服务器/月(假定连接使用时间为 31 天)。按每日按
                                                    ￥1.03/服务器/日的比例值收费。启用 Azure Arc 的计算机仅在连接到并由 Azure 更新管理器管理的情况下才收费。</p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="key-vault-question-stored2">
                                                启用 Arc 的服务器何时被视为由 Azure 更新管理器管理?
                                            </a>
                                            <section>
                                                <p>当机器满足以下条件时，Azure 更新管理器将认为 Arc 启用的服务器由其管理：</p>
                                                <ul><li>Arc 在一天中的任何时间的连接状态。</li><li>会触发更新操作(按需或通过计划作业进行修补、按需评估或定期评估)，或者其与计划相关联。</li></ul>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="key-vault-question-stored3">
                                                是否存在未为 Azure 更新管理器收取已启用 Arc 的服务器的费用的情况?
                                            </a>
                                            <section>
                                                <p>在以下情况下，不对使用 Azure 更新管理器管理的已启用 Arc 的服务器收费：</p>
                                                <ul><li>如果机器启用了 Azure Arc 启用的扩展安全更新 (ESU)。</li><li>托管启用 Arc 的服务器的订阅已启用 Microsoft Defender for Servers Plan 2。</li></ul>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="key-vault-question-stored4">
                                                如果从自动化更新管理迁移到更新管理器，是否会向我收费?
                                            </a>
                                            <section>
                                                <p>在以下情况下，不对使用 Azure 更新管理器管理的已启用 Arc 的服务器收费：</p>
                                                在 LA 代理停用之前，如果客户将已启用 Arc 的服务器加入到启用了自动更新管理解决方案的同一订阅，则不用支付费用(截至 2023 年 9 月 1 日)。
                                            </section>
                                        </div>
                                    </li>

                                    

                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="key-vault-question-operations">
                                                我是 Defender for Server 客户，使用 Azure
                                                更新管理器支持的更新建议，即“应在计算机上启用定期评估”和“应在计算机上安装系统更新”。是否会向我收取 Azure 更新管理器费用?
                                            </a>
                                            <section>
                                                <p>
                                                    如果已购买 Defender for Servers 计划 2，则无需支付费用即可修正上述两项建议的不正常资源。但是，如果为 Arc
                                                    计算机使用任何其他 Defender for Server 计划，则 Azure 更新管理器将在每日按
                                                    ￥1.03/服务器比例对这些计算机收费。</p>
                                            </section>
                                        </div>
                                    </li>
                                    <li>
                                        <i class="icon icon-plus">
                                        </i>
                                        <div>
                                            <a id="key-vault-question-billed">
                                                Azure 更新管理器在 Azure Stack HCI 上是否收费?
                                            </a>
                                            <section>
                                                <p>
                                                    对于已启用 Azure 权益和 Azure Arc VM 管理的 Azure Stack HCI 群集上托管的计算机，不会对 Azure
                                                    更新管理器收费。<a target="_blank"
                                                        href="https://learn.microsoft.com/zh-cn/azure-stack/hci/manage/azure-benefits?tabs=wac">了解详细信息</a>
                                                </p>
                                            </section>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- END: Documentation Content -->
    <!-- BEGIN: Footer -->
    <div class="public_footerpage">
    </div>
    <!--END: Common sidebar-->
    <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet" />
    <script type="text/javascript">
        function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="kklHe0NYvnJYbcCPtcRINnlCfDvuMcxYeuZxSdfevp8W1U4N2cFAdBrE3Eif3gO1YunsY6FaR1DPunQNmjF7Iw5ppI1xbBdUw6I73MDUuKE1" />';
            token = $(token).val();
            return token;
        }
        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
        }

        setLocaleCookie(window.currentLocale);
    </script>
    <script type="text/javascript">
        var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
    </script>
    <!-- BEGIN: Minified RequireJs -->
    <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/require.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
    </script>
    <!-- END: Minified RequireJs -->
    <!-- begin JSLL -->
    <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
    </script>
    <script type="text/javascript">
        (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
    </script>
    <!-- end JSLL -->
    <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
    </script>
    <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
    </script>
    <script src="/common/useCommon.js" type="text/javascript">
    </script>
</body>

</html>