<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Azure, 微软云, Azure 时序见解 - 价格详情, 定价, 计费" name="keywords"/>
  <meta content="学习关于 Azure 时序见解的价格。" name="description"/>
  <title>
   价格详情 - 时序见解 - Azure 云计算
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/time-series-insights/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
        window.currentLocale = "zh-CN";        
        window.headerTimestamp = "2019/1/23 8:25:24";
        window.footerTimestamp = "2019/1/8 8:07:06";
        window.locFileTimestamp = "2018/11/29 7:49:17";

        window.AcnHeaderFooter = {
            IsEnableQrCode: true,
            CurrentLang: window.currentLocale.toLowerCase(),
        };
  </script>
  <style>
   @media (min-width: 0) {
            .acn-header-placeholder {
                height: 48px; 
                width: 100%;
            }
        }

        @media (min-width: 980px) {
            .acn-header-placeholder {
                height: 89px; 
                width: 100%;
            }
        }

        .acn-header-placeholder {
            background-color: #1A1A1A;
        }

        .acn-header-service{
            position: absolute; 
            top: 0;
            width: 100%;
        }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <style type="text/css">
        .pricing-detail-tab .tab-nav{
                padding-left: 0!important;
                margin-top: 5px;
                margin-bottom: 0;
                overflow: hidden;
            }
            .pricing-detail-tab .tab-nav li {
                list-style: none;
                float: left;
            }

            .pricing-detail-tab .tab-nav li.active a {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-nav li.active a:hover {
                border-bottom: 4px solid #00a3d9;
            }

            .pricing-detail-tab .tab-content .tab-panel{
                display: none;
            }

            .pricing-detail-tab .tab-content .tab-panel.show-md{
                display: block;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a{
                padding-left: 5px;
                padding-right: 5px;
                color: #00a3d9;
                background-color: #FFF;
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a{
                color: #FFF;
                background-color: #00a3d9;              
            }

            .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover{
                color: #FFF;
                background-color: #00a3d9;              
            }
            .pure-content .technical-azure-selector .tags-date a,.pure-content .technical-azure-selector p a,.pure-content .technical-azure-selector table a{
            background: 0 0;padding: 0;margin: 0 6px;height: 21px;line-height: 22px;font-size: 14px;color: #00a3d9;float: none;display: inline;
            }
            li {
               list-style: none !important; 
               padding: 0;
               margin: 0;
            }
            .sum_title {
                overflow: hidden;
                background: #f4f5f6;
            }
            .sum_title > li {
                float: left;
                padding: 10px;
                
            }
       </style>
       <tags ms.date="09/30/2015" ms.service="time-series-insights" wacn.date="11/27/2015">
       </tags>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/time-series-insights.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/css/time-series-insights.svg"/>
          <h2>
           Azure 时序见解
           <span>
            Azure Time Series Insights
           </span>
          </h2>
          <h4>
           即时探索和分析 IoT 解决方案中的时序数据
          </h4>
         </div>
        </div>
       </div>
       <div>
        <ul class="sum_title">
         <li style="width: 40%;">
          <h2 style="font-weight: 500;font-size: 24px;">
           大规模监视、分析和直观呈现工业 IoT 数据
          </h2>
         </li>
         <li style="width: 60%;">
          <ul class="sum_ul">
           <li>
            Azure 时序见解第 2 代是一个开放且可扩展的端到端 IoT 分析服务，它具有出色的用户体验和丰富的 API，可将其强大的功能集成到现有工作流或应用程序中。
           </li>
           <li>
            你可以使用它来收集、处理、存储、查询和可视化物联网 (IoT) 规模的数据，这类数据高度情景化并针对时序进行了优化。
           </li>
           <li>
            Azure 时序见解第 2 代旨在用于即席数据浏览和操作分析，使你能够发现隐藏的趋势和异常、执行根本原因分析。它是一款开放且灵活的产品/服务，可满足行业 IoT 部署的各种需求。
           </li>
          </ul>
         </li>
        </ul>
       </div>
       <div class="pricing-page-section">
        <h2>
         定价详细信息
        </h2>
       </div>
       <!-- BEGIN: TAB-CONTROL -->
       <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
        <div class="tab-container-container">
         <div class="tab-container-box">
          <div class="tab-container">
           <div class="dropdown-container software-kind-container" style="display:none;">
            <label>
             OS/软件:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              Time Series Insights
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#tabContent1" href="javascript:void(0)" id="home_time-series-insights">
                Time Series Insights
               </a>
              </li>
             </ol>
            </div>
            <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
             <option data-href="#tabContent1" selected="selected" value="Time Series Insights">
              Time Series Insights
             </option>
            </select>
           </div>
           <div class="dropdown-container region-container">
            <label>
             地区:
            </label>
            <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
             <span class="selected-item">
              中国东部 2
             </span>
             <i class="icon">
             </i>
             <ol class="tab-items">
              <li class="active">
               <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                中国东部 2
               </a>
              </li>
              <li>
               <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                中国北部 2
               </a>
              </li>
              <!-- <li><a href="javascript:void(0)" data-href="#east-china" id="east-china" >中国东部</a></li>
								<li><a href="javascript:void(0)" data-href="#north-china" id="north-china" >中国北部</a></li> -->
             </ol>
            </div>
            <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
             <option data-href="#east-china2" selected="selected" value="east-china2">
              中国东部 2
             </option>
             <option data-href="#north-china2" value="north-china2">
              中国北部 2
             </option>
             <!-- <option data-href="#east-china" value="east-china">中国东部</option>
							<option data-href="#north-china" value="north-china">中国北部</option> -->
            </select>
           </div>
           <div class="clearfix">
           </div>
          </div>
         </div>
        </div>
        <!-- BEGIN: TAB-CONTAINER-1 -->
        <div class="tab-content">
         <!-- BEGIN: TAB-CONTAINER-3 -->
         <div class="tab-panel" id="tabContent1">
          <!-- BEGIN: Tab level 2 navigator 2 -->
          <!-- BEGIN: Tab level 2 content 3 -->
          <div class="category-container-container">
           <div class="category-container-box">
            <div class="category-container">
             <span class="category-title hidden-lg hidden-md">
              类别：
             </span>
             <ul class="os-tab-nav category-tabs hidden-xs hidden-sm">
              <li class="active">
               <a data-href="#tabContent1-1" href="javascript:void(0)" id="home_storage_gpvhy">
                Azure 时序见解第 2 代
               </a>
              </li>
              <li>
               <a data-href="#tabContent1-2" href="javascript:void(0)" id="home_storage_blobs">
                Azure 时序见解第 1 代
               </a>
              </li>
             </ul>
             <select class="dropdown-select category-tabs hidden-lg hidden-md">
              <option data-href="#tabContent1-1" id="home_storage_gpv2" value="General Purpose v2">
               Azure 时序见解第 2 代
              </option>
              <option data-href="#tabContent1-2" id="home_storage_blobs" value="Blob storage">
               Azure 时序见解第 1 代
              </option>
             </select>
            </div>
           </div>
          </div>
          <div class="tab-content">
           <div class="tab-panel" id="tabContent1-1">
            <table cellpadding="0" cellspacing="0" width="100%">
             <tr>
              <th align="left">
               <strong>
               </strong>
              </th>
              <th align="left">
               <strong>
                价格
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               数据处理单位
              </td>
              <td>
               ￥299.3 每单位每月（每个单位允许客户处理最多 100 GB/月）
              </td>
             </tr>
             <tr>
              <td>
               其他已处理的数据
              </td>
              <td>
               ￥2.035/GB
              </td>
             </tr>
             <tr>
              <td>
               元数据存储
              </td>
              <td>
               ￥0.407/MB
              </td>
             </tr>
            </table>
            <div class="tags-date">
             <div class="ms-date">
              *以下价格均为含税价格。
             </div>
             <br/>
            </div>
            <table cellpadding="0" cellspacing="0" id="time-series-insights-analytics" width="100%">
             <tr>
              <th align="left">
               <strong>
               </strong>
              </th>
              <th align="left">
               <strong>
                暖数据分析（用于临时数据分析）
               </strong>
              </th>
              <th align="left">
               <strong>
                原始数据分析（用于分析时间序列优化的数据）
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               存储
              </td>
              <td>
               ￥30.528/GB
              </td>
              <td>
               Blob 定价
               <sup>
                *
               </sup>
              </td>
             </tr>
             <tr>
              <td>
               查询
              </td>
              <td>
               免费（限于 10 次并发查询）
              </td>
              <td>
               ￥0.102/GB
              </td>
             </tr>
            </table>
            <div class="tags-date">
             <div class="ms-date">
              *原始数据存储在客户自己的存储帐户中，客户可以在时序见解设置期间进行配置。对于客户拥有的 Azure 存储帐户中存储的数据，客户将看到时序见解服务将要对其执行的数据读取和写入的额外 Blob 事务费用。
             </div>
             <br/>
            </div>
           </div>
           <div class="tab-panel" id="tabContent1-2">
            <div class="tags-date">
             <div class="ms-date">
              *以下价格均为含税价格。
             </div>
             <br/>
             <div class="ms-date">
              *每月价格估算基于每个月 31 天的使用量。
             </div>
            </div>
            <table cellpadding="0" cellspacing="0" id="time-series-insights-s1-s2" width="100%">
             <tr>
              <th align="left">
               <strong>
               </strong>
              </th>
              <th align="left">
               <strong>
                S1
               </strong>
              </th>
              <th align="left">
               <strong>
                S2
               </strong>
              </th>
             </tr>
             <tr>
              <td>
               每单位存储
               <sup>
                2
               </sup>
              </td>
              <td>
               30 GB 或 3000 万个事件
               <sup>
                1
               </sup>
              </td>
              <td>
               300 GB 或 30000 万个事件
               <sup>
                1
               </sup>
              </td>
             </tr>
             <tr>
              <td>
               每个单位的每日流入量
               <sup>
                2,3
               </sup>
              </td>
              <td>
               1 GB 或 100 万个事件
               <sup>
                1
               </sup>
              </td>
              <td>
               10 GB 或 1000 万个事件
               <sup>
                1
               </sup>
              </td>
             </tr>
             <tr>
              <td>
               最大保留期
               <sup>
                5,6
               </sup>
              </td>
              <td>
               13 个月
               <sup>
                5
               </sup>
              </td>
              <td>
               13 个月
               <sup>
                5
               </sup>
              </td>
             </tr>
             <tr>
              <td>
               查询
              </td>
              <td>
               无限制
              </td>
              <td>
               无限制
              </td>
             </tr>
             <tr>
              <td>
               最大单位数
               <sup>
                4
               </sup>
              </td>
              <td>
               10
              </td>
              <td>
               10
              </td>
             </tr>
             <tr>
              <td>
               价格（每单位/每月）
              </td>
              <td>
               ￥1526.4
              </td>
              <td>
               ￥13737.6
              </td>
             </tr>
            </table>
            <div class="tags-date">
             <div class="ms-date">
              <sup>
               1
              </sup>
              事件是带有时间戳的数据单位。出于计费目的，我们以 1 KB 的程序块来对事件进行计数。 例如，0.8 KB 的实际事件按一个事件计费，2.6 KB 的事件按三个事件计费。实际事件最大不超过 32 KB。
             </div>
             <br/>
             <div class="ms-date">
              <sup>
               2
              </sup>
              流入量和总存储量按事件数或数据大小进行衡量，以其中先达到限制者为准。
             </div>
             <br/>
             <div class="ms-date">
              <sup>
               3
              </sup>
              流入量按分钟计算。S1 的流入量至多为 720 个事件/分钟/单位，S2 的流入量至多为 7,200 个事件/分钟/单位。
             </div>
             <br/>
             <div class="ms-date">
              <sup>
               4
              </sup>
              通过添加更多单位，环境可扩展达 10 倍。
             </div>
             <br/>
             <div class="ms-date">
              <sup>
               5
              </sup>
              根据选定的保留天数或最大限制，在时序见解中保留数据。
             </div>
             <br/>
             <div class="ms-date">
              <sup>
               6
              </sup>
              可在 Azure 门户中配置保留期。最长保留期为滚动计算的 12 个月 + 1 个月，即 400 天。
             </div>
            </div>
           </div>
          </div>
         </div>
         <!-- END: TAB-CONTAINER-3 -->
        </div>
       </div>
       <!-- END: TAB-CONTROL -->
       <div class="pricing-page-section">
        <div class="more-detail">
         <h2>
          常见问题
         </h2>
         <em>
          全部展开
         </em>
         <ul>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="为什么 Azure 要引入新的业务模型？" id="home_time-series-insights_why_is_azure_introducing_a_new_business_model">
             为什么 Azure 要引入新的业务模型？
            </a>
            <section>
             <p>
              客户提供的反馈表明他们更倾向于能够相互独立地缩放数据处理、存储和查询的定价。在引入带有新的工业 IoT 见解功能的经过改进的时序见解服务时，我们将推出一种使定价与客户使用模式保持一致的新业务模式。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="目前市场内的 S1 和 S2 SKU 怎么办？" id="home_time-series-insights_What_happens_to_the_current_in-market_S1_and_S2_SKUs?">
             目前市场内的 S1 和 S2 SKU 怎么办？
            </a>
            <section>
             <p>
              Azure 将继续为使用 S1 和 S2 SKU 的客户提供支持。但是，我们鼓励客户转向新的业务模型，因为它提供了诸多好处 - 新的工业 IoT 见解功能价格点更低、解决方案总成本更少、可以灵活地按使用情况付费，还可以通过改变数据存储和查询模式来控制成本。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="新的时序见解 SKU 中包括什么？" id="home_time-series-insights_What_is_included_in_the_new_Time_Series_Insights_SKU?">
             新的时序见解 SKU 中包括什么？
            </a>
            <section>
             <p>
              新的 SKU 引入了新的时间序列优化冷路径分析，同时继续支持暖路径临时数据探索功能。该服务在公开预览版发布时将首先支持冷路径分析方案。该服务将在未来几个月内更新，并折叠在市场内的暖路径中，临时分析能够根据业务需要将时间序列数据路由到一个或两个存储层。
             </p>
             <p>
              设置时间序列环境的用户体验暂时将包括当前的 S1/S2 SKU 以及更新的新 SKU。我们预计随着暖临时分析功能折叠入新的 SKU，我们会逐渐鼓励客户从 S1/S2 SKU 转移到时序见解产品/服务单一、统一的 SKU 和功能中去，利用更新的定价权益以及更全面的端到端解决方案的功能。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="时序见解 S1 SKU 中包括什么？" id="home_time-series-insights_Whats_included_in_the_Time_Series_Insights_S1_SKU?">
             时序见解 S1 SKU 中包括什么？
            </a>
            <section>
             <p>
              每个 S1 单位包括最多 30 GB 的总存储或 3 千万个事件（以先达到者为准），最大每日流入量和可配置保留期也是如此。还提供时序见解可视化体验。用户数和可执行的查询数不受限制。
             </p>
             <p>
              例如，如果预配 1 个 S1 单位，可将环境配置为在至多 400 天的保留期内保留数据。如果保留期配置为 400 天，则每天的流入量不得超过所分配环境（对于 1 个 S1 单位，即为 30 GB）的 1/400。在本例中，每天可用的总存储容量为其 1/400，即 0.075 GB 或 75 MB。
             </p>
             <p>
              另举一例，如果将环境配置为在 180 天内保留 1 个 S1 单位的数据，则每天的流入量不能超过 0.166 GB (166 MB)。如果每天的流入量超过 0.166 GB，则数据保留不足 180 天。例如，如果日均流入量为 0.189 GB，则只能实现 158 天的保留（30 GB/0.189 = 158.73 天的保留期）。
             </p>
             <p>
              如果该希望将每日流入量增至最大，则每天可流入 1 GB，将该数据保留至多 30 天。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="时序见解 S2 SKU 中包括什么？" id="home_time-series-insights_Whats_included_in_the_Time_Series_Insights_S2_SKU?">
             时序见解 S2 SKU 中包括什么？
            </a>
            <section>
             <p>
              每个 S2 单位包括最多 300 GB 总存储或 3 亿个事件（以先达到者为准）的可配置的每日流入量和保留期。还提供时序见解可视化体验。用户数和可执行的查询数不受限制。
             </p>
             <p>
              例如，如果预配 1 个 S2 单位，则将环境配置为保留 400 天，且日均流入量小于或等于 300 GB 的 1/400（0.75 GB 或 750 MB）时，可实现 400 天的保留。
             </p>
             <p>
              另举一例，如果将环境配置为在 180 天内保留 1 个 S2 单位的数据，则只要每天的流入量不超过 1.66 GB (1,660 MB)，就可实现 180 天的保留。如果每天的流入量超过 1.66 GB，则数据保留不足 180 天。例如，如果日均流入量为 1.89 GB，则只能实现 158 天的保留（300 GB/1.89 = 158.73 天的保留期）。
             </p>
             <p>
              如果该希望将每日流入量增至最大，则每天可流入 10 GB，将该数据保留至多 30 天。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="如果想要使用 S1 或 S2 SKU 发送大于 1KB 的事件会怎样？" id="home_time-series-insights_What_happens_if_I_want_to_send_events_larger_than_1_KB_using_the_S1_or_S2_SKU?">
             如果想要使用 S1 或 S2 SKU 发送大于 1KB 的事件会怎样？
            </a>
            <section>
             <p>
              事件是至多 1KB 的数据单位。较大事件以 1KB 的倍数计数。例如，0.8 KB 按一个事件计费，而 2.6 KB 事件则按三个事件计费。大于上限 32 KB 的事件将被拒绝。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="如何增加或减少购买的单位量？" id="home_time-series-insights_How_can_I_scale_up_or_scale_down_the_number_of_units_I_have_purchased?">
             如何增加或减少购买的单位量？
            </a>
            <section>
             <p>
              可以通过
              <a href="https://portal.azure.cn/">
               Azure 门户
              </a>
              更改单位量。
             </p>
            </section>
           </div>
          </li>
          <li>
           <i class="icon icon-plus">
           </i>
           <div>
            <a aria-label="S1 和 S2 SKU 如何计费？" id="home_time-series-insights_How_does_billing_work_for_the_S1_and_S2_SKUs?">
             S1 和 S2 SKU 如何计费？
            </a>
            <section>
             <p>
              将每天按比例进行计费。为方便计费，一天始于午夜 (UTC)。帐单按月生成。例如，如果客户 5 天内总共使用 3 个单位的时序见解 S，则在本月底按 3 个单位 × 5 天 × (￥1526.4/31) = ￥738.581向客户收费。
             </p>
             <p>
              同样，如果客户 5 天内总共使用 3 个单位的时序见解 S2，则在本月底按 3 个单位 × 5 天 × (￥13737.6/31) = ￥6647.226 向客户收费。
             </p>
            </section>
           </div>
          </li>
         </ul>
        </div>
       </div>
       <div class="pricing-page-section">
        <h2>
         支持和服务级别协议
        </h2>
        <p>
         如有任何疑问或需要帮助，请访问
         <a href="https://support.azure.cn/zh-cn/support/contact" id="storage-contact-page">
          Azure 支持
         </a>
         选择自助服务或者其他任何方式联系我们获得支持。
        </p>
        <p>
         若要了解有关我们的服务器级别协议的详细信息，请访问
         <a href="../../../support/sla/time-series-insights/index.html" id="pricing_storage_sla">
          服务级别协议
         </a>
         页。
        </p>
       </div>
       <!--BEGIN: Support and service code chunk-->
       <!--
-->
       <!--END: Support and service code chunk-->
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="AQYTKJ_AcWsEjeKPbkgJZFrrWQY91F1GVV24CuvyMgW4AL6iLI89lHWo2l1-a63jrISW4flqZQLBlxT7WBDowz0YHeaprbOs3I58d48kZOg1" />';
        token = $(token).val();
        return token;
    }
    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
  </script>
  <!-- end JSLL -->
  <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
