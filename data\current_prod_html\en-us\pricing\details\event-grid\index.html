<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8"/>
    <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
    <meta content="event handler and event handling, publish subscribe and publish subscribe model, reactive programming and event-based programming" name="keywords"/>
    <meta content="Explore pricing for Azure Event Grid, a fully managed publish-subscribe event handler. Billing based on operations with first 100,000 operations/month free."
          name="description"/>
    <title>
        Pricing – Event Grid - Azure Cloud Computing
    </title>
    <link href="/Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
    <link href="/Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
    <link href="/Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
    <link href="/Static/Favicon/manifest.json" rel="manifest"/>
    <link color="#0078D4" href="/Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
    <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
    <meta content="#ffffff" name="theme-color"/>
    <link href="https://azure.microsoft.com/pricing/details/event-grid/" rel="canonical"/>
    <!-- BEGIN: Azure UI Style -->
    <link href="/Static/CSS/azureui.min.css" rel="stylesheet"/>
    <link href="/Static/CSS/common.min.css" rel="stylesheet"/>
    <!-- END: Azure UI Style -->
    <!-- BEGIN: Page Style -->
    <!-- END: Page Style -->
    <!-- BEGIN: Minified Page Style -->
    <link href="/Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
    <!-- END: Minified Page Style -->
    <link href="/StaticService/css/service.min.css" rel="stylesheet"/>
</head>
<body class="en-us">
<script>
    window.requireUrlArgs = "1/6/2020 11:41:53 AM";
    window.currentLocale = "en-US";
    window.headerTimestamp = "5/9/2019 9:29:29 AM";
    window.footerTimestamp = "5/9/2019 9:29:29 AM";
    window.locFileTimestamp = "5/9/2019 9:29:21 AM";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
</script>
<style>
    @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
</style>
<div class="acn-header-container">
    <div class="acn-header-placeholder">
    </div>
    <div class="public_headerpage">
    </div>
</div>
<!-- BEGIN: Documentation Content -->
<div class="content">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="bread-crumb hidden-sm hidden-xs">
                    <ul>
                        <li>
         <span>
         </span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="single-page">
            <div class="row">
                <div class="col-md-2">
                    <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
                        <div class="loader">
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                            <i class="circle">
                            </i>
                        </div>
                    </div>
                </div>
                <div class="col-md-10 pure-content">
                    <div class="select left-navigation-select hidden-md hidden-lg">
                        <select>
                            <option selected="selected">
                                加载中...
                            </option>
                        </select>
                        <span class="icon icon-arrow-top">
        </span>
                    </div>
                    <tags ms.date="09/30/2015" ms.service="en-us-event-grid" wacn.date="11/27/2015">
                    </tags>
                    <!-- BEGIN: Product-Detail-TopBanner -->
                    <div class="common-banner col-top-banner"
                         data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/event-grid_banner.png','imageHeight':'auto'}">
                        <div class="common-banner-image">
                            <div class="common-banner-title">
                                <img src="/Images/marketing-resource/css/<EMAIL>"/>
                                <h2>
                                    Event Grid
                                    <span>
            Event Grid
           </span>
                                </h2>
                                <h4>
                                    Get reliable event delivery at massive scale
                                </h4>
                            </div>
                        </div>
                    </div>
                    <!-- END: Product-Detail-TopBanner -->
                    <div class="pricing-page-section">
                        <p>
                            Event Grid is a fully managed event routing service that provides reliable message delivery at massive scale.
                            Developers use Event Grid to build reactive, event-driven apps in a modern, serverless compute
                            architecture—eliminating polling and its associated cost and latency. One-to-many mapping, in which a single event
                            can trigger multiple actions, provides a uniform event consumption experience. A pay-per-operation model complements
                            and extends your serverless solutions and lets you focus on innovation rather than infrastructure.
                        </p>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Event Grid consumption pricing
                        </h2>
                        <p>
                            Event Grid is priced as pay-per-use based on operations performed. Operations include ingress of events to Domains or
                            Topics, advanced matches, delivery attempts, and management calls. Plan pricing includes a monthly free grant of
                            100,000 operations.
                        </p>
                        <table cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
                            <tr>
                                <th align="left">
                                    Price per million operations
                                </th>
                                <th align="left">
                                    Free usage per month
                                </th>
                            </tr>
                            <tr>
                                <td align="left">
                                    ￥ 0.611
                                </td>
                                <td align="left">
                                    100,000 operations
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <h2>
                            Event Grid pricing example 1
                        </h2>
                        <p>
                            <a href="/en-us/home/<USER>/azure-functions">
                                Azure Functions
                            </a>
                            is connected to
                            <a href="/en-us/home/<USER>/storage/blobs">
                                Blob Storage
                            </a>
                            through Event Grid, to process images each time a new image is added. In the blob storage container 5
                            million images are created—each one triggering the Function through Event Grid.
                        </p>
                        <ul>
                            <li>
                                You publish 5 million events to Event Grid in a month.
                            </li>
                            <li>
                                All events are published to 1 https endpoint.
                            </li>
                        </ul>
                        <table cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
                            <tr>
                                <th align="left">
                                    <strong>
                                        NUMBER OF OPERATIONS
                                    </strong>
                                </th>
                                <th align="left">
                                </th>
                            </tr>
                            <tr>
                                <td align="left">
                                    Published events
                                </td>
                                <td align="left">
                                    5 million operations
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    Delivery attempts
                                </td>
                                <td align="left">
                                    5 million operations
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    Monthly free grant
                                </td>
                                <td align="left">
                                    - 100,000 operations
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    Total operations
                                    <br/>
                                    Price per million operations
                                </td>
                                <td align="left">
                                    9.9 million
                                    <br/>
                                    ￥ 0.611
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    <strong>
                                        Total monthly cost
                                    </strong>
                                </td>
                                <td align="left">
                                    ￥ 6.0489
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <h2>
                            Event Grid pricing example 2
                        </h2>
                        <p>
                            Incoming logs to Event Hubs are being sent to storage through
                            <a href="/en-us/home/<USER>/event-hubs.html">
                                Event Hubs
                            </a>
                            Capture. Five million log batch events are pushed by Event Grid to
                            <a href="/en-us/home/<USER>/logic-apps.html">
                                Logic Apps
                            </a>
                            for monitoring. All events are also pushed to one of several custom-monitoring endpoints based on the event type, and in some cases the origin
                            of the event. One million of these events require advanced matching. One of the endpoints experiences outages and retries are necessary to
                            successfully deliver the events.
                        </p>
                        <ul>
                            <li>
                                You publish 5 million events to Event Grid in a month.
                            </li>
                            <li>
                                All events are published to 2 https endpoints.
                            </li>
                            <li>
                                1 million of the events require advanced matching.
                            </li>
                            <li>
                                1 million of the events required 2 delivery attempts.
                            </li>
                        </ul>
                        <table cellpadding="0" cellspacing="0" width="100%">
                            <tbody>
                            <tr>
                                <th align="left">
                                    <strong>
                                        NUMBER OF OPERATIONS
                                    </strong>
                                </th>
                                <th align="left">
                                </th>
                            </tr>
                            <tr>
                                <td align="left">
                                    Published events
                                </td>
                                <td align="left">
                                    5 million operations
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    Delivery attempts
                                </td>
                                <td align="left">
                                    11 million operations (1 million for second delivery attempt)
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    Advanced match
                                </td>
                                <td align="left">
                                    1 million operations
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    Monthly free grant
                                </td>
                                <td align="left">
                                    - 100,000 operations
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    Total operations
                                    <br/>
                                    Price per million operations
                                </td>
                                <td align="left">
                                    16.9 million
                                    <br/>
                                    ￥ 0.611
                                </td>
                            </tr>
                            <tr>
                                <td align="left">
                                    <strong>
                                        Total monthly cost
                                    </strong>
                                </td>
                                <td align="left">
                                    ￥ 10.3259
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="pricing-page-section">
                        <div class="more-detail">
                            <h2>
                                FAQ
                            </h2>
                            <em>
                                Expand All
                            </em>
                            <h3>
                                Overview
                            </h3>
                            <ul>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="What are operations and how are they billed?" id="home_event-grid_faq_what-are-operations-and-how-are-they-billed">
                                            What are operations and how are they billed?
                                        </a>
                                        <section>
                                            <p>
                                                Operations in Event Grid include all ingress events, advanced match, delivery attempt, and management
                                                calls. You’re charged per million operations with the first 100,000 operations free each month.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="How many Event Subscriptions can I have for each account?"
                                           id="home_event-grid_faq_how-many-Event-Subscriptions-can-I-have-for-each-account">
                                            How many Event Subscriptions can I
                                            have for each account?
                                        </a>
                                        <section>
                                            <p>
                                                By default, you can have up to 500 Event Subscriptions per topic as per the Event Grid limits
                                                documentation.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="How many custom Topics can I have for each account?"
                                           id="home_event-grid_faq_how-many-custom-Topics-can-I-have-for-each-account">
                                            How many custom Topics can I have for each
                                            account?
                                        </a>
                                        <section>
                                            <p>
                                                The default limit for custom topics is 100 per Azure Subscription
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="How many Event Grid subscriptions can I have for each account?"
                                           id="home_event-grid_faq_how-many-Event-Grid-subscriptions-can-I-have-for-each-account">
                                            How many Event Grid
                                            subscriptions can I have for each account?
                                        </a>
                                        <section>
                                            <p>
                                                You can have up to 1,000 Event Grid subscriptions during the preview period.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="Which endpoints are supported in Event Grid?" id="home_event-grid_faq_which-endpoints-are-supported-in-Event-Grid">
                                            Which endpoints are supported in Event Grid?
                                        </a>
                                        <section>
                                            <p>
                                                Currently, we support https endpoints. Other endpoints will be supported later.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="Is there a limit on delivery attempts?" id="home_event-grid_faq_Is-there-a-limit-on-delivery-attempts">
                                            Is there a limit on delivery attempts?
                                        </a>
                                        <section>
                                            <p>
                                                Event Grid will perform an exponential back off for all deliveries. If your WebHook does not return a
                                                2xx, the retry begins immediately. When the back off interval reaches one hour, retries will be
                                                performed every hour. After 24 hours, the service will stop attempting to deliver events.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="What is the maximum retention period?" id="home_event-grid_faq_What-is-the-maximum-retention-period">
                                            What is the maximum retention period?
                                        </a>
                                        <section>
                                            <p>
                                                Event Grid will buffer data for a maximum of 24 hours. At the end of this time, events will be deleted.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="How is Event Grid event size calculated and charged?"
                                           id="home_event-grid_faq_How-is-Event-Grid-event-size-calculated-and-charged">
                                            How is Event Grid event size calculated
                                            and charged?
                                        </a>
                                        <section>
                                            <p>
                                                Each 64KB chunk of delivered data is billed as 1 request. For example, a single event of 256KB will be
                                                billed as 4 events.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                                <li>
                                    <i class="icon icon-plus">
                                    </i>
                                    <div>
                                        <a aria-label="Is there a separate charge for using Event Domains?"
                                           id="home_event-grid_faq_Is-there-a-separate-charge-for-using-Event-Domains">
                                            Is there a separate charge for using Event
                                            Domains?
                                        </a>
                                        <section>
                                            <p>
                                                No, Domains and Topics are treated the same way from a billing perspective. Ingress of an event to Azure
                                                Event Grid system is one operation regardless of entry point.
                                            </p>
                                        </section>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="pricing-page-section">
                        <h2>
                            Support &amp; SLA
                        </h2>
                        <p>
                            If you have any questions or need help, please visit
                            <a aria-label="event-grid-contact-page" href="https://support.azure.cn/en-us/support/contact" id="event-grid-contact-page" target="_blank">
                                Azure Support
                            </a>
                            and select self-help service or any other method to contact us for support.
                        </p>
                        <p>
                            To learn more about the details of our Service Level Agreement, please visit the
                            <a aria-label="pricing_event-grid_sla" href="/en-us/support/sla/event-grid/" id="pricing_event-grid_sla">
                                Service Level Agreements
                            </a>
                            page.
                        </p>
                    </div>
                    <!-- BEGIN: TAB-CONTROL -->
                    <!-- END: TAB-CONTROL -->
                    <!--BEGIN: Support and service code chunk-->
                    <!--END: Support and service code chunk-->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: Documentation Content -->
<!-- BEGIN: Footer -->
<div class="public_footerpage">
</div>
<!--END: Common sidebar-->
<link href="/Static/CSS/Localization/en-us.css" rel="stylesheet"/>
<script type="text/javascript">
    function getAntiForgeryToken() {
        var token = '<input name="__RequestVerificationToken" type="hidden" value="ssXqYw_EwSjxtMqN_SnkhRrzK-AL8jiTpXR4TfIByijh_RvVS1OWaOAwew0q8FKzJYL5kvLBm1_T8UmD2WX1BngCjKa2ZrCVlWSop1wbf4Q1" />';
        token = $(token).val();
        return token;
    }

    function setLocaleCookie(localeVal) {
        var Days = 365 * 10; // Ten year expiration
        var exp = new Date();

        var hostName = window.location.hostname;
        var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

        exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
        document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
    }

    setLocaleCookie(window.currentLocale);
</script>
<script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
    var TECHNICAL_STORAGE = '/blob/tech-content/';
    var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
    var EnablePricingSync = 'false';
</script>
<!-- BEGIN: Minified RequireJs -->
<script src="/Static/Scripts/global.config.js" type="text/javascript">
</script>
<script src="/Static/Scripts/require.js" type="text/javascript">
</script>
<script src="/Static/Scripts/ef3815ab64d6cfe32680fc9c60373db97e92ccc1.js" type="text/javascript">
</script>
<!-- END: Minified RequireJs -->
<!-- begin JSLL -->
<script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
</script>
<script type="text/javascript">
    (function () {
        var config = {
            cookiesToCollect: ["_mkto_trk"],
            syncMuid: true,
            ix: {
                a: true,
                g: true
            },
            coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
            }
        };
        awa.init(config);
    })();
</script>
<!-- end JSLL -->
<script src="/Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
</script>
<script src="/Static/Scripts/wacndatatracker.js" type="text/javascript">
</script>
<script src="/common/useCommon.js" type="text/javascript">
</script>
</body>
</html>
