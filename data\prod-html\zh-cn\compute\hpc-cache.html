<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="WebSocket, real-time messaging, publish-subscribe, web applications, web publishing" name="keywords"/>
  <meta content="用于高性能计算 (HPC) 的文件缓存" name="description"/>
  <title>
   Azure HPC缓存
  </title>
  <link href="../../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/service-fabric/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
      IsEnableQrCode: true,
      CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
   @media (min-width: 0) {
      .acn-header-placeholder {
        height: 48px;
        width: 100%;
      }
    }

    @media (min-width: 980px) {
      .acn-header-placeholder {
        height: 89px;
        width: 100%;
      }
    }

    .acn-header-placeholder {
      background-color: #1A1A1A;
    }

    .acn-header-service {
      position: absolute;
      top: 0;
      width: 100%;
    }

    .remove_list_style {
      list-style: none;
      overflow: hidden;
      background-color: #f4f5f6;
    }

    .title_list {
      display: inline-block;
      float: left;
      width: 50%;
    }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <div class="hide-info" style="display:none;">
   <div class="bg-box">
    <div class="cover-bg">
    </div>
   </div>
   <div class="msg-box">
    <div class="pricing-unavailable-message">
     所选区域不可用
    </div>
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="08/19/2022" ms.service="hpc-cache" wacn.date="08/19/2022">
       </tags>
       <!-- BEGIN: Product-Detail-TopBanner -->
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/resources/self-serve/hpc-cache.svg"/>
          <h2>
           Azure HPC缓存定价
           <span>
            Azure HPC缓存 pricing
           </span>
          </h2>
         </div>
        </div>
       </div>
       <!-- END: Product-Detail-TopBanner -->
       <div class="pricing-page-section">
        <ul class="remove_list_style">
         <li class="title_list">
          <h2>
           用于高性能计算 (HPC) 的文件缓存
          </h2>
         </li>
         <li class="title_list">
          Azure HPC 缓存是一项 Azure 服务，它提供低延迟的文件访问，以支持在 Azure 中运行的高性能计算 (HPC) 工作负荷。在 Azure 计算中转移现有的 HPC 管道，同时使用 Azure HPC 缓存在 Azure Blob 或本地网络连接存储之间进行文件缓存。
         </li>
        </ul>
        <h2>
         定价详细信息
        </h2>
        <!-- BEGIN: TAB-CONTROL -->
        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
         <div class="tab-container-container">
          <div class="tab-container-box">
           <div class="tab-container">
            <!-- <div class="dropdown-container software-kind-container" style="display:none;">
                        <label>OS/软件:</label>
                        <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
                          <span class="selected-item">Azure 防火墙</span>
                          <i class="icon"></i>
                          <ol class="tab-items">
                            <li class="active"><a href="javascript:void(0)" data-href="#tabContent1"
                                id="home_azure_firewall">Azure 防火墙</a></li>
                          </ol>
                        </div>
                        <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
                          <option selected="selected" data-href="#tabContent1" value="metrics-advisor">Azure 防火墙
                          </option>
                        </select>
                      </div> -->
            <div class="dropdown-container region-container">
             <label>
              地区:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               中国北部 3
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
               <!-- id 对应 soft-category 的 region -->
               <li class="active">
                <a data-href="#north-china3" href="javascript:void(0)" id="north-china3">
                 中国北部 3
                </a>
               </li>
              </ol>
             </div>
             <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
              <option data-href="#north-china" selected="selected" value="north-china3">
               中国北部 3
              </option>
             </select>
            </div>
            <div class="clearfix">
            </div>
           </div>
          </div>
         </div>
         <!-- BEGIN: TAB-CONTAINER-1 -->
         <div class="tab-content">
          <!-- BEGIN: TAB-CONTENT-1 -->
          <div class="tab-panel" id="tabContent1">
           <!-- BEGIN: Tab level 2 navigator 2 -->
           <!-- BEGIN: Tab level 2 content 3 -->
           <ul class="tab-nav" style="display:none">
           </ul>
           <div class="tab-content">
            <!-- BEGIN: TAB-CONTAINER-1 -->
            <div class="tab-panel" id="tabContent1">
             <!-- BEGIN: Table1-Content-->
             <section aria-label="定价详细信息" class="section section-size3" data-exception="pricing-tables" id="pricing">
              <div class="row column">
               <h2 class="text-heading3">
                读写缓存​
               </h2>
               <table cellpadding="0" cellspacing="0" width="100%">
                <thead>
                 <tr>
                  <th align="left">
                   实例
                  </th>
                  <th align="left">
                   价格
                  </th>
                 </tr>
                </thead>
                <tbody>
                 <tr>
                  <td>
                   缓存磁盘（GB/小时）
                  </td>
                  <td>
                   0.002646
                  </td>
                 </tr>
                 <tr>
                  <td>
                   吞吐量（每小时 2 GB/s）
                  </td>
                  <td>
                   52.4512
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
              <div class="row column">
               <h2 class="text-heading3">
                只读缓存
               </h2>
               <table cellpadding="0" cellspacing="0" width="100%">
                <thead>
                 <tr>
                  <th align="left">
                   实例
                  </th>
                  <th align="left">
                   价格
                  </th>
                 </tr>
                </thead>
                <tbody>
                 <tr>
                  <td>
                   吞吐量（每小时 4.5 GB/s）
                  </td>
                  <td>
                   65.1264
                  </td>
                 </tr>
                </tbody>
               </table>
              </div>
             </section>
            </div>
           </div>
          </div>
         </div>
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
  </div>
  <!-- END: Documentation Content -->
  <!-- BEGIN: Footer -->
  <div class="public_footerpage">
  </div>
  <!--END: Common sidebar-->
  <link href="../../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
  <script type="text/javascript">
   function getAntiForgeryToken() {
            var token = '<input name="__RequestVerificationToken" type="hidden" value="cO-ztjsaj4AipEd8qChMMWP4d1I0Zf9H9N-JVkDy1ZYltjKc0raP1R4b6ffJjWfcuFPkijs_T5tHY8B40px7VeRBQuNX3FsUo29Oy9k63dM1" />';
            token = $(token).val();
            return token;
          }
          function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp.toGMTString();
          }

          setLocaleCookie(window.currentLocale);
  </script>
  <script type="text/javascript">
   var MARKETING_STORAGE = '/blob/marketing-resource/Content';
          var TECHNICAL_STORAGE = '/blob/tech-content/';
          var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
          var EnablePricingSync = 'false';
  </script>
  <!-- BEGIN: Minified RequireJs -->
  <script src="../../../../Static/Scripts/global.config.js" type="text/javascript">
  </script>
  <script src="../../../../Static/Scripts/require.js" type="text/javascript">
  </script>
  <script src="../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js" type="text/javascript">
  </script>
  <!-- END: Minified RequireJs -->
  <!-- begin JSLL -->
  <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
  </script>
  <script type="text/javascript">
   (function () {
            var config = {
              cookiesToCollect: ["_mkto_trk"],
              syncMuid: true,
              ix: {
                a: true,
                g: true
              },
              coreData: {
                appId: 'AzureCN',
                market: 'zh-cn',
              }
            };
            awa.init(config);
          })();
  </script>
  <!-- end JSLL -->
  <script src="../../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
  </script>
  <script src="../../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
  </script>
  <script src="/common/useCommon.js" type="text/javascript">
  </script>
 </body>
</html>
