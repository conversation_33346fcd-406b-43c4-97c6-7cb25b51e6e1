<!DOCTYPE html>
<html lang="zh-CN">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=2.0" name="viewport"/>
  <meta content="Access Windows 10 Enterprise and Windows 7 Enterprise desktops and apps at no additional cost if you have an eligible Windows or Microsoft 365 license." name="description"/>
  <meta content="Access Windows 10 Enterprise and Windows 7 Enterprise desktops and apps at no additional cost if you have an eligible Windows or Microsoft 365 license." name="twitter:description" property="twitter:description"/>
  <title>
   Azure 虚拟桌面定价
  </title>
  <link href="../../../Static/Favicon/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="shortcut icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/favicon.ico" rel="icon" type="image/x-icon"/>
  <link href="../../../Static/Favicon/manifest.json" rel="manifest"/>
  <link color="#0078D4" href="../../../Static/Favicon/safari-pinned-tab.svg" rel="mask-icon"/>
  <meta content="/Static/Favicon/browserconfig.xml" name="msapplication-config"/>
  <meta content="#ffffff" name="theme-color"/>
  <link href="https://azure.microsoft.com/pricing/details/storage/managed-disks/" rel="canonical"/>
  <!-- BEGIN: Azure UI Style -->
  <link href="../../../Static/CSS/azureui.min.css" rel="stylesheet"/>
  <link href="../../../Static/CSS/common.min.css" rel="stylesheet"/>
  <!-- END: Azure UI Style -->
  <!-- BEGIN: Page Style -->
  <!-- END: Page Style -->
  <!-- BEGIN: Minified Page Style -->
  <link href="../../../Static/CSS/a05a0e4e99489d7b3cb548e24414e01adf779d77.css" rel="stylesheet"/>
  <!-- END: Minified Page Style -->
  <link href="../../../StaticService/css/service.min.css" rel="stylesheet"/>
 </head>
 <body class="zh-cn">
  <script>
   window.requireUrlArgs = "2019/6/24 6:51:27";
    window.currentLocale = "zh-CN";
    window.headerTimestamp = "2019/1/23 8:25:24";
    window.footerTimestamp = "2019/1/8 8:07:06";
    window.locFileTimestamp = "2018/11/29 7:49:17";

    window.AcnHeaderFooter = {
        IsEnableQrCode: true,
        CurrentLang: window.currentLocale.toLowerCase(),
    };
  </script>
  <style>
   @media (min-width: 0) {
        .acn-header-placeholder {
            height: 48px;
            width: 100%;
        }
    }

    @media (min-width: 980px) {
        .acn-header-placeholder {
            height: 89px;
            width: 100%;
        }
    }

    .acn-header-placeholder {
        background-color: #1A1A1A;
    }

    .acn-header-service {
        position: absolute;
        top: 0;
        width: 100%;
    }
  </style>
  <div class="acn-header-container">
   <div class="acn-header-placeholder">
   </div>
   <div class="public_headerpage">
   </div>
  </div>
  <!-- BEGIN: Documentation Content -->
  <div class="content">
   <div class="container">
    <div class="row">
     <div class="col-md-12">
      <div class="bread-crumb hidden-sm hidden-xs">
       <ul>
        <li>
         <span>
         </span>
        </li>
       </ul>
      </div>
     </div>
    </div>
    <div class="single-page">
     <div class="row">
      <div class="col-md-2">
       <div class="documentation-navigation left-navigation hidden-xs hidden-sm">
        <div class="loader">
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
         <i class="circle">
         </i>
        </div>
       </div>
      </div>
      <div class="col-md-10 pure-content">
       <div class="select left-navigation-select hidden-md hidden-lg">
        <select>
         <option selected="selected">
          加载中...
         </option>
        </select>
        <span class="icon icon-arrow-top">
        </span>
       </div>
       <tags ms.date="09/30/2015" ms.service="virtual-desktop" wacn.date="11/27/2015">
       </tags>
       <style type="text/css">
        .pricing-detail-tab .tab-nav {
                            padding-left: 0 !important;
                            margin-top: 5px;
                            margin-bottom: 0;
                            overflow: hidden;
                        }

                        .pricing-detail-tab .tab-nav li {
                            list-style: none;
                            float: left;
                        }

                        .pricing-detail-tab .tab-nav li.active a {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-nav li.active a:hover {
                            border-bottom: 4px solid #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel {
                            display: none;
                        }

                        .pricing-detail-tab .tab-content .tab-panel.show-md {
                            display: block;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a {
                            padding-left: 5px;
                            padding-right: 5px;
                            color: #00a3d9;
                            background-color: #FFF;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li.active a {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pricing-detail-tab .tab-content .tab-panel .tab-nav li a:hover {
                            color: #FFF;
                            background-color: #00a3d9;
                        }

                        .pure-content .technical-azure-selector p a,
                        .pure-content .technical-azure-selector table a {
                            background: 0 0;
                            padding: 0;
                            margin: 0 6px;
                            height: 21px;
                            line-height: 22px;
                            font-size: 14px;
                            color: #00a3d9;
                            float: none;
                            display: inline;
                        }

                        .remove_list_style {
                            list-style: none;
                            overflow: hidden;
                            background-color: #f4f5f6;
                        }

                        .title_list {
                            display: inline-block;
                            float: left;
                            width: 50%;
                        }

                        .svg {
                            width: 50px;
                            float: left;
                            margin-right: 10px;
                        }
       </style>
       <div class="hide-info" style="display:none;">
        <div class="bg-box">
         <div class="cover-bg">
         </div>
        </div>
        <div class="msg-box">
         <div class="pricing-unavailable-message">
          所选区域不可用
         </div>
        </div>
       </div>
       <div class="common-banner col-top-banner" data-config="{'backgroundColor':'#e3f4ff', 'backgroundImage':'/Images/marketing-resource/css/storage.png','imageHeight':'auto'}">
        <div class="common-banner-image">
         <div class="common-banner-title">
          <img src="/Images/marketing-resource/media/images/production/virtual-deskdop.png"/>
          <h2>
           Azure 虚拟桌面定价
          </h2>
          <h3>
           访问 Azure 上提供的安全远程虚拟桌面和应用体验
          </h3>
         </div>
        </div>
       </div>
       <div class="pricing-page-section">
        <ul class="remove_list_style">
         <li class="title_list">
          <h2>
           访问 Azure 上提供的安全远程虚拟桌面和应用体验
          </h2>
         </li>
         <li class="title_list">
          <h3>
           Azure 虚拟桌面定价包括两个部分：
          </h3>
          <ol>
           <li>
            用户访问权限：
            <ul>
             <li>
              许可证权利：如果拥有符合条件的 Windows、Microsoft 365 或 Microsoft 远程桌面服务 (RDS) 客户端访问许可证
                                                (CAL)，则无需支付额外费用。
             </li>
            </ul>
            <ul>
             <li>
              每用户访问定价：我们推出了每用户每月定价的新选项，仅供外部用户访问 Azure 虚拟桌面。
             </li>
            </ul>
           </li>
           <p>
            详情见下文。
           </p>
           <li>
            Azure 基础结构成本
            <p>
             除用户访问权限以外，你还需使用 Azure 帐户来部署和管理虚拟化环境。这些是托管 Azure 虚拟桌面部署通常所需的 Azure 组件。
            </p>
            <ul>
             <li>
              虚拟机
             </li>
             <li>
              存储
             </li>
             <ul>
              <li>
               操作系统 (OS) 存储
              </li>
              <li>
               数据磁盘（仅限个人桌面）
              </li>
              <li>
               用户配置文件存储
              </li>
             </ul>
             <li>
              联网
             </li>
            </ul>
            <p>
             Azure 虚拟桌面虚拟机 (VM)（包括 Azure 上的 Citrix 云和 VMWare Horizon 云）按 Linux 计算费率收费，适用于 Windows 10 单会话、Windows
                                            10 多会话和 Windows Server。若要优化基础结构成本，可以利用
             <a href="https://azure.microsoft.com/zh-cn/pricing/reserved-vm-instances/">
              一年或三年期 Azure 虚拟机预留实例
             </a>
             ，这样可以比即用即付定价节省高达
                                            72% 的费用。
            </p>
            <p>
             <a href="https://docs.azure.cn/zh-cn/virtual-desktop/overview">
              阅读虚拟桌面文档，了解如何向部署应用许可证。
             </a>
            </p>
           </li>
          </ol>
         </li>
        </ul>
        <div class="pricing-page-section">
         <h2>
          定价概述
         </h2>
        </div>
        <!-- BEGIN: TAB-CONTROL -->
        <div class="technical-azure-selector pricing-detail-tab tab-dropdown">
         <div class="tab-container-container">
          <div class="tab-container-box">
           <div class="tab-container">
            <div class="dropdown-container software-kind-container" style="display:none;">
             <label>
              OS/软件:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               Azure Virtual
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
               <li class="active">
                <a data-href="#tabContent1" href="javascript:void(0)" id="Azure Virtual">
                 Azure Virtual
                </a>
               </li>
              </ol>
             </div>
             <select class="dropdown-select software-box hidden-lg hidden-md" id="software-box">
              <option data-href="#tabContent1" selected="selected" value="Azure Virtual">
               Az ure Virtual
              </option>
             </select>
            </div>
            <div class="dropdown-container region-container">
             <label>
              地区:
             </label>
             <div class="dropdown-box os-tab-nav hidden-sm hidden-xs">
              <span class="selected-item">
               中国东部 2
              </span>
              <i class="icon">
              </i>
              <ol class="tab-items">
               <li class="active">
                <a data-href="#east-china2" href="javascript:void(0)" id="east-china2">
                 中国东部 2
                </a>
               </li>
               <li>
                <a data-href="#north-china2" href="javascript:void(0)" id="north-china2">
                 中国北部 2
                </a>
               </li>
               <!--                                                    <li><a href="javascript:void(0)" data-href="#east-china"-->
               <!--                                                           id="east-china">中国东部</a></li>-->
               <!--                                                    <li><a href="javascript:void(0)" data-href="#north-china"-->
               <!--                                                           id="north-china">中国北部</a></li>-->
              </ol>
             </div>
             <select class="dropdown-select region-box hidden-lg hidden-md" id="region-box">
              <option data-href="#east-china2" selected="selected" value="east-china2">
               中国东部 2
              </option>
              <option data-href="#north-china2" value="north-china2">
               中国北部 2
              </option>
              <!--                                                <option data-href="#east-china" value="east-china">中国东部</option>-->
              <!--                                                <option data-href="#north-china" value="north-china">中国北部</option>-->
             </select>
            </div>
            <div class="clearfix">
            </div>
           </div>
          </div>
         </div>
         <!-- BEGIN: TAB-CONTAINER-1 -->
         <div class="tab-content">
          <!-- BEGIN: TAB-CONTENT-1 -->
          <div class="tab-panel" id="tabContent1">
           <!-- BEGIN: Tab level 2 navigator 2 -->
           <!-- BEGIN: Tab level 2 content 3 -->
           <ul class="tab-nav" style="display:none">
            <li class="active">
             <a data-href="#tabContent2" data-toggle="tab" href="javascript:void(0)" id="gpv1">
              常规用途 v1
             </a>
            </li>
           </ul>
           <div class="tab-content">
            <!-- BEGIN: Table1-Content-->
            <div class="tab-panel" id="tabContent2">
             <!-- <h2 style="font-size: 24px;">面向外部用户的远程应用流式传输的新定价选项</h2>
                                            <p>我们最近<a href="https://azure.microsoft.com/zh-cn/blog/azure-virtual-desktop-the-desktop-and-app-virtualization-platform-for-the-hybrid-workplace/">已公布</a>一个新的每用户每月访问定价选项，供组织使用 Azure 虚拟桌面将应用从云交付给外部（非员工）用户。例如，这将使软件供应商能够将其应用作为可供客户访问的 SaaS 解决方案交付。除 Azure 虚拟桌面的每月用户价格外，组织还根据使用情况为 Azure 基础结构服务付费<a href="https://azure.microsoft.com/zh-cn/services/virtual-desktop/">单击此处，详细了解如何开始使用远程应用流式传输。</a></p>
                                            <br>
                                            <h2 style="font-size: 24px;">促销期间立即试用吧</h2>
                                            <p>我们很高兴推出一项特别促销 - 对于访问 Azure 虚拟桌面以将应用程序流式传输给外部用户，不收取任何费用。此促销的有效期为 2021 年 7 月 14 日至 2021 年 12 月 31 日。有关其他定价详细信息，请参阅<a href="https://docs.microsoft.com/zh-cn/azure/virtual-desktop/overview">产品文档页面和下面的常见问题解答。</a></p>
                                            <br> -->
             <h2 style="font-size: 24px;">
              虚拟桌面
             </h2>
             <table cellpadding="0" cellspacing="0" id="Azure_Virtual_Desktop1" width="100%">
              <tr>
               <th align="left">
                <strong>
                 类型
                </strong>
               </th>
               <th align="left">
                <strong>
                 描述
                </strong>
               </th>
               <th align="left">
                <strong>
                 合格/要求
                </strong>
               </th>
              </tr>
              <tr>
               <td align="center" colspan="3">
                <strong>
                 用户访问权限
                </strong>
               </td>
              </tr>
              <tr>
               <td>
                BYOL for Windows 10/ Windows 7
               </td>
               <td>
                如果拥有符合条件的 Windows 或 Microsoft 365 许可证，则可以访问 Windows 10 企业版以及 Windows 7
                                                        企业版桌面，无需支付额外费用。
               </td>
               <td>
                <ul>
                 <li>
                  如果具有下列其中一个
                  <strong>
                   每用户
                  </strong>
                  许可证*，则你符合使用 Azure 虚拟桌面访问 Windows 10 和 Windows 7
                                                                的条件：
                  <br/>
                  Microsoft 365 E3/E5
                 </li>
                 <li>
                  Microsoft 365 A3/A5/学生使用权益
                 </li>
                 <li>
                  Microsoft 365 F3
                 </li>
                 <li>
                  Microsoft 365 商业高级版**
                 </li>
                 <li>
                  Windows 10 企业版 E3/E5
                 </li>
                 <li>
                  Windows 10 教育版 A3/A5
                 </li>
                 <li>
                  基于用户的 Windows 10 VDA
                 </li>
                </ul>
               </td>
              </tr>
              <tr>
               <td>
                BYOL for Windows Server
               </td>
               <td>
                如果你是符合条件的 Microsoft 远程桌面服务 (RDS) 客户端访问许可证 (CAL) 客户，则可访问由 Windows Server
                                                        远程桌面服务桌面支持的桌面，无需支付额外费用。
               </td>
               <td>
                如果拥有具有有效软件保障 (SA) 的每用户或每设备 RDS CAL 许可证，则你符合访问 Windows Server 2012 R2
                                                        以及更新的桌面的条件。
               </td>
              </tr>
              <tr>
               <td>
                外部用户的每用户访问定价
               </td>
               <td>
                仅限外部用户按每月价格访问 Windows 10/Windows 7 桌面。
               </td>
               <td>
                支付每用户每月费用以供外部用户访问 Azure 虚拟桌面
                <ul>
                 <li>
                  桌面 + 应用 ￥101.76
                 </li>
                 <li>
                  应用 ￥55.9686
                 </li>
                </ul>
               </td>
              </tr>
              <tr>
               <td align="center" colspan="3">
                <strong>
                 Azure 基础结构
                </strong>
               </td>
              </tr>
              <tr>
               <td>
                即用即付或预留实例
               </td>
               <td>
                部署 Azure 虚拟桌面需要 Azure 基础结构
               </td>
               <td>
                <ul>
                 <li>
                  虚拟机
                 </li>
                 <li>
                  存储
                  <ul>
                   <li>
                    操作系统 (OS) 存储
                   </li>
                   <li>
                    数据磁盘（仅限个人桌面）
                   </li>
                   <li style="padding: 0;">
                    用户配置文件存储
                   </li>
                  </ul>
                 </li>
                 <li>
                  联网
                 </li>
                </ul>
               </td>
              </tr>
             </table>
             <h2 style="font-size: 24px;">
              远程应用流式传输
             </h2>
             <table cellpadding="0" cellspacing="0" id="Azure_Virtual_Desktop2" width="100%">
              <tr>
               <th align="left">
                <strong>
                 类型
                </strong>
               </th>
               <th align="left">
                <strong>
                 描述
                </strong>
               </th>
               <th align="left">
                <strong>
                 合格/要求
                </strong>
               </th>
              </tr>
              <tr>
               <td align="center" colspan="3">
                <strong>
                 用户访问权限
                </strong>
               </td>
              </tr>
              <tr>
               <td>
                BYOL for Windows 10/ Windows 7
               </td>
               <td>
                如果拥有符合条件的 Windows 或 Microsoft 365 许可证，则可以访问 Windows 10 企业版以及 Windows 7
                                                        企业版桌面，无需支付额外费用。
               </td>
               <td>
                <ul>
                 <li>
                  如果具有下列其中一个
                  <strong>
                   每用户
                  </strong>
                  许可证*，则你符合使用 Azure 虚拟桌面访问 Windows 10 和 Windows 7
                                                                的条件：
                  <br/>
                  Microsoft 365 E3/E5
                 </li>
                 <li>
                  Microsoft 365 A3/A5/学生使用权益
                 </li>
                 <li>
                  Microsoft 365 F3
                 </li>
                 <li>
                  Microsoft 365 商业高级版**
                 </li>
                 <li>
                  Windows 10 企业版 E3/E5
                 </li>
                 <li>
                  Windows 10 教育版 A3/A5
                 </li>
                 <li>
                  基于用户的 Windows 10 VDA
                 </li>
                </ul>
               </td>
              </tr>
              <tr>
               <td>
                BYOL for Windows Server
               </td>
               <td>
                如果你是符合条件的 Microsoft 远程桌面服务 (RDS) 客户端访问许可证 (CAL) 客户，则可访问由 Windows Server
                                                        远程桌面服务桌面支持的桌面，无需支付额外费用。
               </td>
               <td>
                如果拥有具有有效软件保障 (SA) 的每用户或每设备 RDS CAL 许可证，则你符合访问 Windows Server 2012 R2
                                                        以及更新的桌面的条件。
               </td>
              </tr>
              <tr>
               <td>
                外部用户的每用户访问定价
               </td>
               <td>
                仅限外部用户按每月价格访问 Windows 10/Windows 7 桌面。
               </td>
               <td>
                支付每用户每月费用以供外部用户访问 Azure 虚拟桌面
                <ul>
                 <li>
                  桌面 + 应用 ￥101.76
                 </li>
                 <li>
                  应用 ￥55.9686
                 </li>
                </ul>
               </td>
              </tr>
              <tr>
               <td align="center" colspan="3">
                <strong>
                 Azure 基础结构
                </strong>
               </td>
              </tr>
              <tr>
               <td>
                即用即付或预留实例
               </td>
               <td>
                部署 Azure 虚拟桌面需要 Azure 基础结构
               </td>
               <td>
                <ul>
                 <li>
                  虚拟机
                 </li>
                 <li>
                  存储
                  <ul>
                   <li>
                    操作系统 (OS) 存储
                   </li>
                   <li>
                    数据磁盘（仅限个人桌面）
                   </li>
                   <li style="padding: 0;">
                    用户配置文件存储
                   </li>
                  </ul>
                 </li>
                 <li>
                  联网
                 </li>
                </ul>
               </td>
              </tr>
             </table>
             <!-- <p>Learn more on the <a href="#" style="margin: 0; padding: 0;">Azure pricing calculator</a>. Examples listed below to help you get started.</p> -->
             <!-- END: Table1-Content
                                            <div class="tags-date">
                                                <div class="ms-date">* Customers can access Azure Virtual Desktop from their non-Windows Pro endpoints if they have a Microsoft 365 E3/E5/F3/Business/ A3/A5/Student Use Benefits or Windows 10 VDA per user license.</div>
                                                <div class="ms-date">** Microsoft 365 商业高级版在 2020 年 4 月 21 日之前的名称是 Microsoft 365 商业版。</div><br>
                                            </div> -->
             <br/>
             <h2 style="font-size: 24px;">
              如何估算 Azure 基础结构成本
             </h2>
             <!-- <p>Below you will find examples of how to choose the right components for compute, storage and networking to help estimate pricing. To optimize infrastructure costs, you can take advantage of <a style="margin: 0; padding: 0;" href="https://azure.microsoft.com/zh-cn/pricing/reserved-vm-instances/">one-year or three-year Azure Reserved Virtual Machine Instances</a>, which can save you up to 72 percent versus pay-as-you-go pricing. Reserved Virtual Machine Instances are flexible and can easily be exchanged or returned.</p> -->
             <!-- <p>If you require Windows 7 virtual desktops, we also offer free Extended Security Updates until January 2023—to support legacy apps while you transition to Windows 10.</p> -->
             <p>
              您可以在
              <a href="https://www.azure.cn/pricing/calculator/">
               价格计算器
              </a>
              上通过选择您需要的虚机，存储和网络的类型来估算使用Windows虚拟桌面的总体价格。如果你需要使用
                                                Windows 7 虚拟桌面，则在 2023 年 1 月之前我们还会提供免费的扩展安全更新，以便在你过渡到 Windows 10 时为旧版应用提供支持。
             </p>
             <br/>
             <h2 style="font-size: 24px;">
              单会话（个人桌面）示例方案
             </h2>
             <table cellpadding="0" cellspacing="0" id="Azure_Virtual_Desktop3" width="100%">
              <tr>
               <th align="left">
                <strong>
                 示例工作负荷
                </strong>
               </th>
               <th align="left">
                <strong>
                 方案中的用户数
                </strong>
               </th>
               <th align="left">
                <strong>
                 用户类型
                </strong>
               </th>
               <th align="left">
                <strong>
                 vCPU
                </strong>
               </th>
               <th align="left">
                <strong>
                 RAM
                </strong>
               </th>
              </tr>
              <tr>
               <td>
                图形工作站
               </td>
               <td>
                100
               </td>
               <td>
                具有 3D 建模、模拟和 CAD 工作负荷的工程师和图形设计人员。用户每天有 5-6 小时时间需要工作站功能。
               </td>
               <td>
                12
               </td>
               <td>
                112 GB
               </td>
              </tr>
              <tr>
               <td>
                Microsoft Office
               </td>
               <td>
                1000
               </td>
               <td>
                需要使用 Microsoft Office 产品的标准知识工作者。用户每天工作 8-10 小时。
               </td>
               <td>
                2
               </td>
               <td>
                4 GB
               </td>
              </tr>
             </table>
             <br/>
             <h2 style="font-size: 24px;">
              多会话桌面示例方案
             </h2>
             <p>
              多个用户共享共用（非持久）虚拟桌面的的示例用例
             </p>
             <table cellpadding="0" cellspacing="0" id="Azure_Virtual_Desktop4" width="100%">
              <tr>
               <th align="left">
                <strong>
                 示例工作负荷
                </strong>
               </th>
               <th align="left">
                <strong>
                 方案中的用户数
                </strong>
               </th>
               <th align="left">
                <strong>
                 用户类型
                </strong>
               </th>
               <th align="left">
                <strong>
                 用户密度
                </strong>
               </th>
              </tr>
              <tr>
               <td>
                Microsoft Office
               </td>
               <td>
                1000
               </td>
               <td>
                需要使用 Microsoft Office 产品的标准知识工作者。使用 24/7 预留实例便无需再对虚拟机进行管理。
               </td>
               <td>
                2 每 vCPU
               </td>
              </tr>
              <tr>
               <td>
                呼叫中心/数据输入
               </td>
               <td>
                1000
               </td>
               <td>
                工作负荷强度低、主要参与数据输入的呼叫中心用户。用户以 8
                                                        小时为一个时间段（共三个）轮换操作，这样，24/7（全年无休）制的预留实例便成为最具成本效益的选择。
               </td>
               <td>
                6 每 vCPU
               </td>
              </tr>
             </table>
            </div>
           </div>
          </div>
          <!-- END: TAB-CONTENT-3 -->
         </div>
         <!-- END: TAB-CONTAINER-1 -->
        </div>
        <!-- END: TAB-CONTROL -->
        <div class="pricing-page-section">
         <div class="more-detail">
          <h2>
           常见问题
          </h2>
          <em>
           全部展开
          </em>
          <ul>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question14">
              我应使用个人桌面还是多会话桌面？我的用户需要什么级别的性能？
             </a>
             <section>
              <p>
               对于大多数应用程序，多会话能够通过后列方式提供最大的灵活性和成本节约：共享桌面基础结构、通过在共用环境中操作来保留 OS
                                                    映像的状态、允许使用 FSLogix 保存和访问用户数据和设置。
              </p>
              <p>
               单会话（个人桌面）通常适用于有后列需求的用户：(a) 需要相应的管理权限来修改操作系统并希望在 VM 重启时保留这些更改，以及 (b)
                                                    需要运行与多会话不兼容的应用程序。
              </p>
              <p>
               若要确定用户密度和了解 VM 规范，请参阅
               <a href="https://docs.microsoft.com/zh-cn/windows-server/remote/remote-desktop-services/remote-desktop-workloads" style="margin: 0; padding: 0;">
                远程桌面工作负荷指南。
               </a>
               请注意，你的需求会发生改变，设置虚拟机后，应持续监视其实际使用情况并相应调整其规模。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question15">
              用户数和并发率会如何影响我的部署？
             </a>
             <section>
              <p>
               对于多会话方案，用户并发量会影响需要为工作负荷提供的 VM 总数。这是指同时登录到 VM 的用户量所占比例。例如，一个三期轮换的全年无休呼叫中心的使用并发率是
                                                    33%，那么为支持这些用户而需准备的 VM 数量就比他们所有人同时操作时所需要的 VM 数量少。对于大多数采用标准运营时间的业务，最开始可以按
                                                    0.9 的并发率来设置。根据并发率、VM 类型和预期的用户密度，如果 VM
                                                    未得到完全利用，对于用户较少的部署，每个用户的成本可能会有所增加。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question16">
              什么是应用程序流式传输？
             </a>
             <section>
              <p>
               可以通过应用程序流式传输在 Azure
                                                    中运行应用并将其流式传输到远程设备。这使得应用可以在邻近其他应用和数据的云中运行，从而从几乎任意位置、在任何设备上创建低延迟/高性能的用户体验。
              </p>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question17">
              哪些人群有资格获得应用程序流式传输的每用户每月定价？
             </a>
             <section>
              <p>
               任何想要将应用程序流式传输给外部用户（非员工）的组织。而员工应处于符合条件的 Windows/M365/Windows Server
                                                    许可证的涵盖范围内。
              </p>
              <ol>
               <li>
                <p>
                 应用程序流式传输服务的价格是多少？
                </p>
                <p>
                 请阅读
                 <a href="https://docs.microsoft.com/zh-cn/azure/virtual-desktop/overview" style="margin: 0; padding: 0;">
                  产品文档页面。
                 </a>
                </p>
               </li>
               <li>
                <p>
                 可为每个用户流式传输的应用数是否存在限制？
                </p>
                <p>
                 可以按每月价格为特定用户流式传输任意数量的应用。
                </p>
               </li>
               <li>
                <p>
                 如何为应用程序流式传输付费？
                </p>
                <p>
                 在促销期间，无需为外部用户对你应用的访问付费。促销期结束后，系统将按月自动向你收取每位用户的费用。
                </p>
               </li>
               <li>
                <p>
                 应用程序流式传输促销如何工作？
                </p>
                <p>
                 在促销期间，无需为外部用户对你应用的访问付费。促销期结束后，系统将按月自动向你收取每位用户的费用。
                </p>
               </li>
              </ol>
             </section>
            </div>
           </li>
           <li>
            <i class="icon icon-plus">
            </i>
            <div>
             <a id="Storage_question18">
              访问 FSLogix 技术的合格许可证是什么？
             </a>
             <section>
              <p>
               如果具有以下许可证之一，则有资格访问 FSLogix 配置文件容器、Office 365 容器、应用程序屏蔽和 Java 重定向工具：
              </p>
              <ol>
               <li>
                Microsoft 365 E3/E5
               </li>
               <li>
                Microsoft 365 A3/A5/学生使用权益
               </li>
               <li>
                Microsoft 365 F3
               </li>
               <li>
                Microsoft 365 商业高级版**
               </li>
               <li>
                Windows 10 企业版 E3/E5
               </li>
               <li>
                Windows 10 教育版 A3/A5
               </li>
               <li>
                基于用户的 Windows 10 VDA
               </li>
               <li>
                远程桌面服务 (RDS) 客户端访问许可证 (CAL)
               </li>
               <li>
                远程桌面服务 (RDS) 订阅者访问许可证 (SAL)
               </li>
              </ol>
             </section>
            </div>
           </li>
          </ul>
         </div>
        </div>
        <div class="pricing-page-section">
        </div>
       </div>
      </div>
     </div>
    </div>
   </div>
   <!-- END: Documentation Content -->
   <!-- BEGIN: Footer -->
   <div class="public_footerpage">
   </div>
   <!--END: Common sidebar-->
   <link href="../../../Static/CSS/Localization/zh-cn.css" rel="stylesheet"/>
   <script src="/Static/Scripts/lib/jquery-1.12.3.min.js" type="text/javascript">
   </script>
   <script type="text/javascript">
    function getAntiForgeryToken() {
            var token =
                '<input name="__RequestVerificationToken" type="hidden" value="pp_xxKDshbtueTQmfHeAW98thEu6tY1pJ2-jULR15hLqSMGIANieFFwvAOlk-XxiH_alcAFK99NAwcIECWmXpVR8DnvNjMuxo3SsmH4iPvA1" />';
            token = $(token).val();
            return token;
        }

        function setLocaleCookie(localeVal) {
            var Days = 365 * 10; // Ten year expiration
            var exp = new Date();

            var hostName = window.location.hostname;
            var secondLevelDomain = hostName.substring(hostName.lastIndexOf(".", hostName.lastIndexOf(".") - 1));

            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = "acn_selected_locale=" + localeVal + ";domain=" + secondLevelDomain + "; path=/;expires=" + exp
                .toGMTString();
        }

        setLocaleCookie(window.currentLocale);
   </script>
   <script type="text/javascript">
    var MARKETING_STORAGE = '/blob/marketing-resource/Content';
        var TECHNICAL_STORAGE = '/blob/tech-content/';
        var EXTERNAL_RESOURCE_STORAGE = '/blob/external-resource/';
        var EnablePricingSync = 'false';
   </script>
   <!-- BEGIN: Minified RequireJs -->
   <script src="/Static/Scripts/global.config.js" type="text/javascript">
   </script>
   <script src="/Static/Scripts/require.js" type="text/javascript">
   </script>
   <script src="/Static/Scripts/pricing-page-detail.js" type="text/javascript">
   </script>
   <!--    <script src='../../../../Static/Scripts/4552a1505e111da8c71780b349b683700a6158fa.js' type='text/javascript'></script>-->
   <!-- END: Minified RequireJs -->
   <!-- begin JSLL -->
   <script src="https://az725175.vo.msecnd.net/scripts/jsll-4.js">
   </script>
   <script type="text/javascript">
    (function () {
            var config = {
                cookiesToCollect: ["_mkto_trk"],
                syncMuid: true,
                ix: {
                    a: true,
                    g: true
                },
                coreData: {
                    appId: 'AzureCN',
                    market: 'zh-cn',
                }
            };
            awa.init(config);
        })();
   </script>
   <!-- end JSLL -->
   <script src="../../../Static/Scripts/plugins/cookie/jquery.cookie.js" type="text/javascript">
   </script>
   <script src="../../../Static/Scripts/wacndatatracker.js" type="text/javascript">
   </script>
   <script src="/common/useCommon.js" type="text/javascript">
   </script>
  </div>
 </body>
</html>
