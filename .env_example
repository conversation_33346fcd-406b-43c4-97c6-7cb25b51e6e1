# =============================================================================
# AzureCNArchaeologist 环境变量配置文件
# =============================================================================
# 复制此文件为 .env 并根据你的环境修改相应的配置值

# =============================================================================
# 文件路径配置 - 核心路径设置
# =============================================================================

# HTML源文件基础目录 - 存放从Azure官网下载的HTML文件
# 开发环境示例：test-data/html
# 生产环境示例：/var/data/azure-html
HTML_BASE_DIR=data/prod-html

# 输出文件基础目录 - 生成的JSON文件存放位置  
# 开发环境示例：test-output
# 生产环境示例：/var/output/cms-data
OUTPUT_BASE_DIR=output

# 配置文件基础目录 - 产品配置和索引文件位置
CONFIG_BASE_DIR=data/configs

# =============================================================================
# 批处理系统配置 - 控制批量处理行为
# =============================================================================

# 默认并行处理任务数 - 推荐值：2-8，根据机器性能调整
DEFAULT_PARALLEL_JOBS=4

# 默认语言版本 - 支持: zh-cn, en-us, both
DEFAULT_LANGUAGE=zh-cn

# 批处理记录数据库文件路径 - SQLite数据库位置
# 生产环境示例：/var/data/batch_records.db
BATCH_DB_PATH=data/batch_records.db

# 默认最大重试次数 - 处理失败时的重试次数
DEFAULT_MAX_RETRIES=3

# =============================================================================
# 输出格式配置 - 控制导出行为
# =============================================================================

# 默认输出格式 - 支持: json, html, rag, flexible
DEFAULT_OUTPUT_FORMAT=flexible

# 启用数据验证 - 是否对提取结果进行质量验证
ENABLE_VALIDATION=true

# =============================================================================
# 日志系统配置 - 控制日志行为
# =============================================================================

# 日志级别 - 支持: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# =============================================================================
# Azure Storage Account配置 - 云存储集成（可选）
# =============================================================================

# Azure存储连接字符串 - 请替换为实际的连接字符串
# 格式：DefaultEndpointsProtocol=https;AccountName=your_account;AccountKey=your_key;EndpointSuffix=core.windows.net
AZURE_STORAGE_CONNECTION_STRING=

# Blob容器名称 - 用于存储输出文件的容器
AZURE_BLOB_CONTAINER_NAME=cms-output

# =============================================================================
# 部署环境示例配置
# =============================================================================

# 开发环境配置示例：
# HTML_BASE_DIR=test-data/html
# OUTPUT_BASE_DIR=test-output  
# DEFAULT_PARALLEL_JOBS=2
# LOG_LEVEL=DEBUG

# 生产环境配置示例：
# HTML_BASE_DIR=/var/data/azure-html
# OUTPUT_BASE_DIR=/var/output/cms-data
# DEFAULT_PARALLEL_JOBS=8
# LOG_LEVEL=INFO
# BATCH_DB_PATH=/var/data/batch_records.db

# Docker环境配置示例：
# HTML_BASE_DIR=/app/data/html
# OUTPUT_BASE_DIR=/app/output
# CONFIG_BASE_DIR=/app/config
# BATCH_DB_PATH=/app/data/batch.db