# Debug批量处理测试指南

本文档介绍如何使用debug脚本来测试src/batch模块的批量处理功能。

## 📁 文件说明

### 1. `debug_batch_processing.py` - 批量处理调试器
用于测试按category进行产品HTML提取的批量处理功能。

### 2. `debug_single_product.py` - 单产品调试器  
用于测试单个产品的提取过程，便于快速调试和验证提取逻辑。

### 3. `DEBUG_BATCH_TESTING.md` - 本文档
使用指南和测试流程说明。

## 🚀 快速开始

### 环境准备
```bash
# 确保在项目根目录
cd /path/to/AzureCNArchaeologist

# 确保Python环境已激活
# 如果使用uv: uv sync
# 如果使用pip: pip install -r requirements.txt
```

### 基本测试流程

#### 1. 查看可用的产品类别
```bash
python debug_batch_processing.py list-categories
```

#### 2. 检查特定类别的HTML文件
```bash
# 检查database类别的中文HTML文件
python debug_batch_processing.py check-files database

# 检查database类别的英文HTML文件  
python debug_batch_processing.py check-files database --language en-us
```

#### 3. 测试单个类别的批量处理（试运行）
```bash
# 试运行模式，不执行实际处理
python debug_batch_processing.py test-category database --dry-run
```

#### 4. 测试单个类别的批量处理（实际执行）
```bash
# 限制处理3个产品进行测试
python debug_batch_processing.py test-category database --language zh-cn --max-products 3

# 处理整个类别
python debug_batch_processing.py test-category database --language zh-cn
```

#### 5. 测试所有类别的批量处理
```bash
# 试运行模式
python debug_batch_processing.py test-all-categories --dry-run

# 实际执行（谨慎使用）
python debug_batch_processing.py test-all-categories --language zh-cn
```

## 🔍 单产品调试

### 查看类别中的产品
```bash
# 查看database类别中的所有产品
python debug_single_product.py list-products database
```

### 测试单个产品提取
```bash
# 试运行模式
python debug_single_product.py test mysql --dry-run

# 实际提取
python debug_single_product.py test mysql --language zh-cn
```

### 测试特定策略
```bash
# 测试mysql产品使用complex策略
python debug_single_product.py test-strategy mysql --strategy complex

# 测试mysql产品使用simple策略
python debug_single_product.py test-strategy mysql --strategy simple
```

## 📊 输出说明

### 批量处理输出
- **成功率统计**: 显示处理成功/失败的产品数量和百分比
- **处理时间**: 每个产品的处理时间和总体耗时
- **策略使用情况**: 显示各种策略的使用次数
- **错误信息**: 详细的失败原因和错误消息
- **输出文件**: 生成的JSON文件路径

### 单产品调试输出
- **配置检查**: 产品配置是否正确加载
- **文件检查**: HTML文件是否存在及大小
- **策略分析**: 推荐的提取策略和置信度
- **提取统计**: 内容组数量、筛选器、标签页等统计信息
- **导出结果**: 生成的JSON文件位置

## 🗂️ 输出目录结构

```
debug_output/
├── zh-cn/                    # 中文版本输出
│   ├── database/            # 按类别组织
│   │   ├── mysql_flexible_content_*.json
│   │   └── postgresql_flexible_content_*.json
│   └── strategy_test/       # 策略测试输出
│       ├── mysql_complex_flexible_content_*.json
│       └── mysql_simple_flexible_content_*.json
└── en-us/                   # 英文版本输出
    └── ...
```

## 🛠️ 常见问题排查

### 1. 产品配置不存在
```
❌ 产品配置不存在: product-name
```
**解决方案**: 检查`data/configs/products-index.json`中是否包含该产品。

### 2. HTML文件不存在
```
❌ HTML文件不存在: data/prod-html/zh-cn/database/mysql.html
```
**解决方案**: 
- 检查文件路径是否正确
- 运行`python cli.py copy-from-prod`复制HTML文件

### 3. 策略分析失败
```
❌ 策略分析失败: ...
```
**解决方案**: 
- 检查HTML文件是否损坏
- 查看详细错误日志

### 4. 提取过程出错
```
❌ 内容提取失败: ...
```
**解决方案**:
- 使用`--dry-run`模式先检查配置
- 查看日志文件获取详细错误信息
- 尝试不同的策略

## 📝 日志文件

调试过程中的详细日志会保存在：
- `logs/app.log` - 应用程序日志
- `logs/user_operations.log` - 用户操作日志

## 🎯 推荐测试顺序

1. **环境验证**
   ```bash
   python debug_batch_processing.py list-categories
   ```

2. **文件检查**
   ```bash
   python debug_batch_processing.py check-files database
   ```

3. **单产品测试**
   ```bash
   python debug_single_product.py test mysql --dry-run
   python debug_single_product.py test mysql
   ```

4. **小规模批量测试**
   ```bash
   python debug_batch_processing.py test-category database --max-products 2
   ```

5. **完整类别测试**
   ```bash
   python debug_batch_processing.py test-category database
   ```

6. **策略对比测试**
   ```bash
   python debug_single_product.py test-strategy mysql --strategy simple
   python debug_single_product.py test-strategy mysql --strategy complex
   ```

## ⚠️ 注意事项

1. **试运行模式**: 首次测试建议使用`--dry-run`参数
2. **限制产品数量**: 使用`--max-products`参数限制处理数量
3. **输出目录**: 确保有足够的磁盘空间存储输出文件
4. **并发处理**: 批量处理使用2个工作线程，避免系统过载
5. **错误处理**: 遇到错误时查看详细日志进行排查

## 🔧 高级用法

### 自定义输出目录
修改脚本中的`output_dir`参数：
```python
output_dir = f"custom_output/{language}"
```

### 调整并发数
修改`BatchProcessEngine`的`max_workers`参数：
```python
self.engine = BatchProcessEngine(max_workers=4)  # 增加到4个工作线程
```

### 添加自定义回调
```python
def custom_progress_callback(message, current, total):
    print(f"自定义进度: {current}/{total} - {message}")

debugger.engine.set_progress_callback(custom_progress_callback)
```

## 📞 获取帮助

如果遇到问题，可以：
1. 查看脚本的帮助信息：`python debug_batch_processing.py --help`
2. 检查日志文件获取详细错误信息
3. 使用试运行模式进行初步诊断
4. 逐步缩小测试范围定位问题
